@import url("/assets/common/css/tsApps.css");
/* Include in Editor: Start */
.header1 {   font-family: 'Montserrat', sans-serif;    line-height: 1.3em;    margin-bottom: 18px;    text-transform: uppercase; font-size: 70px;  font-weight: 700;}
.header2  {   font-family: 'Montserrat', sans-serif;    line-height: 1.3em;    margin-bottom: 18px;    text-transform: uppercase; font-size: 60px;  font-weight: 700;}
.header3 {   font-family: 'Montserrat', sans-serif;    line-height: 1.3em;    margin-bottom: 18px;    text-transform: uppercase; font-size: 40px;  font-weight: 700;}
.header4 {   font-family: 'Montserrat', sans-serif;    line-height: 1.3em;    margin-bottom: 18px;    text-transform: uppercase; font-size: 24px;  font-weight: 700;}
.header5 {   font-family: 'Montserrat', sans-serif;    line-height: 1.3em;    margin-bottom: 18px;    text-transform: uppercase; font-size: 22px;  font-weight: 400;}
.header6 {   font-family: 'Montserrat', sans-serif;    line-height: 1.3em;    margin-bottom: 18px;    text-transform: uppercase; font-size: 18px;  font-weight: 700;}
.InfoText { font-family: 'Montserrat', sans-serif; font-size:12px; color: #333333;}
.HeaderText {   font-family: 'Montserrat', sans-serif;    line-height: 1.3em;    margin-bottom: 18px;    font-size: 20px;  font-weight: 700;}
.MastheadBody {   font-family: 'Montserrat', sans-serif;    line-height: 1.3em;    margin-bottom: 18px;    font-size: 34px;  font-weight: 500;}
.MastheadBodyWhite {   font-family: 'Montserrat', sans-serif;    line-height: 1.3em;    margin-bottom: 18px;    font-size: 34px;  font-weight: 500; color: #FFFFFF;}
.MastheadTitle {   font-family: 'Montserrat', sans-serif;    line-height: 1.3em;    margin-bottom: 18px;    font-size: 34px;  font-weight: 900;}
.MastheadTitleWhite {   font-family: 'Montserrat', sans-serif;    line-height: 1.3em;    margin-bottom: 18px;    font-size: 34px;  font-weight: 900; color: #FFFFFF;}
.SectionTitleTextBold  { font-family: 'Montserrat', sans-serif;    line-height: 1.3em;    margin-bottom: 18px;    text-transform: uppercase; font-size: 24px;  font-weight: 600;  color: #18297d; }
.TitleText  {  font-family: 'Montserrat', sans-serif;    line-height: 1.3em;    margin-bottom: 18px;    font-size: 24px;  font-weight: 300;}
.TitleTextBold {  font-family: 'Montserrat', sans-serif;    line-height: 1.3em;    margin-bottom: 18px;    font-size: 24px;  font-weight: 700;  color: #18297d;;}
.TitleTextBoldDark {  font-family: 'Montserrat', sans-serif;    line-height: 1.3em;    margin-bottom: 18px;    font-size: 24px;  font-weight: 700;  color: #000000;}
.TitleTextSemiBold {  font-family: 'Montserrat', sans-serif;     line-height: 1.3em;    margin-bottom: 18px;    font-size: 24px;  font-weight: 400;  color: #000000;}
.TitleTextUpperCase {  font-family: 'Montserrat', sans-serif;    line-height: 1.3em;    margin-bottom: 18px;    font-size: 24px;  font-weight: 300;  text-transform: uppercase;}
.TitleTextWhiteUpperCase {  font-family: 'Montserrat', sans-serif;    line-height: 1.3em;    margin-bottom: 18px;    font-size: 24px;  font-weight: 300;  text-transform: uppercase; color: #FFFFFF;}
/* Include in Editor: Stop */


/*! HTML5 Boilerplate v4.3.0 | MIT License | http://h5bp.com/ */
/*
 * What follows is the result of much research on cross-browser styling.
 * Credit left inline and big thanks to Nicolas Gallagher, Jonathan Neal,
 * Kroc Camen, and the H5BP dev community and team.
 */

/*---------------------------------------------------------------------------
[Master Stylesheet]

Project:        Knight
Version:        1
Last change:    Initial Release
Primary use:    MultiPurpose Template
----------------------------------------------------------------------------*/

/*---------------------------------------------------------------------------
[Table Of Contents]

1. Importing Web Fonts
2. Base Styles
   2.1. Headings Styles
   2.2. Preloader
3. Headers Styles
   3.1. Headers Components
   3.2. Header Top Bar
   3.3. Header Main Bar
   3.4. Header Bottom Bar
   3.5. Header Main Nav
   3.6. Headers Different Styles
   3.7. Offcanvas Menu & Mobile Navigation
   3.8. Offcanvas Menu Trigger
   3.9. Fixed Menu
   3.10. Header Banner
   3.11. Header Slider
   3.12. Header Tiltle Bar
   3.13. Mobile Header
   3.14. Sticky Header
4. Main Sidebar
5. Blog
6. Portfolio
7. Shop
8. Main Subscribe Form
9. 404 Page
10. Main Footer
11. Media Queries
----------------------------------------------------------------------------*/

/*************************************************************
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
..................... 1. BASE STYLES ......................
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
*************************************************************/
.icon-search::before{content: none !important;}
html,
button,
input,
select,
textarea {
  color: #222;
}
html {
  font-size: 1em;
  line-height: 1.4;
  height: 100%;
}
::-moz-selection {
  background: #18297d;
  color: white;
  text-shadow: none;
}
::selection {
  background: #ff4d4d;
  color: white;
  text-shadow: none;
}
hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid #ddd;
  margin: 1em 0;
  padding: 0;
}
audio,
canvas,
img,
video {
  vertical-align: middle;
}
fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}
textarea {
  resize: vertical;
}
.browsehappy {
  margin: 0.2em 0;
  background: #ccc;
  color: #000;
  padding: 0.2em 0;
}

body {
  font-family: 'Montserrat', sans-serif;
  font-weight: 300;
  font-size: 14px;
  line-height: 1.6em;
  color: #444444;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/*BizFunctional*/
.footer-copy{
    font-family: 'Droid Sans', sans-serif;
}
.general-copy{
    font-family: 'Droid Sans', sans-serif;
}
.register-box{
    border:solid 3px #c93827;
}
body.offcanvas-menu-dark {
  background: #222222;
}
body.offcanvas-menu-white {
  background: white;
}
body.fullscreen {
  height: 100%;
}
a:hover, a:focus {
    color: #18297d;
}
.main-wrapper {
  position: relative;
  background-color: white;
  -webkit-transition: 0.7s all 0.001s ease;
  -moz-transition: 0.7s all 0.001s ease;
  transition: 0.7s all 0.001s ease;
}
.shifter-enabled .main-wrapper:before{
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999999;
  display: inline-block;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.5s all 0.001s ease;
  -moz-transition: 0.5s all 0.001s ease;
  transition: 0.5s all 0.001s ease;
}
.shifter-enabled.shifter-open .main-wrapper:before{
  opacity: 1;
  visibility: visible;
}
.shifter-enabled.shifter-open .main-wrapper{
  cursor: pointer;
}
a {
  color: #444444;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
  text-decoration: none !important;
}
a a:hover,
a a:focus,
a a:active {
  text-decoration: none !important;
  outline: none !important;
}
p {
  margin-bottom: 23px;
}
img {
  max-width: 100%;
}
ul {
  margin-bottom: 23px;
}
table{
  width: 100%;
}
a:focus,
a:active,
input:focus,
input:active,
textarea:focus,
textarea:active,
select:focus,
select:active {
  outline: none;
}
select,
input[type=number] {
  background-color: #f9f9f9;
  border: 1px solid #eee;
  /*padding: 13px 11px 13px 18px;*/
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
select:focus,
input[type=number]:focus {
  border-color: #bbb;
  outline: none !important;
}
select:focus,
select:active,
input[type=number]:focus
input[type=number]:active {
  outline: none !important;
}
input[type=number],
.fs-number-element {
  width: 90px;
  padding: 13px 15px 13px 25px;
  border-radius: 0;
}
.fs-number {
  display: inline-block;
  margin: 0;
  width: 90px;
  border-radius: 0;
  vertical-align: top;
}
.fs-number-arrow {
  border: none;
  background: none;
  padding: 0;
  width: 27px;
  right: 8px;
  line-height: 0;
}
.fs-number-arrow.fs-number-up:after,
.fs-number-arrow.fs-number-down:after {
  font-family: 'knight';
  font-size: 2em;
  border: none;
  text-indent: -10px;
}
.fs-number-arrow.fs-number-up:after {
  content: '\e1e5';
  top: 8px;
}
.fs-number-arrow.fs-number-down:after {
  content: '\e17b';
  top: -5px;
}
.panel-group .panel-heading + .panel-collapse > .panel-body,
.panel-group .panel-heading + .panel-collapse > .list-group {
  border-top: none;
}

/*************************************************************
..................... 2.1. HEADINGS ......................
*************************************************************/
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'Montserrat', sans-serif;
  line-height: 1.3em;
  margin-bottom: 18px;
  text-transform: uppercase;
}
h1 {
  font-size: 70px;
  font-weight: 700;
}
h2 {
  font-size: 60px;
  font-weight: 700;
}
h3 {
  font-size: 40px;
  font-weight: 700;
}
h4 {
  font-size: 24px;
  font-weight: 600;
}
h5 {
  font-size: 24px;
  font-weight: 500;
}
h6 {
  font-size: 20px;
  font-weight: 700;
}
/*************************************************************
..................... 2.2. PRELOADER ......................
*************************************************************/
.preloader-container {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999999;
  display: inline-block;
  width: 100%;
  height: 100%;
  text-align: center;
}
.preloader-container .preloader-screen {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  -ms-transform: translateX(-50%) translateY(-50%);
  -webkit-transform: translateX(-50%) translateY(-50%);
}
.preloader-container .preloader-screen .logo-container {
  margin-bottom: 57px;
}
.preloader-container .preloader-screen .preloader-bar {
  display: inline-block;
  width: 400px;
  height: 18px;
  border: 1px solid #444444;
  margin-bottom: 23px;
  text-align: left;
}
.preloader-container .preloader-screen .preloader-bar .bar {
  display: inline-block;
  width: 0;
  height: 100%;
  background: #444444;
  -webkit-transform-origin: left center;
  -moz-transform-origin: left center;
  -ms-transform-origin: left center;
  transform-origin: left center;
}
.preloader-container .preloader-screen .preloader-percentage {
  font-family: 'Montserrat', sans-serif;
  font-weight: 400;
  text-transform: uppercase;
}
/*************************************************************
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
..................... 3. HEADERS ......................
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
*************************************************************/

/*************************************************************
................. 3.1. HEADERS COMPONENTS ..................
*************************************************************/
.main-header .main-header-inner {
  position: relative;
  z-index: 9999;
}
.main-header.overlay-header .main-header-inner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: block;
  width: 100%;
  z-index: 9999;
}
.small-logo{
    padding-top: 25px;
}
.main-header .logo-container .logo {
  margin: 0;
  padding: 0;
  height: auto;
  width: 100%;
}
.main-header .logo-container .logo img {
  margin: 0 auto;
}
.main-header .main-bar .logo-container.align-center {
  float: none !important;
  order: 0;
}
.sticky-menu .sticky-menu-container,
.main-header .menu-container {
  float: right;
  order: 1;
}
.sticky-menu .sticky-menu-container {
  float: right;
  order: 1;
}
.sticky-menu .menu-container > nav,
.sticky-menu .menu-container > ul,
.sticky-menu .menu-container > div,
.main-header .menu-container > nav,
.main-header .menu-container > ul,
.main-header .menu-container > div {
  float: left;
}
.main-header .top-bar,
.main-header .main-bar,
.main-header .bottom-bar {
  position: relative;
}
.main-header .top-bar {
  z-index: 3;
}
.main-header .main-bar {
  z-index: 2;
}
.main-header .bottom-bar {
  z-index: 1;
}
.main-header .left-sec {
  float: left;
}
.main-header .right-sec {
  float: right;
}
.main-header .left-sec ul,
.main-header .right-sec ul,
.main-header .left-sec form,
.main-header .right-sec form,
.main-header .left-sec nav,
.main-header .right-sec nav {
  float: left;
}
.main-header .top-bar.dark-bg + .main-bar.dark-bg {
  border-top: 1px solid #444444;
}
.main-header .main-bar.dark-bg + .bottom-bar.dark-bg {
  border-top: 1px solid #444444;
}
.main-header .top-bar.white-bg + .main-bar.white-bg {
  border-top: 1px solid #eee;
}
.main-header .main-bar.white-bg + .bottom-bar.white-bg {
  border-top: 1px solid #eee;
}
.main-header .main-bar.white-bg + .bottom-bar.smocky-white-bg {
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
}
.main-header .contact-info {
  margin-bottom: 0;
}
.main-header .contact-info li {
  float: left;
  font-weight: 400;
  padding: 14px 20px 12px;
}
.main-header .contact-info li i {
  font-size: 1.3em;
  position: relative;
  top: 4px;
  margin-right: 8px;
}
.main-header .options {
  margin-bottom: 0;
}
.main-header .options li {
  float: left;
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
}
.main-header .options li a {
  display: inline-block;
  padding: 12px 23px 13px;
  height: 51px;
}
.main-header .options li i {
  font-size: 1.4em;
  margin-right: 11px;
  position: relative;
  top: 4px;
}
.main-header .options li i.fa-angle-down {
  margin-right: 7px;
}
.main-header .options .language-selector.style2 {
  font-family: 'Montserrat', sans-serif;
  text-transform: none;
}
.main-header .options .language-selector.style2 a {
  padding-top: 10px;
  padding-right: 15px;
}
.main-header .options .language-selector.style2 .fa-angle-down {
  font-size: 1.2em;
  top: 2px;
  margin-left: 10px;
}
.main-header .options .RSS-feed {
  font-family: 'Montserrat', sans-serif;
  text-transform: none;
  border-right: none !important;
}
.main-header .options .RSS-feed a {
  padding-top: 13px;
}
.main-header .options .RSS-feed strong {
  font-weight: 500;
}
.main-header .options .RSS-feed .icon {
  top: 2px;
}
.main-header .socials {
  margin-bottom: 0;
}
.main-header .socials li {
  float: left;
}
.main-header .socials.style1 li {
  display: inline-block;
  width: 51px;
  height: 51px;
  text-align: center;
}
.main-header .socials.style1 li a {
  display: inline-block;
  width: 100%;
  height: 100%;
  border-right: 1px solid #444444;
}
.main-header .socials.style1 li a i {
  font-size: 1.2em;
  line-height: 3.1em;
}
.main-header .socials.style1 li a:hover {
  background: #ff4d4d;
  border-color: #ff4d4d;
  color: white;
}
.main-header .socials.style1 li:first-child a {
  border-left: 1px solid #444444;
}
.main-header .socials.style2 li {
  display: inline-block;
  width: 51px;
  height: 51px;
  text-align: center;
}
.main-header .socials.style2 li a {
  display: inline-block;
  width: 100%;
  height: 100%;
  border-right: 1px solid #444444;
}
.main-header .socials.style2 li a i {
  font-size: 1.2em;
  line-height: 3.1em;
}
.main-header .socials.style2 li a:hover {
  background: #333;
}
.main-header .socials.style2 li:first-child a {
  border-left: 1px solid #444444;
}
.main-header .socials.style3 {
  padding-top: 14px;
  padding-bottom: 12px;
}
.main-header .socials.style3 li {
  margin-right: 22px;
  margin-left: 0;
}
.main-header .socials.style3 li a i {
  font-size: 1.3em;
}
.main-header .socials.style4 {
  padding-top: 14px;
  padding-bottom: 12px;
}
.main-header .socials.style4 li {
  margin-right: 22px;
  margin-left: 0;
}
.main-header .socials.style4 li a {
  color: #999;
}
.main-header .socials.style4 li a i {
  font-size: 1.3em;
}
.main-header .socials.style4 li a:hover {
  color: white;
}
.main-header .socials:last-of-type li:last-child {
  margin-right: 0;
}
.main-header .account-entry {
  margin-bottom: 0;
}
.main-header .account-entry li {
  float: left;
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
}
.main-header .account-entry.style1 li {
  padding-left: 12px;
  padding-top: 16px;
  padding-bottom: 14px;
}
.main-header .account-entry.style1 li:before {
  content: '|';
  margin-right: 12px;
}
.main-header .account-entry.style1 li:first-child:before {
  content: none;
  display: none;
}
.main-header .account-entry.style2 li {
  border-left: 1px solid #ddd;
  font-weight: 600;
}
.main-header .account-entry.style2 li a {
  display: inline-block;
  padding-top: 15px;
  padding-right: 25px;
  padding-bottom: 14px;
  padding-left: 22px;
}
.main-header .account-entry.style2 + .search-form input[type=text] {
  border-left: 0;
}
.main-header .account-entry.style3 li {
  padding-left: 30px;
  font-weight: 600;
  padding-top: 15px;
  padding-bottom: 14px;
}
.main-header .account-entry.style3 li:before {
  content: '|';
  margin-right: 31px;
}
.main-header .account-entry.style3 li:first-child:before {
  content: '';
}
.main-header .account-entry:first-of-type.style2 li:last-child {
  border-right: 1px solid #ddd;
}
.sticky-menu .search-form,
.main-header .search-form {
  position: relative;
}
.sticky-menu .search-form input[type=text],
.main-header .search-form input[type=text] {
  border: none;
  background: none;
  font-weight: 300;
  width: 277px;
  padding: 15px 40px 14px 20px;
  border-right: 1px solid #ddd;
  border-left: 1px solid #ddd;
}
.sticky-menu .search-form input[type=text]:focus,
.main-header .search-form input[type=text]:focus {
  outline: none;
}
.sticky-menu .search-form input[type=submit],
.main-header .search-form input[type=submit] {
  font-family: "knight" !important;
  font-size: 1.2em;
  line-height: 1.5em;
  background: none;
  border: none;
  position: absolute;
  top: 50%;
  right: 13px;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
}
.sticky-menu .search-form ::-webkit-input-placeholder,
.main-header .search-form ::-webkit-input-placeholder {
  color: #444444;
}
.sticky-menu .search-form :-moz-placeholder,
.main-header .search-form :-moz-placeholder {
  color: #444444;
}
.sticky-menu .search-form ::-moz-placeholder,
.main-header .search-form ::-moz-placeholder {
  color: #444444;
}
.sticky-menu .search-form :-ms-input-placeholder,
.main-header .search-form :-ms-input-placeholder {
  color: #444444;
}
/*************************************************************
................. 3.2. HEADER TOP BAR ..................
*************************************************************/
.main-header .top-bar {
  overflow: hidden;
}
.main-header .top-bar ul,
.main-header .top-bar li,
.main-header .top-bar a {
  height: 50px;
}
.main-header .top-bar.style2 ul,
.main-header .top-bar.style2 li,
.main-header .top-bar.style2 a {
  height: 80px;
}
.main-header .top-bar .main-nav ul,
.main-header .top-bar .main-nav li,
.main-header .top-bar .main-nav a {
  height: auto;
}
.main-header .top-bar .socials li {
  height: auto;
}
.main-header .top-bar .left-sec .main-nav ul li:first-child {
  margin-left: 0;
}
.main-header .top-bar .left-sec .socials.style3:first-of-type li:first-child {
  margin-left: 0;
}
.main-header .top-bar .left-sec .account-entry.style1:first-of-type li:first-child {
  padding-left: 0;
}
.main-header .top-bar .left-sec .account-entry.style1:first-of-type li:last-child {
  margin-right: 23px;
}
.main-header .top-bar .left-sec .account-entry.style2 + .options li:first-child,
.main-header .top-bar .left-sec .socials.style1 + .options li:first-child,
.main-header .top-bar .left-sec .socials.style2 + .options li:first-child {
  border-left: none;
}
.main-header .top-bar .right-sec .account-entry + .search-form {
  border-left: none;
}
.main-header .top-bar .right-sec .main-nav:last-of-type ul li:last-child {
  margin-right: 0;
}
.main-header .top-bar .right-sec .socials.style3:last-of-type li:last-child {
  margin-right: 0;
}
.main-header .top-bar .right-sec .account-entry.style1:last-of-type li:first-child {
  margin-left: 11px;
}
.main-header .top-bar .main-nav.style1 ul li:not(.icon) a {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 300;
  text-transform: none;
  display: inline-block;
  padding-top: 17px;
  padding-bottom: 12px;
}
.main-header .top-bar .main-nav.style1 ul li.icon a {
  padding-top: 14px;
  padding-bottom: 11px;
}
.main-header .top-bar .main-nav.style2 ul li {
  margin-left: 0;
}
.main-header .top-bar .main-nav.style2 > ul > li > a {
  font-family: 'Montserrat', sans-serif;
  font-size: 13px;
  font-weight: 600;
  text-transform: uppercase;
  display: inline-block;
  padding: 28px 30px 27px 22px;
  position: relative;
}
.main-header .top-bar .main-nav.style2 > ul > li > a > i {
  font-size: 1.4em;
  position: relative;
  top: 5px;
  margin-right: 8px;
}
.main-header .top-bar .main-nav.style2 > ul > li > a:hover {
  background: #333;
}
.main-header .top-bar .main-nav.style2 > ul > li.active > a {
  font-weight: 700;
  background: #333;
}
.main-header .top-bar.style1 {
  height: 51px;
}
.main-header .top-bar.style1 .socials.style3 {
  padding-top: 17px;
}
.main-header .top-bar.style2 {
  height: 80px;
}
.main-header .top-bar.style2 .contact-info li {
  padding-top: 28px;
  padding-bottom: 27px;
}
.main-header .top-bar.style2 .contact-info .RSS-feed {
  padding-top: 29px;
}
.main-header .top-bar.style2 .options li a {
  display: inline-block;
  padding: 26px 23px 28px;
}
.main-header .top-bar.style2 .account-entry li {
  padding-top: 30px;
  padding-bottom: 28px;
}
.main-header .top-bar.style2:not(.padding-20) .socials.style3 {
  padding-top: 32px;
  padding-bottom: 26px;
}
.main-header .top-bar.dark-bg {
  color: #ddd;
}
.main-header .top-bar.dark-bg a {
  color: #eee;
}
.main-header .top-bar.dark-bg .contact-info li,
.main-header .top-bar.dark-bg .options li {
  border-right: 1px solid #444444;
}
.main-header .top-bar.dark-bg .contact-info li:first-child,
.main-header .top-bar.dark-bg .options li:first-child {
  border-left: 1px solid #444444;
}
.main-header .top-bar.dark-bg .contact-info.style2 li,
.main-header .top-bar.dark-bg .options.style2 li {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  border: none;
}
.main-header .top-bar.dark-bg .search-form {
  border-color: #444444;
}
.main-header .top-bar.dark-bg .search-form input {
  color: white;
}
.main-header .top-bar.dark-bg .search-form ::-webkit-input-placeholder {
  color: white;
}
.main-header .top-bar.dark-bg .search-form :-moz-placeholder {
  color: white;
}
.main-header .top-bar.dark-bg .search-form ::-moz-placeholder {
  color: white;
}
.main-header .top-bar.dark-bg .search-form :-ms-input-placeholder {
  color: white;
}
.main-header .top-bar.dark-bg .account-entry.style2 li {
  border-color: #444444;
}
.main-header .top-bar.foggy-white-bg {
  color: #444444;
}
.main-header .top-bar.foggy-white-bg a {
  color: #444444;
}
.main-header .top-bar.foggy-white-bg .contact-info li,
.main-header .top-bar.foggy-white-bg .options li {
  border-right: 1px solid #ddd;
}
.main-header .top-bar.foggy-white-bg .contact-info li:first-child,
.main-header .top-bar.foggy-white-bg .options li:first-child {
  border-left: 1px solid #ddd;
}
.main-header .top-bar.foggy-white-bg .contact-info.style2 li,
.main-header .top-bar.foggy-white-bg .options.style2 li {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  border: none;
}
.main-header .top-bar.foggy-white-bg .socials.style1 li a {
  border-color: #ddd;
}
.main-header .top-bar.foggy-white-bg .socials.style1 li a:hover {
  background: #ff5344;
  color: white;
  border-color: #ff5344;
}
.main-header .top-bar.foggy-white-bg .socials.style1 li:first-child a {
  border-color: #ddd;
}
.main-header .top-bar.foggy-white-bg .socials.style1 li:first-child a:hover {
  border-color: #ff5344;
}
.main-header .top-bar.foggy-white-bg .socials.style2 li a {
  border-color: #ddd;
}
.main-header .top-bar.foggy-white-bg .socials.style2 li a:hover {
  background: #ff5344;
  color: white;
  border-color: #ff5344;
}
.main-header .top-bar.foggy-white-bg .socials.style2 li:first-child a {
  border-color: #ddd;
}
.main-header .top-bar.foggy-white-bg .socials.style2 li:first-child a:hover {
  border-color: #ff5344;
}
.main-header .top-bar.white-bg a {
  color: #444444;
}
.main-header .top-bar.white-bg .contact-info li,
.main-header .top-bar.white-bg .options li {
  border-right: 1px solid #eee;
}
.main-header .top-bar.white-bg .contact-info li:first-child,
.main-header .top-bar.white-bg .options li:first-child {
  border-left: 1px solid #eee;
}
.main-header .top-bar.white-bg .contact-info.style2 li,
.main-header .top-bar.white-bg .options.style2 li {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  border: none;
}
.main-header .top-bar.white-bg .socials.style1 li a {
  border-color: #eee;
}
.main-header .top-bar.white-bg .socials.style1 li a:hover {
  background: #ff5344;
  border-color: #ff5344;
  color: white;
}
.main-header .top-bar.white-bg .socials.style1 li:first-child a {
  border-color: #eee;
}
.main-header .top-bar.white-bg .socials.style2 li a {
  border-color: #eee;
}
.main-header .top-bar.white-bg .socials.style2 li a:hover {
  background: #f9f9f9;
}
.main-header .top-bar.white-bg .socials.style2 li:first-child a {
  border-color: #eee;
}
.main-header .top-bar.white-bg .socials.style3 li a:hover {
  color: black;
}
.main-header .top-bar.transparent-bg {
  color: white;
}
.main-header .top-bar.transparent-bg a {
  color: white;
}
.main-header .top-bar.smocky-white-bg {
  color: #444444;
}
.main-header .top-bar.smocky-white-bg a {
  color: #444444;
}
.main-header .top-bar.smocky-white-bg .contact-info li,
.main-header .top-bar.smocky-white-bg .options li {
  border-right: 1px solid #ddd;
}
.main-header .top-bar.smocky-white-bg .contact-info li:first-child,
.main-header .top-bar.smocky-white-bg .options li:first-child {
  border-left: 1px solid #ddd;
}
.main-header .top-bar.smocky-white-bg .contact-info.style2 li,
.main-header .top-bar.smocky-white-bg .options.style2 li {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  border: none;
}
.main-header .top-bar.smocky-white-bg .socials.style1 li a {
  border-color: #ddd;
}
.main-header .top-bar.smocky-white-bg .socials.style1 li a:hover {
  background: #ff5344;
  color: white;
  border-color: #ff5344;
}
.main-header .top-bar.smocky-white-bg .socials.style1 li:first-child a {
  border-color: #ddd;
}
.main-header .top-bar.smocky-white-bg .socials.style1 li:first-child a:hover {
  border-color: #ff5344;
}
.main-header .top-bar.smocky-white-bg .socials.style2 li a {
  border-color: #ddd;
}
.main-header .top-bar.smocky-white-bg .socials.style2 li a:hover {
  background: #ff5344;
  color: white;
  border-color: #ff5344;
}
.main-header .top-bar.smocky-white-bg .socials.style2 li:first-child a {
  border-color: #ddd;
}
.main-header .top-bar.smocky-white-bg .socials.style2 li:first-child a:hover {
  border-color: #ff5344;
}
/*************************************************************
................. 3.3. HEADER MAIN BAR ..................
*************************************************************/
.main-header:not(.fixed-menu) .main-bar > .container {
  display: -webkit-flex;
  display: -ms-flexbox;
  display: -moz-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.main-header:not(.fixed-menu) .main-bar > .container > .row {
  width: 100%;
}
.main-header:not(.fixed-menu) .main-bar .main-nav {
  opacity: 0;
  visibility: hidden;
}
.main-header .main-bar .nav {
  background: none;
  border: none;
  margin-bottom: 0;
  height: auto;
  min-height: inherit;
}
.main-header .main-bar .shifter-handle {
  float: left;
}
.main-header .main-bar .socials {
  padding: 0;
}
.sticky-menu .sticky-logo-container,
.main-header .main-bar .logo-container {
  float: left;
  order: -1;
}
.main-header .main-bar .left-sec {
  order: -1;
}
.main-header .main-bar .right-sec {
  order: 1;
}
.main-header .main-bar .search-form input[type=text] {
  width: 100%;
}
.main-header .main-bar .search-form input[type=submit] {
  right: 26px;
}
.main-header .main-bar.white-bg .main-nav ul > li > a {
  color: #444444;
}
.main-header .main-bar.white-bg .main-nav.active-style1 > ul > li.active > a,
.main-header .main-bar.white-bg .main-nav.active-style1 > ul > li.open > a,
.main-header .main-bar.white-bg .main-nav.active-style1 > ul > li:hover > a {
  color:  #18297d;
}
.main-header .main-bar.white-bg .main-nav.active-style2 > ul > li > a:after {
  background: #222222;
}
.main-header .main-bar.white-bg .main-nav.shopping-menu-style1 ul li.icon > a {
  color: black;
}
.main-header .main-bar.white-bg .socials li a {
  color: #444444;
}
.main-header .main-bar.dark-bg {
  color: white;
}
.main-bar.dark-bg .main-nav ul > li > a {
  color: white;
}
.main-header .main-bar.dark-bg .main-nav.active-style1 > ul > li.active:not(.icon) > a,
.main-header .main-bar.dark-bg .main-nav.active-style1 > ul > li.open:not(.icon) > a,
.main-header .main-bar.dark-bg .main-nav.active-style1 > ul > li:not(.icon):hover > a {
  color: #ff553f;
}
.main-header .main-bar.dark-bg .main-nav.active-style2 > ul > li > a:after {
  background: white;
}
.main-header .main-bar.dark-bg .socials.style3 li a:hover {
  color: #ff4f4c;
}
.main-header .main-bar.transparent-bg .main-nav ul > li > a {
  color: white;
}
.main-header .main-bar.transparent-bg .main-nav.active-style2 > ul > li > a:after {
  background: white;
}
.main-header .main-bar.transparent-bg .socials li a {
  color: #444444;
}
.main-header .main-bar.transparent-bg .options li a {
  color: white !important;
  border-color: white !important;
}
/*************************************************************
................. 3.4. HEADER BOTTOM BAR ..................
*************************************************************/
.main-header .bottom-bar.dark-bg {
  color: white;
}
.main-header .bottom-bar.dark-bg .shopping-menu ul li.icon > a {
  color: white;
}
.main-header .bottom-bar.foggy-white-bg {
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}
.main-header .bottom-bar.smocky-white-bg .main-nav > ul > li > a {
  color: #444444;
}
.main-header .bottom-bar.smocky-white-bg .main-nav ul > li,
.main-header .bottom-bar.foggy-white-bg .main-nav ul > li,
.main-header .bottom-bar.white-bg .main-nav ul > li {
  border-color: #ccc !important;
}
.main-header .bottom-bar.white-bg .main-nav ul > li {
  border-color: #eee !important;
}
.main-header .bottom-bar.smocky-white-bg .main-nav.style3 > ul > li:hover > a,
.main-header .bottom-bar.foggy-white-bg .main-nav.style3 > ul > li:hover > a,
.main-header .bottom-bar.white-bg .main-nav.style3 > ul > li:hover > a {
  color: white;
}
.main-header .bottom-bar.white-bg .main-nav ul > li > a {
  color: #444444;
}
.main-header .bottom-bar.transparent-bg .main-nav {
  border-color: white !important;
}
.main-header .bottom-bar.transparent-bg .main-nav > ul > li > a {
  color: white;
}
.main-header .bottom-bar.dark-bg .main-nav > ul > li > a {
  color: white;
}
.main-header .bottom-bar .menu-container {
  float: none;
}
.main-header .bottom-bar .menu-container:before,
.main-header .bottom-bar .menu-container:after {
  content: ' ';
  display: table;
}
.main-header .bottom-bar .menu-container:after {
  clear: both;
}
.main-header .bottom-bar .menu-container .main-nav {
  float: none;
}
.main-header .bottom-bar .main-nav > ul > li.active.style4 a .active-indicator {
  background: #ff4d4d;
}
.main-header .bottom-bar .main-nav > ul > li:first-child {
  margin-left: 0;
}
.main-header .bottom-bar .main-nav > ul .icon > a > i {
  font-size: 1.5em;
}
.main-header .bottom-bar .main-nav .socials.style3 {
  padding-top: 0;
  padding-bottom: 0;
}
.main-header .bottom-bar.style1 .main-nav > ul > li > a {
  display: inline-block;
  padding-top: 29px;
  padding-bottom: 26px;
}
.main-header .bottom-bar.style1 .main-nav > ul > li.icon > a {
  padding-top: 27px;
  padding-bottom: 23px;
}
.main-header .bottom-bar.style1 .main-nav > ul .expandable-search-form {
  padding-top: 10px;
}
.main-header .bottom-bar.style2 .main-nav > ul > li > a {
  display: inline-block;
  padding-top: 20px;
  padding-bottom: 19px;
}
.main-header .bottom-bar.style2 .main-nav.shopping-menu > ul > li {
  margin-left: 20px;
}
.main-header .bottom-bar.style2 .main-nav.shopping-menu > ul > li > a {
  padding-bottom: 14px;
}
.main-header .bottom-bar.style3 .main-nav > ul > li {
  margin-left: 45px;
}
.main-header .bottom-bar.style3 .main-nav > ul > li > a {
  display: inline-block;
  padding-top: 34px;
  padding-bottom: 30px;
}
/*************************************************************
................. 3.5. HEADER MAIN NAV ..................
*************************************************************/
.main-nav {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  padding: 0;
  position: inherit;
}
.main-nav ul {
  margin-bottom: 0;
}
.main-nav ul li {
  float: left;
  margin-left: 24px;
}
.main-nav ul li a {
  text-transform: uppercase;
  display: inline-block;
  color: #444444;
  -webkit-transition: color 0.3s ease, font-weight 0.3s ease, background 0.3s ease;
  -moz-transition: color 0.3s ease, font-weight 0.3s ease, background 0.3s ease;
  transition: color 0.3s ease, font-weight 0.3s ease, background 0.3s ease;
}
.main-nav ul li.icon a i {
  font-size: 1.4em;
  position: relative;
  top: 2px;
}
.main-nav ul li.icon .dropdown-menu {
  left: auto;
  right: 0;
}
.main-nav ul li.icon .dropdown-menu.dropdown-search-form {
  padding: 0;
}
.main-nav ul li.icon .dropdown-menu.dropdown-search-form form {
  padding: 0 !important;
}
.main-nav ul li.icon .dropdown-menu.dropdown-search-form form input[type="text"] {
    border: medium none;
    border-radius: 0;
    height: auto;
    padding-left: 60px;
    width: 460px;
  margin:0;
}
.main-nav ul li.icon .dropdown-menu.dropdown-search-form form input[type=text].dark-bg {
  color: white;
}
.main-nav ul li.icon .dropdown-menu.dropdown-search-form form input[type=text].white-bg {
  color: black;
}
.main-nav ul li.icon .dropdown-menu.dropdown-search-form form input[type=text]:focus {
  outline: none;
}
.main-nav ul li.icon .dropdown-menu.dropdown-search-form form input[type=submit] {
  font-family: 'knight';
  font-size: 1.5em;
  display: inline-block;
  width: 57px;
  height: 100%;
  position: absolute;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
  -ms-transform:     translateY(-50%);
  transform:         translateY(-50%);
}
.main-nav ul li.icon .dropdown-menu.dropdown-search-form form .dark-bg + input[type=submit] {
  color: #ccc;
}
.main-nav ul li.icon .dropdown-menu.dropdown-search-form form .white-bg + input[type=submit] {
  color: black;
}
.main-nav ul li.icon .dropdown-menu.dropdown-search-form form ::-webkit-input-placeholder {
  color: #ccc;
}
.main-nav ul li.icon .dropdown-menu.dropdown-search-form form :-moz-placeholder {
  color: #ccc;
}
.main-nav ul li.icon .dropdown-menu.dropdown-search-form form ::-moz-placeholder {
  color: #ccc;
}
.main-nav ul li.icon .dropdown-menu.dropdown-search-form form :-ms-input-placeholder {
  color: #ccc;
}
.main-nav ul li.icon.expandable-search-form .search-form-container .search-form input[type=text] {
  border: 1px solid transparent;
  width: 0;
  opacity: 0;
  -webkit-transition: 0.8s all 0.001s ease;
  -moz-transition: 0.8s all 0.001s ease;
  transition: 0.8s all 0.001s ease;
}
.main-nav ul li.icon.expandable-search-form .search-form-container .search-form input[type=text]:focus {
  outline: none;
  border-color: #ccc !important;
}
.main-nav ul li.icon.expandable-search-form .search-form-container .search-form input[type=submit] {
  font-weight: normal;
  right: -4px;
  height: 57px;
  width: 57px;
  font-size: 1.45em;
  -webkit-transition: 1s all 0.001s ease;
  -moz-transition: 1s all 0.001s ease;
  transition: 1s all 0.001s ease;
}
.main-nav ul li.icon.expandable-search-form .search-form-container .search-form:hover input[type=text] {
  width: 460px;
  border-color: #eee;
  opacity: 1;
  padding-left: 57px;
}
.main-nav ul li.icon.expandable-search-form .search-form-container .search-form:hover input[type=submit] {
  right: 87%;
  -webkit-transition: 0.5s all 0.001s ease;
  -moz-transition: 0.5s all 0.001s ease;
  transition: 0.5s all 0.001s ease;
}
.main-nav ul li:hover > a,
.main-nav ul li.open > a,
.main-nav ul li.active > a {
  font-weight: 700;
  color:  #18297d;
}
.nav  li:hover > .sub-menu,
.nav  li.open > .sub-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  -ms-transform: translateY(0);
  -webkit-transform: translateY(0);
  -webkit-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

.serach-icon1  li:first-child:hover + .sub-menu,
.serach-icon1  li:first-child.open + .sub-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  -ms-transform: translateY(0);
  -webkit-transform: translateY(0);
  -webkit-transition: all 0.2s ease;
  -o-transition: all 0.2s ease;
  transition: all 0.2s ease;
}


.main-nav ul li.icon-cart .basket-icon {
  margin-right: 19px;
}
.main-nav ul li.icon-cart > a {
  position: relative;
}
.main-nav ul li.icon-cart > a span.items-counter.style2 {
  position: absolute;
  top: 50%;
  left: -11px;
  margin-left: 0;
  display: inline-block;
  width: 24px;
  height: 24px;
  margin-top: -12px;
  font-family: 'Montserrat', sans-serif;
  text-align: center;
  line-height: 1.8em;
  font-weight: 300;
  color: white;
  background: #ff4d4d;
  -webkit-border-radius: 50em;
  -moz-border-radius: 50em;
  border-radius: 50em;
  -webkit-transform: translateY(-50%);
  -ms-transform:   translateY(-50%);
  transform:     translateY(-50%);
}
.main-nav ul li > .sub-menu {
  padding-top: 24px;
  padding-bottom: 22px;
  margin: 0;
  z-index: 99999;
  border: none;
  visibility: hidden;
  opacity: 0;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  -webkit-transition: -webkit-transform 0.1s ease, opacity 0.1s ease, visibility 0.1s ease;
  -moz-transition: -moz-transform 0.1s ease, opacity 0.1s ease, visibility 0.1s ease;
  transition: transform 0.1s ease, opacity 0.1s ease, visibility 0.1s ease;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.main-nav:not(.mobile-nav):not(.offcanvas-menu-style2):not(.main-nav-fixed) ul li > .sub-menu {
  display: inherit !important;
}
.main-nav:not(.mobile-nav) ul li .sub-menu.white-bg li a{
  color: #444;
}
.main-nav ul li > .sub-menu.sub-menu-left {
  float: right;
  left: auto;
  right: 0;
}
.main-nav ul li > .sub-menu li {
  display: block;
  position: relative;
  float: none;
  margin-left: 0;
  padding-left: 19px;
  padding-right: 22px;
}
.main-nav ul li > .sub-menu li > a {
  font-family: 'Montserrat', sans-serif;
  font-weight: 400;
  display: block;
  text-transform: uppercase;
  border-bottom: 1px solid #444444;
  position: relative;
  min-width: 236px;
  padding: 16px 0 16px 12px;
  white-space: nowrap;
}
.main-nav ul li > .sub-menu li > a > i {
  margin-right: 11px;
  font-size: 1.6em;
  position: absolute;
  top: 50%;
  left: 5px;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  font-weight: normal !important;
}
.main-nav ul li > .sub-menu li > a:hover,
.main-nav ul li > .sub-menu li > a:focus {
  background: none;
  /*font-weight: 600;*/
}
.main-nav ul li > .sub-menu li:last-child > a {
  border-bottom: none;
}
.main-nav ul li > .sub-menu li.has-children > a,
.main-nav ul li > .sub-menu li .arrow-right > a {
  position: relative;
}
.main-nav:not(.mobile-nav):not(.offcanvas-menu-style2) ul li > .sub-menu li.has-children > a:after,
.main-nav:not(.mobile-nav):not(.offcanvas-menu-style2) ul li > .sub-menu li .arrow-right > a:after {
  content: 'î†­';
  font-size: 1.28em;
  font-family: 'knight' !important;
  position: absolute;
  top: 50%;
  right: 5px;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  font-weight: normal !important;
}
.main-nav ul li > .sub-menu li.has-icon {
  padding-left: 23px;
  padding-right: 24px;
}
.main-nav ul li > .sub-menu li.has-icon > a {
  padding: 16px 62px 16px 39px;
}
.main-nav ul li > .sub-menu li.has-icon .sub-menu li {
  padding-left: 17px;
  padding-right: 21px;
}
.main-nav ul li > .sub-menu li.has-icon .sub-menu li a {
  padding: 15px 22px 16px 22px;
}
.main-nav ul li > .sub-menu li .sub-menu {
  position: absolute;
  top: -21px;
  left: 100%;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-3%);
  -ms-transform: translateY(-3%);
  -webkit-transform: translateY(-3%);
  padding-top: 22px;
  padding-bottom: 24px;
}
.main-nav ul li > .sub-menu li .sub-menu li {
  padding-left: 13px;
  padding-right: 21px;
}
.main-nav ul li > .sub-menu li .sub-menu li > a {
  min-width: 205px;
  padding: 15px 13px;
}
.main-nav ul .dropdown.dropdown-menu-left > .sub-menu {
  left: auto;
  right: 0;
}
.main-nav ul li > .sub-menu li.dropdown-menu-left .sub-menu {
  left: auto;
  right: 100%;
}
/*.main-nav ul li > .sub-menu li.open > a,
.main-nav ul li > .sub-menu li:hover > a {
  font-weight: 600;
}*/
.main-nav ul li > .sub-menu li.open .sub-menu,
.main-nav ul li > .sub-menu li:hover .sub-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  -ms-transform: translateY(0);
  -webkit-transform: translateY(0);
}
.main-nav ul li > .sub-menu.top-red-border {
  border-top: 3px solid #ff4d4d;
  padding-top: 18px;
}
.main-nav ul li > .sub-menu.dark-bg {
  color: white;
}
.main-nav ul li > .sub-menu.dark-bg a {
  color: white !important;
}
.main-nav ul li > .sub-menu.white-bg {
  color: #444444;
  -webkit-box-shadow: 0 3px 2px rgba(204, 204, 202, 0.4);
  -moz-box-shadow: 0 3px 2px rgba(204, 204, 202, 0.4);
  box-shadow: 0 3px 2px rgba(204, 204, 202, 0.4);
}
.main-nav:not(.mobile-nav) ul li > .sub-menu.white-bg li > a {
  color: #444444 !important;
  border-bottom-color: #ddd;
}
.main-nav ul li > .sub-menu.white-bg .sub-menu.dark-bg > li > a {
  color: white;
  border-bottom: 1px solid #444;
}
.main-nav ul li.mega-menu {
  position: static;
}
.main-nav ul li.mega-menu.multi-column {
  position: static;
}
.main-nav ul li.mega-menu .sub-menu {
  padding-top: 18px;
  padding-bottom: 19px;
}
.main-nav ul li.mega-menu .sub-menu li {
  padding: 0;
}
.main-nav ul li.mega-menu .sub-menu li a {
  text-transform: inherit;
}
.main-nav ul li.mega-menu .sub-menu li.has-icon a {
  padding: 15px 0 15px 39px;
}
.main-nav ul li.mega-menu .sub-menu li .menu-column > ul {
  width: 100%;
}
.main-nav ul li.mega-menu .sub-menu .container {
  float: none;
  margin-right: auto !important;
  margin-left: auto !important;
}
.main-nav ul li.mega-menu .sub-menu.top-red-border {
  padding-top: 15px;
}
.main-nav ul li.mega-menu .sub-menu .blog-post {
  padding-top: 19px;
}
.main-nav ul li.mega-menu .sub-menu .blog-post .post-thumb {
  margin-bottom: 27px;
}
.main-nav ul li.mega-menu .sub-menu .blog-post .post-title h6 {
  margin-bottom: 21px;
}
.main-nav ul li.mega-menu .sub-menu .blog-post .post-title h6 a {
  color: #202020;
}
.main-nav ul li.mega-menu .sub-menu .blog-post .post-title h6 a:hover {
  color: #ff4d4d !important;
}
.main-nav ul li.mega-menu .sub-menu.dark-bg .blog-post .post-title h6 a {
  color: white;
}
.main-nav ul li.mega-menu .sub-menu .blog-post .post-contents p {
  font-family: 'Montserrat', sans-serif;
  font-weight: 300;
  line-height: 1.8em;
}
.main-nav ul li.mega-menu .sub-menu .blog-post .post-meta .post-cat {
  padding-right: 0;
}
.main-nav ul li.mega-menu .sub-menu .blog-post .post-meta .post-comments {
  padding-left: 23px;
}
.main-nav ul li.mega-menu .sub-menu .blog-post .post-meta p,
.main-nav ul li.mega-menu .sub-menu .blog-post .post-meta a {
  font-family: 'Montserrat', sans-serif;
  font-weight: 300;
}
.main-nav ul li.mega-menu .sub-menu .blog-post .post-meta a {
  color: #ff5047;
}
.main-nav ul li.mega-menu:not(.full-width) .sub-menu li .menu-column ul li {
  padding: 0;
}
.main-nav:not(.mobile-nav) ul li.mega-menu:not(.full-width):not(.multi-column) .sub-menu li .menu-column ul li a {
  padding-left: 36px;
  border-bottom: none;
}
.main-nav ul li.mega-menu.full-width .sub-menu {
  width: 100%;
  padding-top: 35px;
  padding-bottom: 35px;
}
.main-nav ul li.mega-menu.multi-column .sub-menu{
  width: auto;
  padding-top: 29px;
  padding-bottom: 29px;
}
.main-header .main-nav ul .mega-menu.multi-column > .sub-menu{
  /*left: auto !important;*/
  right: auto !important;
  /*margin-left: -70px;*/
}
.main-nav ul li.mega-menu.multi-column .sub-menu .menu-column ul li a,
.main-nav ul li.mega-menu.full-width .sub-menu .menu-column ul li a {
  border-bottom: 1px solid #444444;
}
.main-nav ul li.mega-menu.multi-column .sub-menu .menu-column ul li:last-child:not(.column-title) a,
.main-nav ul li.mega-menu.full-width .sub-menu .menu-column ul li:last-child:not(.column-title) a {
  border-bottom: none;
}
.main-nav ul li.mega-menu.multi-column .sub-menu .menu-column ul li.column-title a,
.main-nav ul li.mega-menu.full-width .sub-menu .menu-column ul li.column-title a {
  font-weight: 600;
  text-transform: uppercase;
}
.main-nav:not(.mobile-nav) ul li.mega-menu.multi-column .sub-menu .menu-column ul li.column-title:not(.has-icon) a,
.main-nav:not(.mobile-nav) ul li.mega-menu.full-width .sub-menu .menu-column ul li.column-title:not(.has-icon) a {
  padding-top: 12px;
  padding-left: 6px;
}
.main-nav ul li.mega-menu.multi-column .sub-menu .menu-column ul.arrow-list li:not(.column-title) a,
.main-nav ul li.mega-menu.full-width .sub-menu .menu-column ul.arrow-list li:not(.column-title) a {
  padding: 17px 0 13px 34px;
  position: relative;
}
.main-nav ul li.mega-menu.multi-column .sub-menu .menu-column ul.arrow-list li:not(.column-title) a:before,
.main-nav ul li.mega-menu.full-width .sub-menu .menu-column ul.arrow-list li:not(.column-title) a:before {
  content: 'î†­';
  font-family: knight;
  font-size: 1.45em;
  font-weight: normal !important;
  position: absolute;
  top: 50%;
  left: 6px;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
}
.main-nav ul li.mega-menu.multi-column .sub-menu .menu-column ul.arrow-list li:not(.column-title):last-child a,
.main-nav ul li.mega-menu.full-width .sub-menu .menu-column ul.arrow-list li:not(.column-title):last-child a {
  border-bottom: none;
}
.main-nav:not(.mobile-nav) ul li.mega-menu.multi-column .sub-menu.white-bg .menu-column li a,
.main-nav:not(.mobile-nav) ul li.mega-menu.full-width .sub-menu.white-bg .menu-column li a {
  border-bottom-color: #ddd;
}
.main-nav ul li.mega-menu.multi-column .sub-menu.top-red-border,
.main-nav ul li.mega-menu.full-width .sub-menu.top-red-border {
  padding-top: 19px;
}
.main-nav ul li.mega-menu.shop-style1 > .sub-menu {
  padding-top: 47px;
  padding-bottom: 52px;
}
.main-nav ul li.mega-menu.shop-style2 > .sub-menu {
  padding-top: 35px;
  padding-bottom: 43px;
}
.main-nav ul li.mega-menu.blog > .sub-menu {
  padding-top: 20px;
  padding-bottom: 27px;
}
.main-nav ul li.checkout-style1 > .sub-menu {
  padding-bottom: 0 !important;
  border-top: 1px solid #eee;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}
.main-nav ul li.checkout-style1 .product {
  margin-bottom: 28px;
}
.main-nav ul li.checkout-style1 .product .product-details .product-price p {
  font-weight: 300;
}
.main-nav ul li.checkout-style1 .product .product-details .product-price .quantity {
  margin-right: 4px;
}
.main-nav ul li.checkout-style1 .product .product-details .product-price .quantity:after {
  content: 'x';
  margin-left: 3px;
}
.main-nav ul li.checkout-style1 .checkout-buttons {
  padding-top: 30px;
  padding-bottom: 28px;
}
.main-nav ul li.checkout-style1 .checkout-buttons .button {
  margin-left: 8px;
}
.main-nav ul li.checkout-style2 {
  position: static;
}
.main-nav ul li.checkout-style2 > .sub-menu {
  padding-top: 4px;
  padding-bottom: 40px;
}
.main-nav ul li.checkout-style2 > .sub-menu > li {
  padding: 0 6px 0 13px;
}
.main-nav ul li.checkout-style2 > .sub-menu > li .menu-column {
  min-width: 340px;
  margin-right: 15px;
  margin-left: 15px;
  float: left;
  padding: 0;
}
.main-nav ul li.checkout-style2 > .sub-menu.dark-bg .product {
  border-bottom-color: #444444;
}
.main-nav ul li.checkout-style2 > .sub-menu.dark-bg .product .product-remove {
  color: #838383;
}
.main-nav ul li.checkout-style2 > .sub-menu.white-bg {
  box-shadow: none;
  border: 1px solid #eee;
  padding-top: 3px;
  padding-bottom: 29px;
}
/* BizFunctional */
.prod-category-section{
    max-width:300px;
}
.prod-category-title{
    font-weight:bold;
    padding-bottom: 4px !important;
}
.prod-category-item{
    border: 1px solid #eee;
    padding-left:20px;
    padding-top: 3px;
    padding-bottom: 3px;
    border-right:none;
    border-left:none;
}
.prod-category-item:before {
font-family: 'FontAwesome';
content: '\f105';
margin:0 5px 0 -15px;
}
.main-nav ul li.checkout-style2 > .sub-menu.white-bg .product {
  border-bottom-color: #ddd;
}
.main-nav ul li.checkout-style2 > .sub-menu.white-bg .cart-checkout-buttons-container .checkout-buttons .view-cart-button {
  background: #222222;
  color: white;
}
.main-nav ul li.checkout-style2 > .sub-menu.white-bg .cart-checkout-buttons-container .checkout-buttons .checkout-button {
  border: 1px solid #222222;
  background: white;
  color: #222222;
}
.main-nav ul li.checkout-style2 > .sub-menu .cart-checkout-buttons-container .checkout-buttons .view-cart-button:hover,
.main-nav ul li.checkout-style2 > .sub-menu .cart-checkout-buttons-container .checkout-buttons .checkout-button:hover {
  background: #ff4d4d;
  color: white;
  border-color: transparent;
}
.main-nav ul li.checkout-style2 .product {
  border-bottom: 1px solid #444444;
  padding-top: 26px;
  padding-bottom: 25px;
  position: relative;
}
.main-nav ul li.checkout-style2 .product .product-preview {
  width: 80px;
  height: 80px;
  float: left;
  margin-right: 12px;
  margin-left: 5px;
}
.main-nav ul li.checkout-style2 .product .product-details {
  float: left;
  padding-right: 60px;
  width: 70%;
}
.main-nav ul li.checkout-style2 .product .product-details h6 {
  margin-right: 0;
  margin-bottom: 3px;
}
.main-nav ul li.checkout-style2 .product .product-details .product-cat {
  font-family: 'Montserrat', sans-serif;
  margin-bottom: 6px;
}
.main-nav ul li.checkout-style2 .product .product-details .product-cat a {
  font-weight: 300;
  text-transform: none;
}
.main-nav ul li.checkout-style2 .product .product-details .product-price {
  font-family: 'Montserrat', sans-serif;
  color: #ff4d4d;
  font-weight: 400;
  text-align: left;
}
.main-nav ul li.checkout-style2 .product .product-details .product-price p {
  margin-bottom: 0;
}
.main-nav ul li.checkout-style2 .product .product-details .product-price .quantity {
  margin-right: 4px;
}
.main-nav ul li.checkout-style2 .product .product-details .product-price .quantity:after {
  content: 'x';
  margin-left: 3px;
}
.main-nav ul li.checkout-style2 .product .product-remove {
  position: absolute;
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  color: #adadad;
}
.main-nav ul li.checkout-style2 .product .product-remove i {
  font-size: 2em;
  line-height: 0;
}
.main-nav ul li.checkout-style2 .product .product-remove:hover {
  color: #ff4d4d !important;
}
.main-nav ul li.checkout-style2 .cart-checkout-buttons-container {
  margin-top: 37px;
  text-align: center;
  float: right;
}
.main-nav ul li.checkout-style2 .cart-checkout-buttons-container .checkout-buttons {
  padding-right: 29px;
}
.main-nav ul li.checkout-style2 .cart-checkout-buttons-container .checkout-buttons .button {
  margin-left: 4px;
}
.main-nav ul li.checkout-style2 .cart-checkout-buttons-container .checkout-buttons .view-cart-button {
  border: none;
  background: white;
  color: #222222;
  padding: 11px 32px 13px;
}
.main-nav ul li.checkout-style2 .dark-bg .cart-checkout-buttons-container .checkout-buttons .view-cart-button {
  color: white;
  background: #ff4d4d;
}
.main-nav ul li.checkout-style2 .cart-checkout-buttons-container .checkout-buttons .checkout-button {
  padding: 10px 32px 12px;
  border: 1px solid white;
  background: #222222;
  color: white;
}
.main-nav.extra-padding ul li {
  margin-left: 39px;
}
.main-nav.extra-padding ul li.icon {
  margin-left: 30px;
}
.main-nav.style1 > ul > li > a {
  font-weight: 600;
}
.main-nav.style1 > ul > li.active > a {
  font-weight: 700;
}
.main-nav.style2 > ul > li > a {
  font-weight: 400;
}
.main-nav.style2 > ul > li.active > a {
  font-weight: 600;
}
.main-nav.style3 > ul > li {
  margin-left: 0;
  border-left: 1px solid #444444;
  text-align: center;
}
.main-nav.style3 > ul > li > a {
  display: inline-block;
  padding-right: 23px;
  padding: 20px 22px 19px 23px;
}
.main-nav.style3 > ul > li:hover > a {
  background: #ff4d4d;
}
.main-nav.style3 > ul > li:last-child {
  border-right: 1px solid #444444;
}
.main-nav.style4 {
  float: right;
}
.main-nav.style4 .navigation {
  float: left;
}
.main-nav.style4 .navigation > ul {
  float: none;
  text-align: right;
}
.main-nav.style4 .navigation > ul > li {
  float: none;
  display: inline-block;
  margin-left: 0;
}
.main-nav.style4 .navigation > ul.top-nav {
  border-bottom: 1px solid #666;
  margin-bottom: 8px;
}
.main-nav.style4 .navigation > ul.top-nav > li {
  margin-left: 25px;
}
.main-nav.style4 .navigation > ul.top-nav > li:hover > .sub-menu {
  opacity: 0;
  visibility: hidden;
  -webkit-transform: translateY(3%);
  -moz-transform: translateY(3%);
  transform: translateY(3%);
}
.main-nav.style4 .navigation > ul.top-nav > li.open > .sub-menu {
  opacity: 1;
  visibility: visible;
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  transform: translateY(0);
}
.main-nav.style4 .navigation > ul.top-nav > li > a {
  padding-bottom: 10px;
  letter-spacing: 2px;
}
.main-nav.style4 .navigation > ul.top-nav > li.has-children > a:after {
  font-family: 'knight';
  content: 'î…»';
  margin-left: 5px;
  position: relative;
  top: 2px;
}
.main-nav.style4 .navigation > ul.top-nav > li:first-child {
  margin-left: 30px;
}
.main-nav.style4 .navigation > ul.bottom-nav li {
  margin-left: 17px;
}
.main-nav.style4 .navigation > ul.bottom-nav li a {
  font-family: 'Montserrat', sans-serif;
  font-weight: 300;
  text-transform: none;
}
.main-nav.style4 .navigation > ul.bottom-nav li:before {
  content: '|';
  font-size: 11px;
  color: #666;
  position: relative;
  top: -1px;
  margin-right: 23px;
}
.main-nav.style4 .navigation > ul.bottom-nav li:first-child:before {
  content: '';
}
.main-nav.style4 .shopping-menu-style2 {
  float: right;
  margin-top: 8px;
}
.main-nav.style4 .shopping-menu-style2 > li > a {
  display: inline-block;
  width: 45px;
  height: 45px;
  text-align: center;
  background: #ff4d4d;
  padding: 0 !important;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  border-radius: 3px;
}
.main-nav.style4 .shopping-menu-style2 > li > a i {
  line-height: 2.4em;
}
.main-nav.style4 .shopping-menu-style2 > li > a .items-counter.style2 {
  background: white;
  color: #444444;
  top: -22%;
}
.main-header.style4 .bottom-bar .main-nav {
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}
.main-header.style4 .bottom-bar .main-nav.transparent-border {
  border-color: rgba(255, 255, 255, 0.5) !important;
}
.main-nav.style4 .shopping-menu-style2 > li .basket-icon {
  margin-right: 0;
}
.main-nav.style4 > .shopping-menu > li:hover > .sub-menu {
  opacity: 0;
  visibility: hidden;
  -webkit-transform: translateY(3%);
  -moz-transform: translateY(3%);
  transform: translateY(3%);
}
.main-nav.style4 > .shopping-menu > li.open > .sub-menu {
  opacity: 1;
  visibility: visible;
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  transform: translateY(0);
}
.main-nav.style5 > ul > li {
  margin-left: 38px;
}
.main-nav.style5 > ul > li > a {
  font-family: 'Montserrat', sans-serif;
  font-weight: 400;
  font-size: 13px;
  letter-spacing: 2px;
}
.main-nav.style5 > ul > li > a .items-counter.style2 {
  top: 0;
}
.main-nav.style5 .shopping-menu > li {
  margin-left: 15px;
}
.main-nav.style5 .shopping-menu > li > a {
  display: inline-block;
  width: 39px;
  height: 39px;
  padding: 0 !important;
  text-align: center;
  background: none;
  border: 1px solid white;
  -webkit-border-radius: 50em;
  -moz-border-radius: 50em;
  border-radius: 50em;
}
.main-nav.style5 .shopping-menu > li > a i {
  line-height: 2em;
}
.main-nav.style5 .shopping-menu > li > a .items-counter.style2 {
  top: -20%;
}
.main-nav.style5 .shopping-menu > li:hover > a {
  background: #ff4d4d;
  border-color: #ff4d4d;
}
.main-nav.style5 .shopping-menu > li .basket-icon {
  margin-right: 0;
}
.main-nav.style5 .shopping-menu > li:first-child {
  margin-left: 38px;
}
.main-nav.red-active > ul > li:not(.icon):hover > a,
.main-nav.red-active > ul li.active > a {
  color: #ff4d4d !important;
}
.main-nav.active-style1 > ul > li.active > a {
  font-weight: 700;
}
.main-nav.active-style2 > ul > li:not(.icon) > a {
  position: relative;
}
.main-nav.active-style2 > ul > li:not(.icon) > a:after {
  content: '';
  display: inline-block;
  width: 100%;
  height: 1px;
  position: absolute;
  bottom: 37%;
  left: 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(4px);
  -ms-transform: translateY(4px);
  -webkit-transform: translateY(4px);
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
  pointer-events: none;
}
.main-nav.active-style2 > ul > li:hover:not(.icon) > a:after {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  -ms-transform: translateY(0);
  -webkit-transform: translateY(0);
}
.main-nav.active-style2 > ul > li.active:not(.icon) > a:after,
.main-nav.active-style2 > ul > li.open:not(.icon) > a:after {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  -ms-transform: translateY(0);
  -webkit-transform: translateY(0);
}
.main-nav.active-style3 > ul > li.active:not(.icon) > a,
.main-nav.active-style3 > ul > li.open:not(.icon) > a,
.main-nav.active-style3 > ul > li:hover:not(.icon) > a {
  position: relative;
}
.main-nav.active-style3 > ul > li.active:not(.icon) > a:after,
.main-nav.active-style3 > ul > li.open:not(.icon) > a:after,
.main-nav.active-style3 > ul > li:hover:not(.icon) > a:after {
  content: '';
  display: inline-block;
  width: 100%;
  height: 3px;
  background: white;
  position: absolute;
  bottom: 0;
  left: 0;
}
.main-nav.active-style4 > ul > li.active:not(.icon) > a,
.main-nav.active-style4 > ul > li.open:not(.icon) > a,
.main-nav.active-style4 > ul > li:hover:not(.icon):not(.shifter-handle) > a {
  position: relative;
}
.main-nav.active-style4 > ul > li.active:not(.icon) > a:after,
.main-nav.active-style4 > ul > li.open:not(.icon) > a:after,
.main-nav.active-style4 > ul > li:hover:not(.icon):not(.shifter-handle) > a:after {
  content: '';
  display: inline-block;
  width: 115%;
  min-width: 60px;
  height: 5px;
  background: white;
  position: absolute;
  top: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  transform: translateX(-50%);
}
.main-nav.active-style4.red-active > ul > li.active:not(.icon) > a,
.main-nav.active-style4.red-active > ul > li.open:not(.icon) > a,
.main-nav.active-style4.red-active > ul > li:hover:not(.shifter-handle):not(.icon) > a {
  color: #ff4d4d;
}
.main-nav.active-style4.red-active > ul > li.active:not(.icon) > a:after,
.main-nav.active-style4.red-active > ul > li.open:not(.icon) > a:after,
.main-nav.active-style4.red-active > ul > li:hover:not(.shifter-handle):not(.icon) > a:after {
  background: #ff4d4d;
}
.main-nav.shopping-menu > ul > li > a {
  font-weight: 300;
}
.main-nav.shopping-menu.shopping-menu-style1 ul li.icon {
  margin-left: 38px;
}
.main-nav.shopping-menu.shopping-menu-style1 ul li.icon a {
  text-transform: none;
}
.main-nav.shopping-menu.shopping-menu-style1 ul li.icon a > span {
  margin-right: 4px;
  position: relative;
  top: -3px;
}
.main-nav.shopping-menu.shopping-menu-style1 ul li.icon a .price:before {
  padding-right: 8px;
  content: '-';
}
.main-nav.align-center ul li {
  float: none;
  display: inline-block;
}
.main-nav.align-center ul li:first-child {
  margin-left: 0;
}
/*************************************************************
.............. 3.6. HEADERS DIFFERENT STYLES ...............
*************************************************************/
.main-header.style1 .main-header-inner {
  padding-right: 90px;
  padding-left: 90px;
  -webkit-transition: 0.35s all 0.001s ease-out;
  -moz-transition: 0.35s all 0.001s ease-out;
  transition: 0.35s all 0.001s ease-out;
}
.main-header.style2 .main-bar.style2 {
  padding-left: 15px;
  padding-right: 15px;
}
.main-header.style2 .main-bar.style2 > .container {
  width: 100%;
}
.main-header.style3 .main-bar {
  border-bottom: 1px solid rgba(255, 255, 255, 0.5);
}
.main-header.style3 .main-bar.transparent-bg {
  border-bottom-color: rgba(255, 255, 255, 0.2);
}
.main-header.style3 .main-bar .menu-container {
  float: none;
  padding: 0;
}
.main-header.style3 .main-bar .menu-container > div {
  float: none;
}
.main-header.style3 .main-bar .menu-container .logo-container {
  text-align: center;
}
.main-header.style3 .main-bar .menu-container .left-menu {
  padding-right: 18px;
}
.main-header.style3 .main-bar .menu-container .left-menu > ul {
  float: right;
}
.main-header.style3 .main-bar .menu-container .left-menu > ul > li {
  margin-left: 28px;
}
.main-header.style3 .main-bar .menu-container .right-menu {
  padding-left: 18px;
}
.main-header.style3 .main-bar .menu-container .right-menu > ul > li {
  margin-left: 0;
  margin-right: 26px;
}
.main-header.style3 .main-bar .menu-container .right-menu > ul > li:first-child {
  margin-left: 0;
}
.main-header.style3 .main-bar .menu-container .right-menu > ul .icons {
  float: right;
  margin-right: 10px;
}
.main-header.style3 .main-bar .menu-container .right-menu > ul .icons li {
  margin-left: 31px;
}
.main-header.style3 .main-bar .menu-container .right-menu.short-spacing > ul > li {
  margin-right: 21px;
}
.main-header.style3 .main-bar .menu-container .right-menu.short-spacing > ul > li:last-child {
  margin-right: 0;
}
.main-header.style3 .main-bar .menu-container .right-menu.short-spacing > ul .icons li:first-child {
  margin-left: 0;
}
.main-header.style4 .left-menu {
  order: -1;
}
.main-header.style4 .logo-container {
  order: 0;
}
.main-header.style4 .right-menu {
  order: 1;
}
.main-header.style4 .white-bg + .white-bg {
  border-top: none;
}
.main-header.style4 .main-bar .logo-container {
  text-align: center;
}
.main-header.style4 .main-bar .right-menu .main-nav {
  float: right;
}
.main-header.style4 .main-bar .options li {
  float: left;
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  margin-right: 12px;
}
.main-header.style4 .main-bar .options li a {
  display: inline-block;
  padding: 8px 20px 9px;
  color: #444444;
  border: 1px solid #ccc;
}
.main-header.style4 .main-bar .options li i {
  font-size: 1.4em;
  margin-right: 11px;
  position: relative;
  top: 4px;
}
.main-header.style4 .bottom-bar {
  border-top: none !important;
}
.main-header.style4 .bottom-bar .menu-container {
  /*border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;*/
}
.main-header.style4 .bottom-bar.transparent-bg .menu-container {
  border-top-color: rgba(238, 238, 238, 0.5) !important;
  border-bottom-color: rgba(238, 238, 238, 0.5) !important;
}
.main-header.style4 .bottom-bar .main-nav > ul > li > .dropdown-menu {
  top: calc(100% - 1px);
}
.main-header.with-shadow {
  box-shadow: 0 1px 1px #ddd;
}
.transparent-menu .header-banner {
  position: relative;
  overflow: hidden;
}
.transparent-menu .header-banner:after {
  content: '';
  display: inline-block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: #222222;
  opacity: 0.2;
  z-index: 0;
}
.transparent-menu .header-banner > .container {
  position: relative;
  z-index: 2;
}

/* TOP NAV */
.main-header .nav > li > ul , .sticky-main-nav .nav > li > ul {
	opacity: 0;
    visibility: hidden;
    padding: 0;
    background-color: rgb(250,250,250);
    text-align: left;
    position: absolute;
    top: 95px;
    margin-left: -90px;
    width:240px;
	-webkit-transition: all .3s .1s;
	-moz-transition: all .3s .1s;
	-o-transition: all .3s .1s;
	transition: all .3s .1s;
	-webkit-box-shadow: 0px 1px 3px rgba(0,0,0,.4);
	-moz-box-shadow: 0px 1px 3px rgba(0,0,0,.4);
	box-shadow: 0px 1px 3px rgba(0,0,0,.4);
}
.main-header .nav > li:hover > ul , .sticky-main-nav .nav > li:hover > ul {
    opacity: 1;
    top: 105px;
    visibility: visible;
}
@media screen and (max-width: 1450px) {
	.main-header .nav > li > ul , .sticky-main-nav .nav > li > ul {
		top: 0px;
	    margin-left: -90px;
	}
	.main-header .nav > li:hover > ul , .sticky-main-nav .nav > li:hover > ul {
	 	top: 90px;
	    margin-left: -90px;
	}
}
.sticky-main-nav .nav > li > ul {
	 top: 55px;
    margin-left: -140px;
}
.sticky-main-nav .nav > li:hover > ul {
 	top: 65px;
    margin-left: -140px;
}


.main-header .nav > li > ul:before , .sticky-main-nav .nav > li > ul:before{
    content: '';
    display: block;
    border-color: transparent transparent #ebebeb transparent;
    border-style: solid;
    border-width: 10px;
    position: absolute;
    top: -20px;
    left: 50%;
    margin-left: -10px;
}
.main-header .nav ul > li , .sticky-main-nav .nav ul > li { position: relative;}
.main-header .nav ul a , .sticky-main-nav .nav ul a {
    color: rgb(50,50,50);
    padding: 5px 8px 7px 16px;
    display: block;
	-webkit-transition: background-color .1s;
	-moz-transition: background-color .1s;
	-o-transition: background-color .1s;
	transition: background-color .1s;
}
.main-header .nav ul a:hover , .sticky-main-nav .nav ul a:hover {
	background-color: rgb(194,49,35);
	color:rgb(255,255,255) !important;
}


.main-header .nav li ul li , .sticky-main-nav .nav li ul li {
	font-size: .9em;
	float: none;
	padding: 4px;
	background: #ebebeb;
	border: none;
	line-height: 20px;
	margin-left:0;

}
.main-header .nav ul ul , .sticky-main-nav .nav ul ul {
    visibility: hidden;
    opacity: 0;
    position: absolute;
    top: 0;
    left: -150px;
    padding: 0;
    text-align: left;
    width: 160px;
	-webkit-transition: all .3s;
   	-moz-transition: all .3s;
	-o-transition: all .3s;
	transition: all .3s;
	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;
	-webkit-box-shadow: 0px 1px 3px rgba(0,0,0,.4);
	-moz-box-shadow: 0px 1px 3px rgba(0,0,0,.4);
	box-shadow: 0px 1px 3px rgba(0,0,0,.4);
}

.main-header .nav ul > li:hover > ul , .sticky-main-nav .nav ul > li:hover > ul {
	opacity: 1;
	left: -160px;
	visibility: visible;
}

.main-header .nav ul a:hover , .sticky-main-nav .nav ul a:hover {
	background-color: rgb(194,49,35);
	color:rgb(255,255,255) !important;
}

/*************************************************************
.......... 3.7. OFFCANVAS MENU & MOBILE NAVIGATION ...........
*************************************************************/
.main-nav.offcanvas-menu {
  padding-bottom: 40px;
}
.main-nav.offcanvas-menu ul {
  padding: 73px 43px 0;
  margin-bottom: 18px;
}
.main-nav.offcanvas-menu.mobile-nav {
  padding-top: 21px;
}
.main-nav.offcanvas-menu.mobile-nav .logo-container {
  padding-left: 16px;
  margin-top: 21px;
  margin-bottom: 7px;
}
.main-nav.offcanvas-menu.mobile-nav ul {
  padding: 0 26px;
  margin: 0;
}
.main-nav.offcanvas-menu.mobile-nav ul + ul {
  padding-top: 0;
  border-top: 1px solid #444;
}
.main-nav.offcanvas-menu.mobile-nav.white-bg ul + ul {
  border-color: #ddd;
}
.main-nav.offcanvas-menu.mobile-nav ul li .sub-menu {
  opacity: 1;
  visibility: visible;
  width: 100%;
  position: relative;
  top: auto !important;
  bottom: auto !important;
  left: auto !important;
  right: auto !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  -webkit-transform: translateX(0) translateY(0) !important;
  -ms-transform:   translateX(0) translateY(0) !important;
  transform:     translateX(0) translateY(0) !important;
  -webkit-transition: none;
  -moz-transition:    none;
  transition:         none;
}
.main-nav.offcanvas-menu.mobile-nav ul li > .sub-menu li {
  padding-left: 0 !important;
  padding-right: 0 !important;
}
.main-nav.offcanvas-menu.mobile-nav ul li .sub-menu .menu-column + .menu-column {
  border-top: 1px solid #444;
}
.main-nav.offcanvas-menu.mobile-nav.white-bg ul li .sub-menu .menu-column + .menu-column {
  border-top-color: #ddd;
}
.main-nav.offcanvas-menu.mobile-nav ul li.mega-menu.multi-column .sub-menu .menu-column ul.arrow-list li:not(.column-title) a,
.main-nav.offcanvas-menu.mobile-nav ul li.mega-menu.full-width .sub-menu .menu-column ul.arrow-list li:not(.column-title) a{
  padding: 17px 14px 16px 27px;
}
.main-nav.offcanvas-menu.mobile-nav ul li.mega-menu.multi-column .sub-menu .menu-column ul.arrow-list li:not(.column-title) a:before,
.main-nav.offcanvas-menu.mobile-nav ul li.mega-menu.full-width .sub-menu .menu-column ul.arrow-list li:not(.column-title) a:before{
  content: none;
}
.main-nav.offcanvas-menu ul > li {
  float: none;
  margin-left: 0;
  opacity: 0;
  -webkit-transition: 0.15s all 0.001s ease-out;
  -moz-transition: 0.15s all 0.001s ease-out;
  transition: 0.15s all 0.001s ease-out;
}
.main-nav.offcanvas-menu > ul > li > a,
.main-nav.offcanvas-menu > ul > li > .sub-menu > li > a,
.main-nav.offcanvas-menu > ul > li > .sub-menu > li > .sub-menu > li > a,
.main-nav.offcanvas-menu > ul > li .sub-menu .menu-column > ul > li > a {
  font-family: 'Montserrat', sans-serif;
  font-weight: 400;
  display: block;
  padding: 17px 14px 16px 18px;
  line-height: 1.5em;
  text-transform: uppercase;
  border-bottom: 1px solid #444444;
  position: relative;
}
.main-nav.offcanvas-menu ul li .sub-menu a {
  text-transform: none !important;
}
.main-nav.offcanvas-menu.iconic-items ul > li > a {
  padding-left: 39px;
}
.main-nav.offcanvas-menu ul > li > a > i {
  font-size: 1.6em;
}
.main-nav.offcanvas-menu > ul > li > a > i {
  position: absolute;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
  -ms-transform:     translateY(-50%);
  transform:         translateY(-50%);
}
.main-nav.offcanvas-menu > ul > li:last-child a {
  border-bottom: none;
}
.main-nav.offcanvas-menu ul li:last-child .sub-menu {
  border-top: 1px solid #ddd !important;
}
.main-nav.offcanvas-menu.dark-bg ul li:last-child .sub-menu {
  border-top: 1px solid #444 !important;
}
.main-nav.offcanvas-menu ul li .sub-menu .menu-column a {
  border-bottom: 1px solid #ddd;
}
.main-nav.offcanvas-menu.dark-bg ul li .sub-menu .menu-column a {
  border-color: #444;
}
/*.main-nav.offcanvas-menu ul > li:hover > a,
.main-nav.offcanvas-menu ul > li.active > a {
  font-weight: 600;
}*/
.main-nav.offcanvas-menu ul li .sub-menu li.has-icon a > i,
.main-nav.offcanvas-menu.fullwidth-items > ul > li.has-icon > a > i,
.main-nav.offcanvas-menu.fullwidth-menu > ul > li.has-icon > a > i {
  margin-right: 11px;
  position: relative;
  top: 4px;
}
.offcanvas-menu-right .main-nav.offcanvas-menu ul > li,
.offcanvas-menu-right .main-nav.offcanvas-menu .logo-container,
.offcanvas-menu-right .main-nav.offcanvas-menu .search-form {
  transform: translateX(7%);
  -ms-transform: translateX(7%);
  -webkit-transform: translateX(7%);
}
.offcanvas-menu-left .main-nav.offcanvas-menu ul > li,
.offcanvas-menu-left .main-nav.offcanvas-menu .logo-container,
.offcanvas-menu-left .main-nav.offcanvas-menu .search-form {
  transform: translateX(-7%);
  -ms-transform: translateX(-7%);
  -webkit-transform: translateX(-7%);
}
.main-nav.offcanvas-menu.dark-bg {
  background: #222222;
}
.main-nav.offcanvas-menu.dark-bg ul li a {
  color: white;
}
.main-nav.offcanvas-menu.white-bg {
  background: white;
}
.main-nav.offcanvas-menu.white-bg ul li a {
  border-bottom-color: #ddd !important;
}
.main-nav.offcanvas-menu.white-bg > ul > li a {
  color: #444444 !important;
}
.main-nav.offcanvas-menu > ul > li .sub-menu {
  position: relative;
  top: auto;
  left: auto !important;
  float: none;
  margin: 0;
  padding: 0;
  border: 0;
  min-width: inherit;
  background: none !important;
  -webkit-box-shadow: none;
  box-shadow:         none;
}
.main-nav.offcanvas-menu > ul > li .sub-menu .container,
.main-nav.offcanvas-menu > ul > li .sub-menu .row {
  margin: 0;
  padding: 0;
}
.main-nav.offcanvas-menu > ul > li .sub-menu li > .container {
  width: auto;
}
.main-nav.offcanvas-menu > ul > li .sub-menu li .menu-column {
  float: none;
  padding: 0;
}
.main-nav.offcanvas-menu > ul > li .sub-menu li .menu-column ul {
  padding: 0;
  margin: 0;
}
.main-nav.offcanvas-menu.dark-bg > ul > li .sub-menu li a {
  background: #333;
}
.main-nav.offcanvas-menu.white-bg > ul > li .sub-menu li a {
  background: #f9f9f9;
}
.main-nav.offcanvas-menu.fullwidth-items > ul {
  padding-right: 0;
  padding-left: 0;
}
.main-nav.offcanvas-menu.fullwidth-items ul .dropdown > a {
  position: relative;
}
.main-nav.offcanvas-menu.fullwidth-items ul .dropdown > a:after {
  content: '';
  display: inline-block;
  width: 13px;
  height: 1px;
  background: #ccc;
  position: absolute;
  top: 50%;
  right: 14px;
  -webkit-transition: all 0.3s ease;
  -moz-transition:    all 0.3s ease;
  transition:         all 0.3s ease;
  -webkit-transform: translateY(-50%);
  -ms-transform:     translateY(-50%);
  transform:         translateY(-50%);
}
.main-nav.offcanvas-menu.fullwidth-items ul .dropdown > a:before {
  content: '';
  display: inline-block;
  width: 1px;
  height: 13px;
  background: #ccc;
  position: absolute;
  top: 50%;
  right: 20px;
  -webkit-transition: all 0.3s ease;
  -moz-transition:    all 0.3s ease;
  transition:         all 0.3s ease;
  -webkit-transform: translateY(-50%);
  -ms-transform:     translateY(-50%);
  transform:         translateY(-50%);
}
.main-nav.offcanvas-menu.fullwidth-items ul .dropdown.open > a:before {
  -webkit-transform: translateY(-50%) rotateZ(270deg);
  -ms-transform:     translateY(-50%) rotateZ(270deg);
  transform:         translateY(-50%) rotateZ(270deg);
}
.main-nav.offcanvas-menu.fullwidth-items.white-bg ul .dropdown > a:after,
.main-nav.offcanvas-menu.fullwidth-items.white-bg ul .dropdown > a:before {
  background: #666;
}
.main-nav.offcanvas-menu > ul li.icon {
  display: none;
}
.main-nav.offcanvas-menu.mobile-nav ul li .sub-menu {
  display: none;
}
.main-nav.offcanvas-menu .search-form {
  display: block;
  margin: 0 auto;
  position: relative;
}
.main-nav.offcanvas-menu.fullwidth-items .search-form {
  width: 240px;
}
.main-nav.offcanvas-menu .search-form {
  width: 216px;
  margin-top: 25px !important;
}
.main-nav.offcanvas-menu.offcanvas-menu-style2 .search-form {
  width: 240px;
}
.main-nav.offcanvas-menu .search-form input[type=search] {
  display: inline-block;
  width: 100%;
  height: 59px;
  padding: 16px 40px 16px 16px;
  margin: 0 auto;
  color: white;
  border: 1px solid #333;
  font-weight: 600;
  background: transparent;
  -webkit-transition: all 0.3s ease;
  -moz-transition:    all 0.3s ease;
  transition:         all 0.3s ease;
}
.main-nav.offcanvas-menu.white-bg .search-form input[type=search] {
  color: #444;
}
.main-nav.offcanvas-menu .search-form input[type=search]:active,
.main-nav.offcanvas-menu .search-form input[type=search]:focus {
  border-color: #666;
}
.main-nav.offcanvas-menu .search-form input[type=submit] {
  font-family: 'knight';
  font-size: 1.4em;
  text-align: center;
  background: transparent;
  border: none;
  color: #666;
  position: absolute;
  top: 0;
  right: 0;
  display: inline-block;
  width: 50px;
  height: 59px;
  -webkit-transition: all 0.3s ease;
  -moz-transition:    all 0.3s ease;
  transition:         all 0.3s ease;
}
.main-nav.offcanvas-menu .search-form input[type=submit]:hover {
  color: white;
}
.main-nav.offcanvas-menu.white-bg .search-form input[type=submit]:hover {
  color: #444;
}
.main-nav.offcanvas-menu .search-form input[type=search]:active,
.main-nav.offcanvas-menu .search-form input[type=search]:focus,
.main-nav.offcanvas-menu .search-form input[type=submit]:active,
.main-nav.offcanvas-menu .search-form input[type=submit]:focus {
  outline: none;
}
.main-nav.offcanvas-menu.mobile-nav.white-bg .search-form input[type=search] {
  border-color: #ddd;
  color: #444;
}
.main-nav.offcanvas-menu.mobile-nav.white-bg .search-form input[type=search]:focus {
  border-color: #bbb;
}
.main-nav.offcanvas-menu.mobile-nav.white-bg .search-form input[type=submit] {
  color: #444;
}
.main-nav.offcanvas-menu.mobile-nav.fullwidth-menu {
  width: 100%;
}
.main-nav.offcanvas-menu.mobile-nav.fullwidth-menu > ul > li > a,
.main-nav.offcanvas-menu.mobile-nav.fullwidth-menu > ul > li > .sub-menu li > a {
  padding: 15px 27px 16px;
}
.main-nav.offcanvas-menu.mobile-nav.fullwidth-menu .search-form {
  width: 100%;
  margin: 0;
  padding: 0 26px;
}
.main-nav.offcanvas-menu.mobile-nav.fullwidth-menu .search-form input[type=search] {
  width: 100%;
}
.main-nav.offcanvas-menu.mobile-nav.fullwidth-menu .search-form input[type=submit] {
  right: 26px;
}
body.offcanvas-menu-right.shifter-open .main-header.style1 .main-header-inner {
  padding-right: 27px;
}
body.shifter-open .main-nav.offcanvas-menu li,
body.shifter-open .main-nav.offcanvas-menu .logo-container,
body.shifter-open .main-nav.offcanvas-menu .search-form {
  opacity: 1;
  -webkit-transform: translateX(0) translateY(0);
  -ms-transform: translateX(0) translateY(0);
  transform: translateX(0) translateY(0);
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.main-nav.offcanvas-menu.offcanvas-menu-style2 {
  padding-top: 57px;
}
.main-nav.offcanvas-menu.offcanvas-menu-style2 ul,
.main-nav.offcanvas-menu.offcanvas-menu-style2 .sub-menu,
.main-nav.offcanvas-menu.offcanvas-menu-style2 .sub-menu li {
  padding: 0 !important;
  position: relative !important;
  top: auto !important;
  left: auto !important;
  bottom: auto !important;
  right: auto !important;
  -webkit-transform: translateY(0) translateX(0) !important;
  -ms-transform:   translateY(0) translateX(0) !important;
  transform:     translateY(0) translateX(0) !important;
  -webkit-transition: none;
  -moz-transition:    none;
  transition:         none;
}
.main-nav.offcanvas-menu.offcanvas-menu-style2 .logo-container{
  width: 215px;
  text-align: center;
  margin: 0 auto 26px;
}
.main-nav.offcanvas-menu.offcanvas-menu-style2 ul .sub-menu{
  opacity: 1;
  visibility: visible;
  padding: 0;
  display: none;
  -webkit-transition: none;
  -moz-transition:    none;
  transition:         none;
}
.main-nav.offcanvas-menu.offcanvas-menu-style2 ul .menu-column{
  width: 100% !important;
}
.main-nav.offcanvas-menu.offcanvas-menu-style2 ul .dropdown.open > a {
  font-weight: 700;
}
.main-nav.offcanvas-menu.offcanvas-menu-style2 ul .dropdown > a:after {
  content: '';
  display: inline-block;
  width: 13px;
  height: 1px;
  background: #ccc;
  position: absolute;
  top: 50%;
  right: 14px;
  -webkit-transition: all 0.3s ease;
  -moz-transition:    all 0.3s ease;
  transition:         all 0.3s ease;
  -webkit-transform: translateY(-50%);
  -ms-transform:     translateY(-50%);
  transform:         translateY(-50%);
}
.main-nav.offcanvas-menu.offcanvas-menu-style2 ul .dropdown > a:before {
  content: '';
  display: inline-block;
  width: 1px;
  height: 13px;
  background: #ccc;
  position: absolute;
  top: 50%;
  right: 20px;
  -webkit-transition: all 0.3s ease;
  -moz-transition:    all 0.3s ease;
  transition:         all 0.3s ease;
  -webkit-transform: translateY(-50%);
  -ms-transform:     translateY(-50%);
  transform:         translateY(-50%);
}
.main-nav.offcanvas-menu.offcanvas-menu-style2 ul .dropdown.open > a:before {
  -webkit-transform: translateY(-50%) rotateZ(270deg);
  -ms-transform:     translateY(-50%) rotateZ(270deg);
  transform:         translateY(-50%) rotateZ(270deg);
}
.main-nav.offcanvas-menu.offcanvas-menu-style2.white-bg ul .dropdown > a:after,
.main-nav.offcanvas-menu.offcanvas-menu-style2.white-bg ul .dropdown > a:before {
  background: #666;
}
.main-nav.offcanvas-menu.offcanvas-menu-style2 ul li li a {
  text-transform: none;
  padding-top: 15px;
  padding-bottom: 15px;
}

.main-nav.offcanvas-menu.mobile-nav ul li ul{
	margin-left:0px;
	padding-left:0 !important;
	padding-right:0 !important;
	background-color: #ddd !important;
}
.main-nav.offcanvas-menu.mobile-nav .nav > li > ul > li{
}
.main-nav.offcanvas-menu.mobile-nav ul ul ul{
	marging:0 0 0 -40px !important;
	background-color: #aaa !important;
}
.main-nav.offcanvas-menu.mobile-nav .nav li ul a {
	padding:10px !important;
	width:100%;
	padding-left:40px !important;
	border-bottom:1px #aaa solid !important;
	font-weight: 400;
}
.main-nav.offcanvas-menu.mobile-nav .nav > li > ul > li > ul li a{
	padding-left:80px !important;
	color:#fff !important;
}
/*************************************************************
.............. 3.8. OFFCANVAS MENU TRIGGER ...............
*************************************************************/
.shifter-handle {
  display: block;
  margin: 0;
  padding: 0;
  border: 0;
}
.shifter-handle:hover {
  background: none;
}
.shifter-handle.style1 {
  float: right;
  margin-top: 27px;
}
.shifter-handle.style1 .bars {
  display: inline-block;
  width: 25px;
  height: 20px;
  cursor: pointer;
}
.shifter-handle.style1 .bars span {
  display: inline-block;
  width: 25px;
  height: 3px;
  float: left;
  background: white;
  margin-top: 5px;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.shifter-handle.dark-handle .bars span {
  background: #222;
}
.shifter-handle.style1 .bars span:first-child {
  margin-top: 0;
}
.shifter-handle.style2 .bars {
  display: inline-block;
  width: 18px;
  height: 15px;
  cursor: pointer;
}
.shifter-handle.style2 .bars span {
  display: inline-block;
  width: 18px;
  height: 3px;
  float: left;
  background: white;
  margin-top: 3px;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.shifter-handle.style2 .bars span:first-child {
  margin-top: 0;
}
.shifter-handle.style3 .bars {
  display: inline-block;
  width: 20px;
  height: 16px;
  cursor: pointer;
  position: relative;
  bottom: 3px;
}
.shifter-handle.style3 .bars span {
  display: inline-block;
  width: 20px;
  height: 3px;
  float: left;
  background: white;
  margin-top: 3px;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
/*************************************************************
..................... 3.9. FIXED MENU ......................
*************************************************************/
body.fixed-nav {
  height: 100%;
}
body.fixed-nav .main-wrapper {
  height: 100%;
}
body.fixed-nav.fixed-nav-left {
  padding-left: 382px;
}
body.fixed-nav .fixed-menu {
  position: fixed;
  top: 0;
  bottom: 0;
  height: 100%;
  z-index: 120;
  min-width: 382px;
  max-width: 382px;
  padding-right: 50px;
  padding-left: 50px;
  text-align: center;
}
body.fixed-nav .fixed-menu.left {
  left: 0;
}
body.fixed-nav .fixed-menu .main-header-inner {
  height: 100%;
}
body.fixed-nav .fixed-menu .logo-container {
  float: none;
  margin-bottom: 71px;
}
body.fixed-nav .fixed-menu .menu-container {
  float: none;
  position: relative;
  top: auto;
  right: auto;
  transform: translateX(0);
  -ms-transform: translateX(0);
  -webkit-transform: translateX(0);
}
body.fixed-nav .fixed-menu .menu-container .main-nav {
  float: none;
}
body.fixed-nav .fixed-menu .menu-container .main-nav ul li {
  float: none;
  margin-bottom: 18px;
  margin-left: 0;
}
body.fixed-nav .fixed-menu .menu-container .main-nav ul li.open > a {
  font-weight: 700;
}
body.fixed-nav .fixed-menu .menu-container .main-nav ul li .sub-menu {
  background: none;
  float: none;
  width: 100%;
  position: relative;
  top: auto;
  left: auto !important;
  right: auto !important;
  bottom: auto;
  opacity: 1;
  visibility: visible;
  text-align: center;
  display: none;
  padding: 15px 0 0;
  -webkit-transition: none;
  -moz-transition:    none;
  transition:     none;
}
body.fixed-nav .fixed-menu .menu-container .main-nav ul li .sub-menu a {
  text-transform: none;
}
body.fixed-nav .fixed-menu .menu-container .main-nav ul li .sub-menu .sub-menu {
  padding: 0;
}
body.fixed-nav .fixed-menu .menu-container .main-nav ul li .sub-menu .dropdown > a:after {
  right: 0;
  -webkit-transition: all 0.3s ease;
  -moz-transition:    all 0.3s ease;
  transition:         all 0.3s ease;
}
body.fixed-nav .fixed-menu .menu-container .main-nav ul li .sub-menu .dropdown.open > a:after {
  -webkit-transform: rotateZ(90deg) translateY(-4px) translateX(-12px);
  -ms-transform:   rotateZ(90deg) translateY(-4px) translateX(-12px);
  transform:     rotateZ(90deg) translateY(-4px) translateX(-12px);
}
body.fixed-nav .fixed-menu .menu-container .main-nav ul li .sub-menu li,
body.fixed-nav .fixed-menu .menu-container .main-nav ul li .sub-menu li a {
  margin: 0;
  padding-right: 0;
  padding-left: 0;
  min-width: inherit;
}
body.fixed-nav .fixed-menu .menu-container .main-nav ul li .sub-menu li a {
  border: none;
}
body.fixed-nav .fixed-menu .bottom-sec {
  margin-top: 210px;
  display: inline-block;
  width: 100%;
}
body.fixed-nav .fixed-menu .bottom-sec .copyright p {
  font-weight: 400;
  margin-bottom: 3px;
  color: #666;
}
body.fixed-nav .fixed-menu .bottom-sec .socials {
  margin-top: 14px;
}
body.fixed-nav .fixed-menu .bottom-sec .socials li {
  float: none;
  display: inline-block;
  margin-right: 9px;
}
body.fixed-nav .fixed-menu .bottom-sec .socials li a {
  color: #666;
}
body.fixed-nav .fixed-menu .bottom-sec .socials li a i {
  font-size: 1.1em;
}
/*************************************************************
.................... 3.10. HEADER BANNER .....................
*************************************************************/
.header-banner {
  position: relative;
  min-height: 425px;
  color: white;
}
.header-banner.tall {
  min-height: 610px;
}
.header-banner.taller {
  min-height: 900px;
}
.header-banner.short {
  min-height: 255px;
}
.header-banner.header-banner-boxed {
  padding-left: 70px;
}
.header-banner.header-banner-boxed p {
  font-size: 18px;
  line-height: 1.6em;
  width: 60%;
}
.header-banner.header-banner-boxed .button {
  margin-top: 10px;
}
/*************************************************************
.................... 3.11. HEADER SLIDER .....................
*************************************************************/
.header-slider .tp-banner-container {
  width: 100%;
  position: relative;
  padding: 0;
}
.header-slider .tp-banner {
  width: 100%;
  position: relative;
}
/*************************************************************
................. 3.12. HEADER TILTLE BAR ..................
*************************************************************/

/*---------- CUSTOM HEADINGS ----------*/
/*BizFunctional*/
.heading-alt-style9 a {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.2em;
  color:#c93827;
}
.heading-alt-style12 {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
}
/* BizFunctional */
.heading-alt-styleBF {
  font-family: 'Montserrat', sans-serif;
  font-size: 32px;
  font-weight: normal;
  text-transform: none;
  margin-top: 120px;
  margin-bottom: 0px;
}
.heading-alt-style2BF {
  font-family: 'Droid Sans', sans-serif;
  font-size: 20px;
  font-weight: normal;
  text-transform: none;
  color: darkgray;

}
.heading-alt-style3BF {
  font-family: 'Montserrat', sans-serif;
  font-size: 32px;
  font-weight: normal;
  text-transform: none;
  margin-top: 40px;
  margin-bottom: 0px;
}
.heading-alt-style4BF {
  font-family: 'Droid Sans', sans-serif;
  font-size: 20px;
  font-weight: normal;
  text-transform: none;
  color: gray;

}
.heading-alt-styleBF-small {
  font-family: 'Montserrat', sans-serif, serif;
  font-size: 32px;
  font-weight: normal;
  text-transform: none;
  margin-top: 20px;
  margin-bottom: 0px;
  text-align:center;
}
.heading-alt-style2BF-small {
  font-family: 'Droid Sans', sans-serif;
  font-size: 20px;
  font-weight: normal;
  text-transform: none;
  color: darkgray;
  text-align:center;
}
.heading-alt-style3BF-small {
  font-family: 'Montserrat', sans-serif, serif;
  font-size: 32px;
  font-weight: normal;
  text-transform: none;
  margin-top: 20px;
  margin-bottom: 0px;
  text-align:center;
}
.heading-alt-style4BF-small {
  font-family: 'Droid Sans', sans-serif;
  font-size: 20px;
  font-weight: normal;
  text-transform: none;
  text-align:center;
}

.footer-copyright{
  font-family: 'Droid Sans', sans-serif;
  font-size: 14px;
  font-weight: 300;
  text-transform:none;
}
.title-bar {
  background-position: center center;
  background-size: cover;
  text-align: center;
  color: black;
}
/* BizFunctional */
.title-barBF {
  background-position: center center;
  background-size: cover;
  text-align: center;
  color: black;
  margin-bottom:30px;
}
.title-barBF-imageless {
    height:80px;
    background-color:#060e43;
}
.home-desc {
    font-size: 1.5em;

}
.title-bar h6 {
  margin-top: 75px;
  margin-bottom: 34px;
}
.title-bar h1 {
  margin-bottom: 80px;
}
.title-bar .breadcrumb {
  background: none;
  margin: 0;
  padding: 0;
  margin-bottom: 80px;
}
.title-bar .breadcrumb li {
  display: inline-block;
}
.title-bar .breadcrumb li:before {
  padding: 0;
}
.title-bar .breadcrumb li a {
  color: white;
}
.title-bar .breadcrumb li.active {
  font-weight: 500;
  color: white;
}
.title-bar .breadcrumb li.active:before {
  font-weight: 400;
}
.title-bar.title-bar-style2 h6 {
  margin-top: 100px;
  margin-bottom: 20px;
}
.title-bar.title-bar-style2 h1 {
  margin-bottom: 106px;
}
.title-bar.title-bar-style3 h6 {
  margin-top: 95px;
  margin-bottom: 15px;
}
.title-bar.title-bar-style3 h1 {
  margin-bottom: 10px;
}
.title-bar.title-bar-style4 {
  color: #444;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}
.title-bar.title-bar-style4 h6 {
  margin-top: 65px;
  margin-bottom: 5px;
}
.title-bar.title-bar-style4 .breadcrumb {
  margin-bottom: 65px;
}
.title-bar.title-bar-style4 .breadcrumb li:before {
  color: #444;
}
.title-bar.title-bar-style4 .breadcrumb li a {
  color: #444;
}
.title-bar.title-bar-style4 .breadcrumb li a:hover {
  color: #ff4d4d;
}
.title-bar.title-bar-style4 .breadcrumb li.active {
  color: #444;
  font-weight: 600;
}
.title-bar.title-bar-style4 .breadcrumb li.active:before {
  font-weight: 300;
}
/*************************************************************
................. 3.13. MOBILE HEADER ..................
*************************************************************/
.mobile-header-wrapper{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 99999;
}
.mobile-header-wrapper.style1 .logo-container{
  float: left;
}
.mobile-header-wrapper.style2 {
  position: fixed;
}
.mobile-header-wrapper.style2 .shifter-handle {
  margin: 0;
  position: absolute;
  top: 53%;
  left: 25px;
  -webkit-transform: translateY(-50%);
  -ms-transform:     translateY(-50%);
  transform:         translateY(-50%);
}
.mobile-header-wrapper.style2.white-bg .shifter-handle span{
  background: #444;
}
.mobile-header-wrapper.style2 .logo-container {
  float: none;
  display: block;
  width: 150px;
  margin: 0 auto;
  text-align: center;
}
.mobile-header-wrapper.style2 .search-form-trigger {
  font-size: 1.4em;
  position: absolute;
  top: 52%;
  right: 80px;
  -webkit-transform: translateY(-50%);
  -ms-transform:     translateY(-50%);
  transform:         translateY(-50%);
}

/*---------- BizFunctional ----------*/
.mobile-header-wrapper.style2 .cart-link {
  font-size: 1.4em;
  position: absolute;
  top: 52%;
  right: 50px;
  -webkit-transform: translateY(-50%);
  -ms-transform:     translateY(-50%);
  transform:         translateY(-50%);
}
.mobile-header-wrapper.style2 .contact-link {
  font-size: 1.4em;
  position: absolute;
  top: 52%;
  right: 20px;
  -webkit-transform: translateY(-50%);
  -ms-transform:     translateY(-50%);
  transform:         translateY(-50%);
}
.mobile-header-wrapper.style2 .login-link {
  font-size: 1.4em;
  position: absolute;
  top: 40%;
  right: 20px;
  -webkit-transform: translateY(-50%);
  -ms-transform:     translateY(-50%);
  transform:         translateY(-50%);
}
.mobile-header-wrapper.style2.dark-bg .search-form-trigger {
  color: white;
}
.mobile-header-wrapper.style2 .search-form-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  display: none;
}
.mobile-header-wrapper.style2 .search-form-wrapper form {
  display: inline-block;
  width: 100%;
  height: 100%;
}
.mobile-header-wrapper.style2 .search-form-wrapper form input[type=search] {
  display: inline-block;
  width: 100%;
  height: 100%;
  border: none;
  font-size: 2em;
  line-height: 1.5em;
  padding: 40px;
}
.mobile-header-wrapper.style2 .search-form-wrapper form input[type=search]:focus,
.mobile-header-wrapper.style2 .search-form-wrapper form input[type=search]:active {
  outline: none;
}
.mobile-header-wrapper.style2.white-bg .search-form-wrapper form input[type=search] {
  background: white;
}
.mobile-header-wrapper.style2 .search-form-wrapper .search-form-close-trigger {
  font-size: 2em;
  line-height: 1em;
  display: inline-block;
  position: absolute;
  top: 52%;
  right: 24px;
  cursor: pointer;
  padding-top: 5px;
  -webkit-transform: translateY(-50%);
  -ms-transform:     translateY(-50%);
  transform:         translateY(-50%);
}
.shifter-enabled.mobile-header-style2 .main-nav.offcanvas-menu.mobile-nav {
  margin-top: 113px;
  padding-top: 0;
}
.mobile-header-style2 .main-nav.offcanvas-menu {
  z-index: 2;
  -webkit-transform: translate3D(-100%, 0, 0);
  -ms-transform: translate3D(-100%, 0, 0);
  transform: translate3D(-100%, 0, 0);
}
.mobile-header-style2.offcanvas-menu-right .main-nav.offcanvas-menu {
  -webkit-transform: translate3D(100%, 0, 0);
  -ms-transform: translate3D(100%, 0, 0);
  transform: translate3D(100%, 0, 0);
}
.mobile-header-style2.shifter-open .main-nav.offcanvas-menu {
  -webkit-transform: translate3D(0, 0, 0);
  -ms-transform: translate3D(0, 0, 0);
  transform: translate3D(0, 0, 0);
}
/*************************************************************
................. 3.14. STICKY HEADER ..................
*************************************************************/
.sticky-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 10;
  background: white;
  opacity: 0;
  visibility: hidden;
  -webkit-transform: translateY(-100%);
  -ms-transform:     translateY(-100%);
  transform:         translateY(-100%);
  -webkit-transition: all 0.6s ease;
  -moz-transition:    all 0.6s ease;
  transition:         all 0.6s ease;
}
.sticky-menu.slideDown {
  opacity: 1;
  visibility: visible;
  -webkit-transform: translateY(0);
  -ms-transform:     translateY(0);
  transform:         translateY(0);
}
.sticky-menu > .container {
  display: table;
  height: 100%;
}
.sticky-menu > .container > div {
  display: table-cell;
  height: 100%;
  float: none;
  vertical-align: middle;
}
.sticky-menu .shifter-handle {
  text-align: right;
}
.sticky-menu .shifter-handle .bars {
  display: table-cell;
  height: 100%;
}
.sticky-menu .sticky-logo-container {
  padding: 18px 25px 20px 20px;
  vertical-align:middle;
}
.sticky-menu .sticky-logo-container img {
  max-height: 100px;
}
/* BizFunctional */
.sticky-menu .main-nav{
  float: right;
  height: 100%;
  padding-top: 0px;
  padding-right: 20px;
}
.sticky-menu .main-nav ul .mega-menu.multi-column > .sub-menu{
  left: auto !important;
}
.sticky-menu .main-nav > ul,
.sticky-menu .main-nav > ul > li,
.sticky-menu .main-nav > ul > li > a,
.sticky-menu .main-nav > ul .shopping-menu,
.sticky-menu .main-nav > ul .shopping-menu > li,
.sticky-menu .main-nav > ul .shopping-menu > li > a {
  height: 100%;
}
.sticky-menu .main-nav > ul .shopping-menu,
.sticky-menu .main-nav > ul {
  float: left;
  display: table-cell;
}
.sticky-menu .main-nav > ul .shopping-menu,
.sticky-menu .main-nav > ul .shopping-menu > li,
.sticky-menu .main-nav > ul > li {
  display: table;
  height: 100%;
}
.sticky-menu .main-nav > ul .shopping-menu > li > a,
.sticky-menu .main-nav > ul > li > a {
  display: table-cell;
  vertical-align: middle;
  padding-top: 30px;
  padding-bottom: 0px;
}
.sticky-menu .main-nav .icons > ul,
.sticky-menu .main-nav .icons > ul > li,
.sticky-menu .main-nav .icons > ul > li > a {
  height: 100%;
}
.sticky-menu .main-nav .icons > ul > li {
  display: table;
}
.sticky-menu .main-nav .icons > ul > li > a {
  display: table-cell;
  vertical-align: middle;
}
.sticky-menu .main-nav > ul > li:not(.icon) > a > i {
  display: none;
}
.sticky-menu .main-nav .icons > ul:before,
.sticky-menu .main-nav .icons > ul:after {
  content: " ";
  display: table;
}
.sticky-menu .main-nav .icons > ul:after {
  clear: both;
}
.sticky-menu .main-nav .socials,
.sticky-menu .shifter-handle,
.sticky-menu .main-nav .expandable-search-form,
.sticky-menu .main-nav .icon-cart .cart-quantity .items-counter.style1,
.sticky-menu .main-nav .icon-cart .price {
  display: none;
}
.sticky-menu .main-nav ul li.icon-cart{
  float: right;
}
.sticky-menu .main-nav ul li.icon-cart .basket-icon {
  margin-right: 0;
}
.sticky-dark .sticky-menu{
  background: rgba(34, 34, 34, 0.9);
}
.sticky-dark .sticky-menu .main-nav ul li a {
  color: white;
}
.sticky-dark .sticky-menu .main-nav ul li:not(.icon):not(.icons):hover > a,
.sticky-dark .sticky-menu .main-nav ul li:not(.icon):not(.icons).active > a{
  color: #ff4d4d;
}
.sticky-light .sticky-menu {
  background: white;
}
.shifter-open .sticky-menu{
  -webkit-transform: translateX(-300px);
  -ms-transform:   translateX(-300px);
  transform:     translateX(-300px);
}
.shifter-left.shifter-open .sticky-menu{
  -webkit-transform: translateX(300px);
  -ms-transform:   translateX(300px);
  transform:     translateX(300px);
}

/*************************************************************
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
..................... 4. MAIN SIDEBAR ......................
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
*************************************************************/
.main-sidebar .widget {
  margin-bottom: 43px;
}
.main-sidebar .widget .widget-title {
  background: #f9f9f9;
  border: 1px solid #eee;
  padding: 19px 27px 22px;
  margin-bottom: 35px;
}
.main-sidebar .widget.widget-categories ul li {
  margin-bottom: 15px;
}
.main-sidebar .widget.widget-categories ul li a {
  color: #444444;
  display: block;
  width: 100%;
  position: relative;
}
.main-sidebar .widget.widget-categories ul li a:after {
  content: '\e120';
  font-family: 'knight';
  font-size: 1em;
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
}
.main-sidebar .widget.widget-product .product {
  margin-bottom: 46px;
}
.main-sidebar .widget.widget-product .product .product-preview {
  display: inline-block;
  width: 80px;
  height: 80px;
  float: left;
  margin-right: 5px;
}
.main-sidebar .widget.widget-product .product .product-details {
  overflow: hidden;
}
.main-sidebar .widget.widget-product .product .product-details .product-info h6 {
  margin-bottom: 12px;
}
.main-sidebar .widget.widget-product .product .product-details .product-info .product-rating {
  margin-bottom: 10px;
}
.main-sidebar .widget.widget-product .product .product-details .product-price {
  text-align: left;
}
.main-sidebar .widget.widget-product .product .product-details .product-price p {
  font-weight: 400;
  margin-bottom: 0;
}
.main-sidebar .widget.widget-instagram ul {
  margin-right: -10px;
}
.main-sidebar .widget.widget-instagram ul li {
  display: inline-block;
  width: 83px;
  height: 83px;
  margin-right: 10px;
  margin-bottom: 10px;
}
.main-sidebar .widget.widget-subscribe .subscribe-form .subscribe-email {
  width: 100%;
  border: 0px none;
  padding: 17px 16px;
  margin-bottom: 27px;
}
.main-sidebar .widget.widget-subscribe .subscribe-form ::-webkit-input-placeholder {
  color: #444444;
}
.main-sidebar .widget.widget-subscribe .subscribe-form :-moz-placeholder {
  color: #444444;
}
.main-sidebar .widget.widget-subscribe .subscribe-form ::-moz-placeholder {
  color: #444444;
}
.main-sidebar .widget.widget-subscribe .subscribe-form :-ms-input-placeholder {
  color: #444444;
}
.sidebar-left .post-container {
  float: right !important;
}
.sidebar-right .post-container {
  float: left !important;
}
.no-sidebar .main-sidebar {
  display: none;
}
.no-sidebar .post-container {
  width: 100% !important;
}

/*************************************************************
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
..................... 5. BLOG ......................
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
*************************************************************/
.post-container .post {
  border-bottom: 1px solid #ddd;
  margin-bottom: 75px;
  padding-bottom: 67px;
}
.post-container .post:last-child {
  border-bottom: none;
}
.post-container .post .entry-image {
  margin-bottom: 58px;
  position: relative;
}
.post-container .post .entry-image .owl-controls {
  position: absolute;
  bottom: 26px;
  left: 50%;
  transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
}
.post-container .post .entry-image .owl-controls .owl-dots .owl-dot {
  display: inline-block;
  width: 11px;
  height: 11px;
  margin-right: 11px;
  background: rgba(255, 255, 255, 0.5);
  -webkit-border-radius: 50em;
  -moz-border-radius: 50em;
  border-radius: 50em;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.post-container .post .entry-image .owl-controls .owl-dots .owl-dot.active {
  background: white;
}
.post-container .post .video-container {
  margin-bottom: 60px;
}
.post-container .post .entry-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 30px;
}
.post-container .post .entry-title a {
  color: #202020;
}
.post-container .post .entry-title a:hover {
  color: #ff4d4d;
}
.post-container .post .entry-content p {
  font-size: 15px;
  line-height: 1.8em;
  margin-bottom: 30px;
}
.post-container .post .entry-meta a {
  color: #ff4d4d;
}
.post-container .post .entry-meta span {
  margin-right: 28px;
}
.post-container .post.format-quote .blockquote-container blockquote {
  background: #eee;
  font-family: 'Montserrat', sans-serif;
  font-weight: 300;
  padding: 37px 40px 42px 80px;
  position: relative;
}
.post-container .post.format-quote .blockquote-container blockquote:before {
  content: '';
  display: inline-block;
  width: 31px;
  height: 25px;
  position: absolute;
  top: 42px;
  left: 32px;
  background: url(../img/template-assets/quote-mark.svg);
  background-repeat: no-repeat;
}
.post-container .post.format-quote .blockquote-container blockquote p {
  font-size: 24px;
  line-height: 2em;
  margin-bottom: 0;
}
.post-container .post.format-quote .blockquote-container blockquote cite {
  font-size: 18px;
  position: relative;
}
.post-container .post.format-quote .blockquote-container blockquote cite:before {
  content: 'â€”';
  margin-right: 4px;
}
.post-container .post.format-video .video-container video {
  width: 100%;
}
.post-container .post.format-video .video-container .mejs-layer {
  width: 100% !important;
  background-size: cover;
}
.post-container.blog-timeline {
  position: relative;
  margin-bottom: 60px;
  margin-right: 0;
  margin-left: 0;
}
.post-container.blog-timeline:before {
  content: '';
  display: inline-block;
  width: 3px;
  height: 100%;
  position: absolute;
  top: 0;
  left: 50%;
  background: #eee;
  transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
}
.post-container.blog-timeline .article-container {
  position: relative;
  margin-bottom: 45px;
}
.post-container.blog-timeline .article-container.left-positioned:before,
.post-container.blog-timeline .article-container.left-positioned:after,
.post-container.blog-timeline .article-container.right-positioned:before,
.post-container.blog-timeline .article-container.right-positioned:after {
  content: '';
  display: inline-block;
  position: absolute;
  top: 0;
  -webkit-border-radius: 50em;
  -moz-border-radius: 50em;
  border-radius: 50em;
}
.post-container.blog-timeline .article-container.left-positioned:before,
.post-container.blog-timeline .article-container.right-positioned:before {
  width: 21px;
  height: 21px;
  background: white;
  border: 1px solid #ddd;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.post-container.blog-timeline .article-container.left-positioned:after,
.post-container.blog-timeline .article-container.right-positioned:after {
  width: 11px;
  height: 11px;
  background: #ff4d4d;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.post-container.blog-timeline .article-container.left-positioned:before {
  transform: translateX(10px);
  -ms-transform: translateX(10px);
  -webkit-transform: translateX(10px);
}
.post-container.blog-timeline .article-container.left-positioned:after {
  transform: translateX(5px) translateY(5px);
  -ms-transform: translateX(5px) translateY(5px);
  -webkit-transform: translateX(5px) translateY(5px);
}
.post-container.blog-timeline .article-container.left-positioned:before,
.post-container.blog-timeline .article-container.left-positioned:after {
  top: 50px;
  right: 0;
}
.post-container.blog-timeline .article-container.left-positioned:hover:after {
  transform: translateX(5px) translateY(5px) scale(1.2);
  -ms-transform: translateX(5px) translateY(5px) scale(1.2);
  -webkit-transform: translateX(5px) translateY(5px) scale(1.2);
}
.post-container.blog-timeline .article-container.right-positioned:before {
  transform: translateX(-10px);
  -ms-transform: translateX(-10px);
  -webkit-transform: translateX(-10px);
}
.post-container.blog-timeline .article-container.right-positioned:after {
  transform: translateX(-5px) translateY(5px);
  -ms-transform: translateX(-5px) translateY(5px);
  -webkit-transform: translateX(-5px) translateY(5px);
}
.post-container.blog-timeline .article-container.right-positioned:before,
.post-container.blog-timeline .article-container.right-positioned:after {
  top: 90px;
  left: 0;
}
.post-container.blog-timeline .article-container.right-positioned:hover:after {
  transform: translateX(-5px) translateY(5px) scale(1.2);
  -ms-transform: translateX(-5px) translateY(5px) scale(1.2);
  -webkit-transform: translateX(-5px) translateY(5px) scale(1.2);
}
.post-container.blog-timeline .article-container {
  max-width: 100%;
}
.post-container.blog-timeline .article-container article {
  border: 1px solid #eee !important;
  padding-bottom: 37px;
  margin-bottom: 0;
}
.post-container.blog-timeline .article-container article .entry-image {
  margin-bottom: 50px;
}
.post-container.blog-timeline .article-container article .mejs-container {
  margin-bottom: 50px;
}
.post-container.blog-timeline .article-container article .entry-title {
  padding-right: 25px;
  padding-left: 25px;
}
.post-container.blog-timeline .article-container article .entry-content {
  padding-right: 25px;
  padding-left: 25px;
}
.post-container.blog-timeline .article-container article .entry-content p {
  margin-bottom: 18px;
}
.post-container.blog-timeline .article-container article .entry-meta {
  padding-right: 25px;
  padding-left: 25px;
}
.post-container.blog-timeline .article-container article .entry-meta span {
  margin-right: 20px;
}
.post-container.blog-timeline .article-container article.format-quote {
  border: none !important;
}
.post-container.blog-timeline .article-container article.format-quote .entry-content {
  padding: 0;
}
.post-container.blog-masonry {
  margin: 0;
}
.post-container.blog-masonry .article-container {
  margin-bottom: 30px;
  overflow: hidden;
  max-width: 100%;
}
.post-container.blog-masonry .article-container .post {
  margin-bottom: 0;
  padding-bottom: 37px;
  border: 1px solid #eee;
}
.post-container.blog-masonry .article-container .post .entry-image {
  margin-bottom: 45px;
}
.post-container.blog-masonry .article-container .post .entry-title {
  padding-right: 26px;
  padding-left: 26px;
}
.post-container.blog-masonry .article-container .post .entry-content {
  padding-right: 26px;
  padding-left: 26px;
}
.post-container.blog-masonry .article-container .post .entry-content p {
  margin-bottom: 18px;
}
.post-container.blog-masonry .article-container .post .entry-meta {
  padding-right: 26px;
  padding-left: 26px;
}
.post-container.blog-masonry .article-container .post .entry-meta span {
  margin-right: 21px;
}
.post-container.blog-masonry .article-container .post.format-audio .mejs-container {
  margin-bottom: 40px;
}
.post-container.blog-masonry .article-container .post.format-video .mejs-container {
  margin-bottom: 44px;
}
.post-container.blog-masonry .article-container .post.format-quote {
  padding-bottom: 0;
  border: none;
}
.post-container.blog-masonry .article-container .post.format-quote .entry-content {
  padding: 0;
}
.post-container.blog-masonry.columns2 .article-container .post.format-quote .blockquote-container blockquote {
  padding: 35px 25px 35px 75px;
}
.post-container.blog-masonry.columns3 .article-container .post .entry-image {
  margin-bottom: 40px;
}
.post-container.blog-masonry.columns3 .article-container .post .entry-title {
  font-size: 18px;
  font-weight: 600;
}
.post-container.blog-masonry.columns3 .article-container .post .mejs-container .mejs-controls .mejs-time {
  font-size: 14px;
}
.post-container.blog-masonry.columns4 .article-container {
  margin-bottom: 38px;
}
.post-container.blog-masonry.columns4 .article-container .post .entry-image {
  margin-bottom: 35px;
}
.post-container.blog-masonry.columns4 .article-container .post .entry-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
}
.post-container.blog-masonry.columns4 .article-container .post .audio-container .song-name {
  padding-left: 25px;
}
.post-container.blog-masonry.columns4 .article-container .post .mejs-container .mejs-controls .mejs-time {
  font-size: 14px;
}
.post-container.blog-masonry.columns4 .article-container .post.format-quote .blockquote-container blockquote {
  padding: 35px 20px 35px 70px;
}
.post-container.medium-post-thumb .post {
  padding-bottom: 70px;
  margin-bottom: 70px;
}
.post-container.medium-post-thumb .post .entry-image {
  margin-bottom: 0 !important;
}
.post-container.medium-post-thumb .post .entry-image {
  margin-bottom: 28px;
}
.post-container.medium-post-thumb .post .entry-content p {
  margin-bottom: 20px;
}
.post-container.medium-post-thumb .mejs-container.mejs-video {
  margin-bottom: 0 !important;
}
.post-container.blog-grid .post {
  padding-bottom: 0;
  margin-bottom: 60px;
}
.post-container.blog-grid .post .entry-image {
  margin-bottom: 35px;
}
.post-container.blog-grid .post .entry-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 26px;
}
.post-container.blog-grid .post .entry-content p {
  margin-bottom: 15px;
}
.post-container.blog-grid.columns2 .post .entry-image {
  margin-bottom: 41px;
}
.post-container.blog-grid.columns4 .post {
  margin-bottom: 53px;
}
.post-container.blog-grid.columns4 .post .entry-content p {
  margin-bottom: 29px;
}
.post-container.blog-grid.columns4 .post .entry-meta span {
  margin-right: 26px;
}
.post-container.blog-post-single .post .entry-image {
  margin-bottom: 62px;
}
.post-container.blog-post-single .post .entry-title {
  margin-bottom: 44px;
}
.post-container.blog-post-single .post .entry-meta {
  margin-bottom: 30px;
}
.post-container.blog-post-single .post .entry-content {
  margin-bottom: 46px;
}
.post-container.blog-post-single .post .entry-content p {
  margin-bottom: 25px;
}
.post-container.blog-post-single .post .entry-content p.section-heading {
  font-size: 18px;
  line-height: 1.8em;
}
.post-container.blog-post-single .post .entry-footer .entry-tags {
  margin-bottom: 30px;
}
.post-container.blog-post-single .post .entry-footer .entry-tags ul {
  margin-bottom: 0;
}
.post-container.blog-post-single .post .entry-footer .entry-tags ul li {
  float: left;
  margin-right: 10px;
  margin-bottom: 10px;
}
.post-container.blog-post-single .post .entry-footer .entry-tags ul li a {
  display: inline-block;
  background: #ff4d4d;
  color: white;
  padding: 5px 15px 5px;
}
.post-container.blog-post-single .post .entry-footer .entry-tags ul li a:hover {
  background: #444444;
}
.post-container.blog-post-single .post .entry-footer .entry-share {
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  padding-top: 30px;
  padding-bottom: 30px;
  margin-bottom: 45px;
}
.post-container.blog-post-single .post .entry-footer .entry-share p {
  float: left;
  margin-bottom: 0;
}
.post-container.blog-post-single .post .entry-footer .entry-share ul {
  float: right;
  margin-bottom: 0;
}
.post-container.blog-post-single .post .entry-footer .entry-share ul li {
  float: left;
  margin-right: 20px;
}
.post-container.blog-post-single .post .entry-footer .entry-share ul li a {
  color: #444444;
}
.post-container.blog-post-single .post .entry-footer .entry-share ul li a i {
  font-size: 1.25em;
}
.post-container.blog-post-single .post .entry-footer .entry-share ul li a:hover {
  color: #ff4d4d;
}
.post-container.blog-post-single .post .entry-footer .entry-author {
  border-bottom: 1px solid #ddd;
  padding-bottom: 26px;
  margin-bottom: 28px;
}
.post-container.blog-post-single .post .entry-footer .entry-author .author-avatar {
  float: left;
  width: 100px;
  margin-right: 34px;
}
.post-container.blog-post-single .post .entry-footer .entry-author .author-bio {
  overflow: hidden;
}
.post-container.blog-post-single .post .entry-footer .entry-author .author-bio a {
  color: #444444;
}
.post-container.blog-post-single .post .entry-footer .entry-author .author-bio a:hover {
  color: #ff4d4d;
}
.post-container.blog-post-single .post .entry-related-post {
  margin-bottom: 20px;
}
.post-container.blog-post-single .post .entry-related-post > h6 {
  margin-bottom: 32px;
}
.post-container.blog-post-single .post .entry-related-post article {
  margin-bottom: 34px;
}
.post-container.blog-post-single .post .entry-related-post article figure {
  margin-bottom: 26px;
}
.post-container.blog-post-single .post .entry-related-post article a {
  font-size: 15px;
  line-height: 1.6em;
  color: #444444;
}
.post-container.blog-post-single .post .entry-related-post article a:hover {
  color: #ff4d4d;
}
.post-container.blog-post-single .post .entry-comments-container {
  margin-bottom: 46px;
}
.post-container.blog-post-single .post .entry-comments-container .comments-counter {
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  padding-top: 36px;
  padding-bottom: 38px;
  margin-bottom: 40px;
}
.post-container.blog-post-single .post .entry-comments-container .comments-counter h6 {
  margin-bottom: 0;
}
.post-container.blog-post-single .post .entry-comments-container .comments-counter a {
  color: #444444;
}
.post-container.blog-post-single .post .entry-comments-container .comments-counter a:hover {
  color: #ff4d4d;
}
.post-container.blog-post-single .post .entry-comments-container .entry-comments > ol .comment .comment-box {
  border-bottom: 1px solid #ddd;
  margin-bottom: 27px;
}
.post-container.blog-post-single .post .entry-comments-container .entry-comments > ol .comment .comment-box .avatar {
  float: left;
  margin-right: 20px;
}
.post-container.blog-post-single .post .entry-comments-container .entry-comments > ol .comment .comment-box .comment-meta {
  margin-bottom: 15px;
}
.post-container.blog-post-single .post .entry-comments-container .entry-comments > ol .comment .comment-box .comment-meta .comment-author a {
  font-size: 18px;
  color: #444444;
}
.post-container.blog-post-single .post .entry-comments-container .entry-comments > ol .comment .comment-box .comment-meta .comment-author a:hover {
  color: #ff4d4d;
}
.post-container.blog-post-single .post .entry-comments-container .entry-comments > ol .comment .comment-box .comment-meta .comment-time {
  display: inline-block;
  margin-right: 27px;
}
.post-container.blog-post-single .post .entry-comments-container .entry-comments > ol .comment .comment-box .comment-meta .comment-time time {
  font-size: 12px;
  font-weight: 400;
  text-transform: uppercase;
  color: #858585;
}
.post-container.blog-post-single .post .entry-comments-container .entry-comments > ol .comment .comment-box .comment-meta .comment-reply {
  display: inline-block;
}
.post-container.blog-post-single .post .entry-comments-container .entry-comments > ol .comment .comment-box .comment-meta .comment-reply a {
  font-size: 12px;
  font-weight: 400;
  text-transform: uppercase;
  color: #ff4d4d;
}
.post-container.blog-post-single .post .entry-comments-container .entry-comments > ol .comment .comment-box .comment-meta .comment-reply a:hover {
  color: #444444;
}
.post-container.blog-post-single .post .entry-comments-container .entry-comments > ol .comment .comment-box .comment-text {
  overflow: hidden;
  padding-bottom: 15px;
  margin-left: 76px;
}
.post-container.blog-post-single .post .entry-comments-container .entry-comments > ol .comment .children .comment {
  margin-bottom: 27px;
  padding-left: 75px;
}
.post-container.blog-post-single .post .comment-respond > h6 {
  margin-bottom: 46px;
}
.post-container.blog-post-single .post .comment-respond .comment-form input:not([type=submit]),
.post-container.blog-post-single .post .comment-respond .comment-form textarea {
  font-weight: 400;
  width: 100%;
  border: 1px solid #ddd;
  margin-bottom: 22px;
  padding: 18px 25px 16px;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.post-container.blog-post-single .post .comment-respond .comment-form input:not([type=submit]):focus,
.post-container.blog-post-single .post .comment-respond .comment-form textarea:focus {
  border-color: #444444;
  outline: none;
}
.post-container.blog-post-single .post .comment-respond .comment-form input[type=submit] {
  width: auto;
}
.post-container.blog-post-single .post .comment-respond .comment-form ::-webkit-input-placeholder {
  color: #444;
}
.post-container.blog-post-single .post .comment-respond .comment-form :-moz-placeholder {
  color: #444;
}
.post-container.blog-post-single .post .comment-respond .comment-form ::-moz-placeholder {
  color: #444;
}
.post-container.blog-post-single .post .comment-respond .comment-form :-ms-input-placeholder {
  color: #444;
}
.post-container.blog-post-single .post.format-audio .mejs-container .mejs-inner .mejs-controls .mejs-time {
  font-size: 18px;
}
.post-container.blog-post-single .post.format-quote .blockquote-container {
  margin-bottom: 34px;
}
.sidebar-right .post-container:not(.blog-post-single) .post .entry-image,
.sidebar-left .post-container:not(.blog-post-single) .post .entry-image {
  margin-bottom: 49px;
}
.sidebar-right .post-container:not(.blog-post-single) .post .mejs-container,
.sidebar-left .post-container:not(.blog-post-single) .post .mejs-container {
  margin-bottom: 50px;
}
.sidebar-right .blog-timeline,
.sidebar-left .blog-timeline {
  padding: 0;
}
.sidebar-right .blog-timeline .article-container,
.sidebar-left .blog-timeline .article-container {
  margin-bottom: 37px;
}
.sidebar-right .blog-timeline .article-container article.format-audio .audio-container .song-name p,
.sidebar-left .blog-timeline .article-container article.format-audio .audio-container .song-name p {
  padding-right: 120px;
}
.sidebar-right .blog-timeline .article-container article.format-quote .blockquote-container blockquote p,
.sidebar-left .blog-timeline .article-container article.format-quote .blockquote-container blockquote p {
  font-size: 18px;
}
.sidebar-right .blog-timeline .article-container article.format-quote .blockquote-container blockquote cite,
.sidebar-left .blog-timeline .article-container article.format-quote .blockquote-container blockquote cite {
  font-size: 14px;
}
.sidebar-right .mejs-container .mejs-inner .mejs-controls .mejs-time,
.sidebar-left .mejs-container .mejs-inner .mejs-controls .mejs-time {
  font-size: 14px;
  top: -72px;
}
.sidebar-right .blog-grid .post .entry-content p,
.sidebar-left .blog-grid .post .entry-content p {
  margin-bottom: 25px;
}
.sidebar-right .blog-grid .post .entry-meta span,
.sidebar-left .blog-grid .post .entry-meta span {
  margin-right: 26px;
}
.sidebar-right .blog-timeline {
  padding: 0;
}
.sidebar-left .blog-timeline {
  padding: 0;
  margin: 0;
}
/*************************************************************
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
..................... AUDIO AND VIDEO ......................
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
*************************************************************/
.mejs-container {
  width: 100% !important;
  background: #666;
  outline: none !important;
}
.mejs-container .mejs-inner .mejs-controls {
  background: #666;
  height: 70px;
  padding-right: 27px;
  padding-left: 23px;
}
.mejs-container .mejs-inner .mejs-controls .mejs-playpause-button {
  width: 20px;
  height: 24px;
  margin-top: 23px;
}
.mejs-container .mejs-inner .mejs-controls .mejs-playpause-button button {
  background: none;
  position: relative;
  width: 20px;
  height: 24px;
  margin: 0;
  outline: none;
}
.mejs-container .mejs-inner .mejs-controls .mejs-playpause-button button:before,
.mejs-container .mejs-inner .mejs-controls .mejs-playpause-button button:after {
  content: '';
  display: inline-block;
  width: 0;
  height: 0;
  border-top: 0 solid transparent;
  border-right: 0 solid transparent;
  border-bottom: 0 solid transparent;
  border-left: 0 solid transparent;
  position: absolute;
  left: 0;
  top: 0;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.mejs-container .mejs-inner .mejs-controls .mejs-playpause-button.mejs-play button:before {
  border-top: 12px solid transparent;
  border-right: 0 solid transparent;
  border-bottom: 12px solid transparent;
  border-left: 20px solid white;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
}
.mejs-container .mejs-inner .mejs-controls .mejs-playpause-button.mejs-pause button:before {
  border-top: 12px solid white;
  border-right: 4px solid white;
  border-bottom: 12px solid white;
  border-left: 4px solid white;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.mejs-container .mejs-inner .mejs-controls .mejs-playpause-button.mejs-pause button:after {
  border-top: 12px solid white;
  border-right: 4px solid white;
  border-bottom: 12px solid white;
  border-left: 4px solid white;
  left: 12px;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.mejs-container .mejs-inner .mejs-controls .mejs-time-rail {
  padding-top: 0;
  height: 11px;
  margin-top: 30px;
  margin-left: 40px;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
}
.mejs-container .mejs-inner .mejs-controls .mejs-time-rail span,
.mejs-container .mejs-inner .mejs-controls .mejs-time-rail a {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  outline: none !important;
}
.mejs-container .mejs-inner .mejs-controls .mejs-time-rail .mejs-time-total {
  background: white;
  margin: 0;
}
.mejs-container .mejs-inner .mejs-controls .mejs-time-rail .mejs-time-total .mejs-time-loaded {
  background: white;
}
.mejs-container .mejs-inner .mejs-controls .mejs-time-rail .mejs-time-total .mejs-time-current {
  background: #ff4d4d;
}
.mejs-container .mejs-inner .mejs-controls .mejs-time {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  font-weight: 300;
  position: absolute;
  top: -73px;
  right: 19px;
}
.mejs-container .mejs-inner .mejs-controls .mejs-time span {
  display: none;
}
.mejs-container .mejs-inner .mejs-controls .mejs-duration {
  display: inline-block !important;
}
.mejs-container .mejs-inner .mejs-controls .mejs-duration:before {
  content: '/';
  padding-right: 5px;
}
.mejs-container .mejs-inner .mejs-controls .mejs-currenttime {
  display: inline-block !important;
  margin-right: 4px;
  color: #ff4d4d;
}
.mejs-container .mejs-inner .mejs-controls .mejs-volume-button {
  display: none;
}
.mejs-container .mejs-inner .mejs-controls .mejs-horizontal-volume-slider {
  width: 0;
  opacity: 0;
  visibility: hidden;
}
.audio-container {
  background: #444444;
  width: 100%;
  color: white;
  padding-top: 45px;
  position: relative;
}
.audio-container .song-name {
  padding-left: 30px;
  display: inline-block;
  position: relative;
  z-index: 2;
}
.audio-container .song-name p {
  margin-bottom: 0;
  padding-right: 140px;
}
.audio-container .song-name p a {
  color: #ff4d4d;
}
.audio-container .mejs-container {
  height: 70px !important;
  position: static;
  margin-top: 45px;
}
.audio-container.audio-container-style2 {
  background: transparent;
  padding: 0;
  width: 100%;
}
.audio-container.audio-container-style2 .cover img {
  width: 100%;
}
.audio-container.audio-container-style2 .contents {
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 60px 50px 40px 70px;
  background: rgba(0, 0, 0, 0.3);
  width: 100%;
}
.audio-container.audio-container-style2 .contents .song-details {
  float: right;
  width: 90%;
  position: relative;
  z-index: 2;
}
.audio-container.audio-container-style2 .contents .mejs-container {
  background: transparent;
  position: relative;
  margin: 0;
  z-index: 0;
  width: 10%;
  height: 100% !important;
}
.audio-container.audio-container-style2 .contents .mejs-container .mejs-inner {
  position: relative;
}
.audio-container.audio-container-style2 .contents .mejs-container .mejs-inner .mejs-controls {
  background: transparent;
  padding: 0;
  height: 100%;
}
.audio-container.audio-container-style2 .contents .mejs-container .mejs-inner .mejs-controls .mejs-playpause-button {
  width: 37px;
  height: 45px;
  margin-top: 0;
  position: absolute;
  top: 50%;
  left: auto;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
}
.audio-container.audio-container-style2 .contents .mejs-container .mejs-inner .mejs-controls .mejs-playpause-button button {
  width: 100%;
  height: 100%;
}
.audio-container.audio-container-style2 .contents .mejs-container .mejs-inner .mejs-controls .mejs-playpause-button.mejs-play button:before {
  border-top-width: 22px;
  border-left-width: 37px;
  border-bottom-width: 22px;
}
.audio-container.audio-container-style2 .contents .mejs-container .mejs-inner .mejs-controls .mejs-playpause-button.mejs-pause button:before,
.audio-container.audio-container-style2 .contents .mejs-container .mejs-inner .mejs-controls .mejs-playpause-button.mejs-pause button:after {
  border-top-width: 22px;
  border-right-width: 8px;
  border-bottom-width: 22px;
  border-left-width: 8px;
}
.audio-container.audio-container-style2 .contents .mejs-container .mejs-inner .mejs-controls .mejs-playpause-button.mejs-pause button:after {
  left: 20px;
}
.audio-container.audio-container-style2 .contents .mejs-container .mejs-inner .mejs-controls .mejs-time-rail,
.audio-container.audio-container-style2 .contents .mejs-container .mejs-inner .mejs-controls .mejs-time {
  display: none;
}
.video-container .mejs-container .mejs-controls .mejs-time {
  display: none;
}
.video-container .mejs-container .mejs-controls .mejs-fullscreen-button button {
  margin-top: 27px;
  margin-left: 17px;
}
/*************************************************************
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
..................... 6. PORTFOLIO ......................
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
*************************************************************/
.portfolio-container {
  overflow: hidden;
}
.portfolio-container .portfolio-filter-container ul {
  margin-bottom: 0;
}
.portfolio-container .portfolio-filter-container ul li {
  display: inline-block;
  margin-right: 7px;
  margin-bottom: 10px;
}
.portfolio-container .portfolio-filter-container ul li:last-child {
  margin-right: 0;
}
.portfolio-container .portfolio-filter-container ul li span {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  text-transform: uppercase;
  display: inline-block;
  padding: 11px 24px;
  border: 1px solid #676767;
  cursor: pointer;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.portfolio-container .portfolio-filter-container ul li:hover span,
.portfolio-container .portfolio-filter-container ul li.active span {
  background: #444444;
  color: white;
}
.portfolio-container .portfolio-filter-container.portfolio-filter-container-style2 .portfolio-filter.active span,
.portfolio-container .portfolio-filter-container.portfolio-filter-container-style2 .portfolio-filter:hover span {
  background: #ff4d4d;
  border-color: #ff4d4d;
  color: white;
}
.portfolio-container .portfolio-filter-container.portfolio-filter-container-style3 {
  text-align: center;
}
.portfolio-container .portfolio-filter-container.portfolio-filter-container-style3 > p {
  display: inline-block;
  margin-right: 25px;
  font-weight: 300;
}
.portfolio-container .portfolio-filter-container.portfolio-filter-container-style3 ul {
  display: inline-block;
}
.portfolio-container .portfolio-filter-container.portfolio-filter-container-style3 ul li {
  display: inline-block;
  margin-left: 10px;
  margin-bottom: 10px;
}
.portfolio-container .portfolio-filter-container.portfolio-filter-container-style3 ul li:after {
  content: '/';
  margin-left: 15px;
}
.portfolio-container .portfolio-filter-container.portfolio-filter-container-style3 ul li:last-child:after {
  content: none;
}
.portfolio-container .portfolio-filter-container.portfolio-filter-container-style3 ul li span {
  font-family: 'Montserrat', sans-serif;
  font-weight: 300;
  text-transform: none;
  padding: 0;
  border: none;
}
.portfolio-container .portfolio-filter-container.portfolio-filter-container-style3 ul li:hover span,
.portfolio-container .portfolio-filter-container.portfolio-filter-container-style3 ul li.active span {
  background: none;
  color: inherit;
  font-weight: 400;
  border-bottom: 1px solid #444444;
}
.portfolio-container .portfolio-item-wrapper {
  -webkit-transition: 0.75s all 0.001s ease-out;
  -moz-transition: 0.75s all 0.001s ease-out;
  transition: 0.75s all 0.001s ease-out;
  margin: 0;
}
.portfolio-container .portfolio-item-wrapper .portfolio-item-container {
  margin-bottom: 30px;
}
.portfolio-container .portfolio-item-wrapper .portfolio-item {
  width: 100%;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.portfolio-container .portfolio-item-wrapper .portfolio-item figure {
  position: relative;
  width: 100%;
}
.portfolio-container .portfolio-item-wrapper .portfolio-item figure img {
  width: 100%;
}
.portfolio-container .portfolio-item-wrapper .portfolio-item figure .overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateY(-50%) translateX(-50%);
  -ms-transform: translateY(-50%) translateX(-50%);
  -webkit-transform: translateY(-50%) translateX(-50%);
}
.portfolio-container .portfolio-item-wrapper .portfolio-item .overlay a {
  display: inline-block;
  width: 54px;
  height: 54px;
  color: #444444;
  background: white;
  text-align: center;
  position: relative;
  margin: 0 3px;
  border: 2px solid #505050;
  -webkit-box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.5);
  -moz-box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.5);
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.4s all 0.001s cubic-bezier(0.89, 0.75, 0.52, 0.91);
  -moz-transition: 0.4s all 0.001s cubic-bezier(0.89, 0.75, 0.52, 0.91);
  transition: 0.4s all 0.001s cubic-bezier(0.89, 0.75, 0.52, 0.91);
  -webkit-border-radius: 50em;
  -moz-border-radius: 50em;
  border-radius: 50em;
}
.portfolio-container .portfolio-item-wrapper .portfolio-item .overlay a i {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateY(-50%) translateX(-50%);
  -ms-transform: translateY(-50%) translateX(-50%);
  -webkit-transform: translateY(-50%) translateX(-50%);
}
.portfolio-container .portfolio-item-wrapper .portfolio-item .overlay a.icon-link i {
  font-size: 1.55em;
}
.portfolio-container .portfolio-item-wrapper .portfolio-item .overlay a.icon-zoom {
  -webkit-transition: 0.4s all 0.2s cubic-bezier(0.89, 0.75, 0.52, 0.91);
  -moz-transition: 0.4s all 0.2s cubic-bezier(0.89, 0.75, 0.52, 0.91);
  transition: 0.4s all 0.2s cubic-bezier(0.89, 0.75, 0.52, 0.91);
}
.portfolio-container .portfolio-item-wrapper .portfolio-item .overlay a.icon-zoom i {
  font-size: 1.3em;
}
.portfolio-container .portfolio-item-wrapper .portfolio-item .overlay a:hover {
  background: #444444;
  color: white;
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style1 .portfolio-item {
  position: relative;
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style1 .portfolio-item .overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  text-align: center;
  transform: translateY(-50%) translateX(-50%);
  -ms-transform: translateY(-50%) translateX(-50%);
  -webkit-transform: translateY(-50%) translateX(-50%);
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style1 .portfolio-item .overlay a {
  transform: translateY(-20px);
  -ms-transform: translateY(-20px);
  -webkit-transform: translateY(-20px);
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style1 .portfolio-item:hover {
  z-index: 1;
  -webkit-box-shadow: 0 0 9px 6px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 0 9px 6px rgba(0, 0, 0, 0.2);
  box-shadow: 0 0 9px 6px rgba(0, 0, 0, 0.2);
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style1 .portfolio-item:hover .overlay a {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  -ms-transform: translateY(0);
  -webkit-transform: translateY(0);
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 {
  margin-bottom: 40px;
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item {
  border: 1px solid #ddd;
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item figure .overlay {
  text-align: center;
  width: 100%;
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item figure .overlay a {
  transform: translateY(-15%);
  -ms-transform: translateY(-15%);
  -webkit-transform: translateY(-15%);
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .portfolio-item-details {
  padding: 35px 30px 27px;
  color: #444444;
  -webkit-transition: 0.5s all 0.001s ease-out;
  -moz-transition: 0.5s all 0.001s ease-out;
  transition: 0.5s all 0.001s ease-out;
  background: white;
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .portfolio-item-details .portfolio-item-name {
  margin-bottom: 15px;
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .portfolio-item-details h4,
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .portfolio-item-details h6,
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .portfolio-item-details a {
  color: #444444;
  margin-bottom: 0;
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .portfolio-item-details a:hover {
  color: #ff4d4d;
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .overlay {
  text-align: right;
  margin-top: 12px;
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .overlay a {
  opacity: 0;
  visibility: hidden;
  transform: translateX(15%);
  -ms-transform: translateX(15%);
  -webkit-transform: translateX(15%);
  -webkit-transition: 0.4s all 0.001s cubic-bezier(0.89, 0.75, 0.52, 0.91);
  -moz-transition: 0.4s all 0.001s cubic-bezier(0.89, 0.75, 0.52, 0.91);
  transition: 0.4s all 0.001s cubic-bezier(0.89, 0.75, 0.52, 0.91);
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .overlay a.icon-link {
  -webkit-transition: 0.4s all 0.2s cubic-bezier(0.89, 0.75, 0.52, 0.91);
  -moz-transition: 0.4s all 0.2s cubic-bezier(0.89, 0.75, 0.52, 0.91);
  transition: 0.4s all 0.2s cubic-bezier(0.89, 0.75, 0.52, 0.91);
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .overlay a.icon-zoom {
  -webkit-transition: 0.4s all 0s cubic-bezier(0.89, 0.75, 0.52, 0.91);
  -moz-transition: 0.4s all 0s cubic-bezier(0.89, 0.75, 0.52, 0.91);
  transition: 0.4s all 0s cubic-bezier(0.89, 0.75, 0.52, 0.91);
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item:hover figure .overlay a {
  transform: translateY(0);
  -ms-transform: translateY(0);
  -webkit-transform: translateY(0);
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item:hover .portfolio-item-details {
  background: #444444;
  color: white;
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item:hover .portfolio-item-details h4,
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item:hover .portfolio-item-details h6
/*.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item:hover .portfolio-item-details a*/ {
  color: white;
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item:hover .portfolio-item-details a:hover {
  color: white;
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item:hover .overlay a {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
  -ms-transform: translateX(0);
  -webkit-transform: translateX(0);
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item:hover .overlay a.icon-link {
  -webkit-transition: 0.4s all 0s cubic-bezier(0.89, 0.75, 0.52, 0.91);
  -moz-transition: 0.4s all 0s cubic-bezier(0.89, 0.75, 0.52, 0.91);
  transition: 0.4s all 0s cubic-bezier(0.89, 0.75, 0.52, 0.91);
}
.portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item:hover .overlay a.icon-zoom {
  -webkit-transition: 0.4s all 0.2s cubic-bezier(0.89, 0.75, 0.52, 0.91);
  -moz-transition: 0.4s all 0.2s cubic-bezier(0.89, 0.75, 0.52, 0.91);
  transition: 0.4s all 0.2s cubic-bezier(0.89, 0.75, 0.52, 0.91);
}
.portfolio-container.columns3 .portfolio-item-wrapper.portfolio-item-wrapper-style2 {
  margin-bottom: 25px;
}
.portfolio-container.columns3 .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .portfolio-item-details {
  padding: 25px 15px 22px;
}
.portfolio-container.columns3 .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .portfolio-item-details .portfolio-item-name {
  margin-bottom: 10px;
}
.portfolio-container.columns4 .portfolio-item-wrapper.portfolio-item-wrapper-style2 {
  margin-bottom: 25px;
}
.portfolio-container.columns4 .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .portfolio-item-details {
  padding: 24px 13px 21px;
}
.portfolio-container.columns4 .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .portfolio-item-details .portfolio-item-name {
  margin-bottom: 10px;
}
.portfolio-container.columns5 .portfolio-item-container {
  float: left;
  width: 20%;
  padding-left: 15px;
  padding-right: 15px;
}
.portfolio-container.portfolio-fullwidth.columns3 .portfolio-item-wrapper.portfolio-item-wrapper-style2 {
  margin-bottom: 40px;
}
.portfolio-container.portfolio-fullwidth.columns3 .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .portfolio-item-details {
  padding: 35px 30px 27px;
}
.portfolio-container.portfolio-fullwidth.columns4 .portfolio-item-wrapper.portfolio-item-wrapper-style2 {
  margin-bottom: 40px;
}
.portfolio-container.portfolio-fullwidth.columns4 .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .portfolio-item-details {
  padding: 35px 20px 31px;
}
.portfolio-container.portfolio-fullwidth.columns5 .portfolio-item-wrapper.portfolio-item-wrapper-style2 {
  margin-bottom: 30px;
}
.portfolio-container.portfolio-fullwidth.columns5 .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .portfolio-item-details {
  padding: 25px 15px 27px;
}
.portfolio-container.portfolio-fullwidth.columns5.no-gap .portfolio-item-wrapper .portfolio-item-container {
  padding: 0;
}
.portfolio-container.portfolio-fullwidth.columns6 .portfolio-item-wrapper.portfolio-item-wrapper-style2 {
  margin-bottom: 25px;
}
.portfolio-container.portfolio-fullwidth.columns6 .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .portfolio-item-details {
  padding: 23px 13px 18px;
}
.portfolio-container.portfolio-grayscale .portfolio-item figure img {
  -webkit-filter: grayscale(1);
  filter: grayscale(1);
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.portfolio-container.portfolio-grayscale .portfolio-item:hover img {
  -webkit-filter: grayscale(0);
  filter: grayscale(0);
}
.portfolio-container.no-gap {
  margin-left: -15px;
  margin-right: -15px;
}
.portfolio-container.no-gap .portfolio-item-container {
  padding: 0;
  margin: 0;
}
.portfolio-container.portfolio-fullwidth {
  margin: 0;
}
.portfolio-container.portfolio-timeline .portfolio-item-wrapper:before {
  content: '';
  display: inline-block;
  width: 3px;
  height: 100%;
  position: absolute;
  top: 0;
  left: 50%;
  background: #eee;
  transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
}
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container {
  position: relative;
}
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.left-positioned:before,
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.left-positioned:after,
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.right-positioned:before,
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.right-positioned:after {
  content: '';
  display: inline-block;
  position: absolute;
  top: 0;
  -webkit-border-radius: 50em;
  -moz-border-radius: 50em;
  border-radius: 50em;
}
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.left-positioned:before,
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.right-positioned:before {
  width: 21px;
  height: 21px;
  background: white;
  border: 1px solid #ddd;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.left-positioned:after,
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.right-positioned:after {
  width: 11px;
  height: 11px;
  background: #ff4d4d;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.left-positioned:before {
  transform: translateX(10px);
  -ms-transform: translateX(10px);
  -webkit-transform: translateX(10px);
}
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.left-positioned:after {
  transform: translateX(5px) translateY(5px);
  -ms-transform: translateX(5px) translateY(5px);
  -webkit-transform: translateX(5px) translateY(5px);
}
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.left-positioned:before,
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.left-positioned:after {
  top: 50px;
  right: 0;
}
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.left-positioned:hover:after {
  transform: translateX(5px) translateY(5px) scale(1.2);
  -ms-transform: translateX(5px) translateY(5px) scale(1.2);
  -webkit-transform: translateX(5px) translateY(5px) scale(1.2);
}
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.right-positioned:before {
  transform: translateX(-10px);
  -ms-transform: translateX(-10px);
  -webkit-transform: translateX(-10px);
}
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.right-positioned:after {
  transform: translateX(-5px) translateY(5px);
  -ms-transform: translateX(-5px) translateY(5px);
  -webkit-transform: translateX(-5px) translateY(5px);
}
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.right-positioned:before,
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.right-positioned:after {
  top: 90px;
  left: 0;
}
.portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.right-positioned:hover:after {
  transform: translateX(-5px) translateY(5px) scale(1.2);
  -ms-transform: translateX(-5px) translateY(5px) scale(1.2);
  -webkit-transform: translateX(-5px) translateY(5px) scale(1.2);
}
.portfolio-container.portfolio-masonry .portfolio-item-container .portfolio-item {
  height: 100%;
  border: none;
  position: relative;
  overflow: hidden;
}
.portfolio-container.portfolio-masonry .portfolio-item-container .portfolio-item figure {
  height: 100%;
  overflow: hidden;
}
.portfolio-container.portfolio-masonry .portfolio-item-container .portfolio-item figure img {
  width: 100%;
  height: auto;
}
.portfolio-container.portfolio-masonry .portfolio-item-container .portfolio-item .portfolio-item-details {
  position: absolute;
  bottom: 33px;
  left: 0;
  color: white;
  padding: 50px 30px 25px;
  margin-right: 33px;
  margin-left: 33px;
  background: rgba(0, 0, 0, 0.9);
  opacity: 0;
  visibility: hidden;
  -webkit-filter: blur(12px);
  filter: blur(12px);
  transform: translateY(10%) scale(0.9);
  -ms-transform: translateY(10%) scale(0.9);
  -webkit-transform: translateY(10%) scale(0.9);
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.portfolio-container.portfolio-masonry .portfolio-item-container .portfolio-item .portfolio-item-details .portfolio-item-name,
.portfolio-container.portfolio-masonry .portfolio-item-container .portfolio-item .portfolio-item-details p,
.portfolio-container.portfolio-masonry .portfolio-item-container .portfolio-item .portfolio-item-details a {
  color: white;
}
.portfolio-container.portfolio-masonry .portfolio-item-container .portfolio-item .portfolio-item-details a:hover {
  color: #ff4d4d;
}
.portfolio-container.portfolio-masonry .portfolio-item-container .portfolio-item .portfolio-item-details .portfolio-item-name {
  line-height: 1.6em;
}
.portfolio-container.portfolio-masonry .portfolio-item-container .portfolio-item:hover .portfolio-item-details {
  background: rgba(0, 0, 0, 0.9);
  -webkit-filter: blur(0);
  filter: blur(0);
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
  -ms-transform: translateY(0) scale(1);
  -webkit-transform: translateY(0) scale(1);
}
.portfolio-project-details .project-info .project-name {
  margin-bottom: 27px;
}
.portfolio-project-details .project-info .project-name h4,
.portfolio-project-details .project-info .project-name h6 {
  color: #444444;
}
.portfolio-project-details .project-info .project-name h6 {
  margin-bottom: 26px;
}
.portfolio-project-details .project-info .project-name h4 {
  line-height: 1.6em;
}
.portfolio-project-details .project-info .project-desc {
  margin-bottom: 33px;
}
.portfolio-project-details .project-info .project-details {
  margin-bottom: 34px;
}
.portfolio-project-details .project-info .project-details h6,
.portfolio-project-details .project-info .project-details span {
  color: #444444;
}
.portfolio-project-details .project-info .project-details span {
  font-style: italic;
  font-weight: 400;
}
.portfolio-project-details .project-info .related-project-container h6 {
  background: #f9f9f9;
  padding: 19px 25px;
}
.portfolio-project-details .project-info .related-project-container .related-project {
  margin-right: -5px;
}
.portfolio-project-details .project-info .related-project-container .related-project figure {
  padding: 0;
  padding-right: 5px;
  margin-bottom: 5px;
}
.portfolio-project-details .project-info .related-project-container .related-project figure .overlay a i {
  font-size: 1.3em;
}
.portfolio-project-details .related-project-container .related-project figure {
  padding: 0;
}
.portfolio-project-details .related-project-container .related-project figure img {
  width: 100%;
}
.portfolio-project-details figure {
  overflow: hidden;
}
.portfolio-project-details figure .overlay {
  display: inline-block;
  width: 101%;
  height: 101%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  background: rgba(255, 255, 255, 0.55);
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.portfolio-project-details figure .overlay a {
  color: #444444;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-80%);
  -ms-transform: translateX(-50%) translateY(-80%);
  -webkit-transform: translateX(-50%) translateY(-80%);
}
.portfolio-project-details figure .overlay a i {
  font-size: 1.8em;
}
.portfolio-project-details figure:hover .overlay {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  -ms-transform: translateY(0);
  -webkit-transform: translateY(0);
}
.portfolio-project-details figure:hover .overlay a {
  transform: translateX(-50%) translateY(-50%);
  -ms-transform: translateX(-50%) translateY(-50%);
  -webkit-transform: translateX(-50%) translateY(-50%);
}
.portfolio-project-details.single-column-image .project-image figure {
  margin-bottom: 60px;
}
.portfolio-project-details.single-column-image .project-image-carousel {
  position: relative;
}
.portfolio-project-details.single-column-image .project-image-carousel figure {
  margin-bottom: 0;
  transform: translateZ(0);
  -ms-transform: translateZ(0);
  -webkit-transform: translateZ(0);
}
.portfolio-project-details.single-column-image .project-image-carousel .owl-controls .owl-nav div {
  position: absolute;
  top: 50%;
  z-index: 10;
  font-weight: 700;
  text-align: center;
  color: white;
  display: inline-block;
  width: 48px;
  height: 48px;
  border: 2px solid white;
  padding-top: 12px;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -webkit-transition: all 0.3s ease;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
}
.portfolio-project-details.single-column-image .project-image-carousel .owl-controls .owl-nav div i {
  font-size: 1.5em;
}
.portfolio-project-details.single-column-image .project-image-carousel .owl-controls .owl-nav .owl-prev {
  left: 20px;
}
.portfolio-project-details.single-column-image .project-image-carousel .owl-controls .owl-nav .owl-next {
  float: right;
  right: 20px;
}
.portfolio-project-details.single-column-image .project-image-carousel .owl-controls .owl-nav div:hover {
  background: white;
  color: #444;
}
.portfolio-project-details.single-column-image.single-column-image-fullwidth .project-image {
  margin-bottom: 15px;
}
.portfolio-project-details.single-column-image.single-column-image-fullwidth .project-image figure {
  margin-bottom: 50px;
}
.portfolio-project-details.single-column-image.single-column-image-fullwidth .project-info .project-name {
  margin-bottom: 0;
}
.portfolio-project-details.single-column-image.single-column-image-fullwidth .project-info .project-name h6 {
  margin-bottom: 16px;
}
.portfolio-project-details.multi-column-image .project-image figure {
  margin-bottom: 35px;
}
.portfolio-project-details.multi-column-image.multi-column-image-fullwidth .project-image {
  margin-bottom: 15px;
}
.portfolio-project-details.info-right .project-info {
  float: right;
}
/*************************************************************
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
..................... 7. SHOP ......................
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
*************************************************************/
.product .product-details .product-info h6,
.product .product-details .product-info p {
  margin-bottom: 0;
}
.product .product-details .product-info h6 a {
  margin-bottom: 2px;
  text-transform: uppercase;
}
.product .product-details .product-info a:hover {
  color: #ff4d4d !important;
}
.product .product-details .product-info .product-cat a {
  font-family: 'Montserrat', sans-serif;
  font-weight: 400;
}
.product .product-details .product-price {
  text-align: center;
  padding-left: 0;
}
.product .product-details .product-price p {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  margin-bottom: 0;
}
.product .product-details .product-rating {
  margin-top: 13px;
}
.product .product-details .product-desc {
  margin-top: 33px;
}
.product .product-options form select,
.product .product-options form .fs-dropdown {
  margin-right: 20px;
  margin-bottom: 10px;
}
.product .product-options form select:last-child,
.product .product-options form .fs-dropdown:last-child {
  margin-right: 0;
}
.product .bottom-links a {
  display: inline-block;
  width: 39px;
  height: 46px;
  float: left;
  padding-top: 15px;
  margin-left: 1px;
  background: #222222;
  color: white;
  text-align: center;
  -webkit-transition: 0.1s all 0.001s ease-out;
  -moz-transition: 0.1s all 0.001s ease-out;
  transition: 0.1s all 0.001s ease-out;
}
.product .bottom-links a i {
  font-size: 1.4em;
}
.product .bottom-links a.add-to-cart {
  width: auto;
  padding-right: 17px;
  padding-left: 17px;
  background: #ff5049;
  text-transform: uppercase;
  font-weight: 300;
}
.product .bottom-links a.add-to-cart span {
  position: relative;
  bottom: 5px;
}
.product .bottom-links a.add-to-cart i {
  margin-right: 7px;
}
.product .bottom-links a.add-to-cart:hover {
  background: #444444;
}
.product .bottom-links a:hover {
  background: #444444;
}
.product.style1 .product-preview {
  position: relative;
  overflow: hidden;
  margin-bottom: 21px;
}
.product.style1 .product-preview .overlay {
  position: absolute;
  top: 0;
  left: 0;
  display: inline-block;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
  visibility: hidden;
  opacity: 0;
  transform: scale(0.8);
  -ms-transform: scale(0.8);
  -webkit-transform: scale(0.8);
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.product.style1 .product-preview .overlay .product-quickview {
  width: 200px;
  padding-right: 0;
  padding-left: 0;
  text-transform: uppercase;
  font-weight: 600;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-35%);
  -ms-transform: translateX(-50%) translateY(-35%);
  -webkit-transform: translateX(-50%) translateY(-35%);
  visibility: hidden;
  opacity: 0;
  -webkit-transition: 0.1s all 0.001s ease-out;
  -moz-transition: 0.1s all 0.001s ease-out;
  transition: 0.1s all 0.001s ease-out;
}
.product.style1 .product-preview .overlay .bottom-links {
  position: absolute;
  bottom: 0;
  right: 0;
}
.product.style1 .product-preview .overlay .bottom-links a {
  visibility: hidden;
  opacity: 0;
  color: white;
  transform: translateY(30%);
  -ms-transform: translateY(30%);
  -webkit-transform: translateY(30%);
}
.product.style1 .product-preview .overlay.fixed {
  background: none;
  opacity: 1;
  visibility: visible;
  transform: scale(1);
  -ms-transform: scale(1);
  -webkit-transform: scale(1);
}
.product.style1 .product-preview:hover .overlay {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
  -ms-transform: scale(1);
  -webkit-transform: scale(1);
}
.product.style1 .product-preview:hover .overlay .product-quickview {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-50%);
  -ms-transform: translateX(-50%) translateY(-50%);
  -webkit-transform: translateX(-50%) translateY(-50%);
  -webkit-transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.1s ease-out, opacity 0.5s 0.1s ease-out, -webkit-transform 0.5s 0.1s ease-out;
  -moz-transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.1s ease-out, opacity 0.5s 0.1s ease-out, -moz-transform 0.5s 0.1s ease-out;
  transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.1s ease-out, opacity 0.5s 0.1s ease-out, transform 0.5s 0.1s ease-out;
}
.product.style1 .product-preview:hover .overlay .bottom-links a.product-options,
.product.style1 .product-preview:hover .overlay .bottom-links a.heart,
.product.style1 .product-preview:hover .overlay .bottom-links a.add-to-cart {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  -ms-transform: translateY(0);
  -webkit-transform: translateY(0);
}
.product.style1 .product-preview:hover .overlay .bottom-links a.product-options {
  -webkit-transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.25s ease-out, opacity 0.5s 0.25s ease-out, -webkit-transform 0.5s 0.25s ease-out;
  -moz-transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.25s ease-out, opacity 0.5s 0.25s ease-out, -moz-transform 0.5s 0.25s ease-out;
  transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.25s ease-out, opacity 0.5s 0.25s ease-out, transform 0.5s 0.25s ease-out;
}
.product.style1 .product-preview:hover .overlay .bottom-links a.heart {
  -webkit-transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.35s ease-out, opacity 0.5s 0.35s ease-out, -webkit-transform 0.5s 0.35s ease-out;
  -moz-transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.35s ease-out, opacity 0.5s 0.35s ease-out, -moz-transform 0.5s 0.35s ease-out;
  transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.35s ease-out, opacity 0.5s 0.35s ease-out, transform 0.5s 0.35s ease-out;
}
.product.style1 .product-preview:hover .overlay .bottom-links a.add-to-cart {
  -webkit-transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.45s ease-out, opacity 0.5s 0.45s ease-out, -webkit-transform 0.5s 0.45s ease-out;
  -moz-transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.45s ease-out, opacity 0.5s 0.45s ease-out, -moz-transform 0.5s 0.45s ease-out;
  transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.45s ease-out, opacity 0.5s 0.45s ease-out, transform 0.5s 0.45s ease-out;
}
.product.style2 .product-preview {
  margin-top: 28px;
  margin-bottom: 35px;
}
.product .product-share {
  margin-top: 35px;
}
.product .product-share > p {
  display: inline-block;
  float: left;
  margin-right: 5px;
  margin-bottom: 0;
}
.product .product-share > p .plus-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background: #f8694d;
  color: white;
  text-align: center;
  margin-right: 5px;
}
.product .product-share > p .plus-icon i {
  font-size: 0.8em;
  display: inline-block;
  position: relative;
  top: -2px;
}
.product .product-share .share-container {
  display: inline-block;
}
.product .product-share .share-container > a {
  float: left;
  border: none;
  width: 16px;
  height: 16px;
  overflow: hidden;
  margin-right: 4px;
  color: white;
  text-align: center;
  background: #222;
  display: inline-block;
}
.product .product-share .share-container > a i {
  font-size: 0.8em;
  position: relative;
  top: -3px;
}
.product .product-share .share-container .facebook {
  background: #305891;
}
.product .product-share .share-container .twitter {
  background: #2ca8d2;
}
.product .product-share .share-container .googleplus {
  background: #dd4b39;
}
.product .product-share .share-container .linkedin {
  background: #007bb6;
}
.product .product-share .share-container .pinterest {
  background: #cb2027;
}
.product .product-share .share-container .stumbleupon {
  background: #eb4924;
}
.product .product-share .share-container .digg {
  background: #000000;
}
.product .product-share .share-container .delicious {
  background: #3399ff;
}
.product .product-share .share-container .buttons {
  float: left;
}
.product.single-product .product-details-container .product-info {
  margin-bottom: 30px;
}
.product.single-product .product-details-container .product-info h4 {
  margin-bottom: 16px;
}
.product.single-product .product-details-container .product-info .product-cat {
  font-size: 18px;
}
.product.single-product .product-details-container .product-review {
  float: right;
}
.product.single-product .product-details-container .product-review .review-counter {
  display: inline-block;
  margin-right: 35px;
}
.product.single-product .product-details-container .product-review .product-rating {
  display: inline-block;
}
.product.single-product .product-details-container .product-price {
  font-size: 24px;
  font-weight: 500;
  text-align: left;
  margin-bottom: 45px;
}
.product.single-product .product-details-container .product-price del {
  font-size: 18px;
  font-weight: 300;
  margin-right: 25px;
}
.product.single-product .product-details-container .product-description {
  margin-bottom: 42px;
}
.product.single-product .product-details-container .product-description > h6 {
  margin-bottom: 28px;
}
.product.single-product .product-details-container .product-description .row p {
  margin-bottom: 7px;
}
.product.single-product .product-options > h6 {
  margin-bottom: 43px;
}
.product.single-product .product-options form {
  margin-bottom: 55px;
}
.product.single-product .product-options form label {
  position: relative;
  top: 1em;
  margin-right: 5px;
}
.product.single-product .product-options form input {
  margin-right: 13px;
}
.product.single-product .product-options form .input-container {
  margin-bottom: 30px;
}
.product.single-product .product-options form .input-container:last-child {
  margin-bottom: 0;
}
.single-product-image-container .thumbnails,
.single-product-image-container .slider {
  position: relative;
  overflow: hidden;
}
.single-product-image-container .slider .flex-direction-nav {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
}
.single-product-image-container .thumbnails .flex-direction-nav li,
.single-product-image-container .slider .flex-direction-nav li {
  display: inline-block;
  width: 50px;
  height: 50px;
}
.single-product-image-container .thumbnails .flex-direction-nav li a,
.single-product-image-container .slider .flex-direction-nav li a {
  opacity: 1;
  position: relative;
  top: 20px;
  width: 100%;
  height: 100%;
  line-height: 3.6em;
  color: white;
  background: #444;
  text-align: center;
}
.single-product-image-container .thumbnails .flex-direction-nav li a:before,
.single-product-image-container .slider .flex-direction-nav li a:before {
  font-size: 30px;
  color: white;
  text-shadow: none;
}
.single-product-image-container .thumbnails .flex-direction-nav li a:hover,
.single-product-image-container .slider .flex-direction-nav li a:hover {
  background: #ff4d4d;
}
.single-product-image-container .thumbnails .flex-direction-nav li a:active,
.single-product-image-container .slider .flex-direction-nav li a:active {
  outline: none;
}
.single-product-image-container .thumbnails .flex-direction-nav li.flex-nav-prev,
.single-product-image-container .slider .flex-direction-nav li.flex-nav-prev,
.single-product-image-container .thumbnails .flex-direction-nav li.flex-nav-next,
.single-product-image-container .slider .flex-direction-nav li.flex-nav-next {
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.single-product-image-container .thumbnails .flex-direction-nav li.flex-nav-prev,
.single-product-image-container .slider .flex-direction-nav li.flex-nav-prev {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%) translateX(-100%);
  -ms-transform: translateY(-50%) translateX(-100%);
  -webkit-transform: translateY(-50%) translateX(-100%);
}
.single-product-image-container .thumbnails .flex-direction-nav li.flex-nav-next,
.single-product-image-container .slider .flex-direction-nav li.flex-nav-next {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%) translateX(100%);
  -ms-transform: translateY(-50%) translateX(100%);
  -webkit-transform: translateY(-50%) translateX(100%);
}
.single-product-image-container .thumbnails:hover .flex-direction-nav li.flex-nav-prev,
.single-product-image-container .slider:hover .flex-direction-nav li.flex-nav-prev,
.single-product-image-container .thumbnails:hover .flex-direction-nav li.flex-nav-next,
.single-product-image-container .slider:hover .flex-direction-nav li.flex-nav-next {
  opacity: 1;
  visibility: visible;
  transform: translateY(-50%) translateX(0);
  -ms-transform: translateY(-50%) translateX(0);
  -webkit-transform: translateY(-50%) translateX(0);
}
.single-product-image-container .thumbnails .flex-direction-nav {
  top: 50%;
}
.single-product-image-container .thumbnails .slides li {
  cursor: pointer;
  opacity: 0.7 !important;
  z-index: 0 !important;
  width: 100px !important;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.single-product-image-container .thumbnails .slides li.flex-active-slide,
.single-product-image-container .thumbnails .slides li:hover {
  opacity: 1 !important;
}
.single-product-image-container .slider .slides li {
  min-width: 1px;
}
.single-product-image-container .slider .slides li.flex-active-slide .overlay.fixed .bottom-links a {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  -ms-transform: translateY(0);
  -webkit-transform: translateY(0);
}
.single-product-image-container .slider .slides li.flex-active-slide .overlay.fixed .bottom-links a.product-options {
  -webkit-transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.4s ease-out, opacity 0.5s 0.4s ease-out, -webkit-transform 0.5s 0.4s ease-out;
  -moz-transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.4s ease-out, opacity 0.5s 0.4s ease-out, -moz-transform 0.5s 0.4s ease-out;
  transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.4s ease-out, opacity 0.5s 0.4s ease-out, transform 0.5s 0.4s ease-out;
}
.single-product-image-container .slider .slides li.flex-active-slide .overlay.fixed .bottom-links a.heart {
  -webkit-transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.6s ease-out, opacity 0.5s 0.6s ease-out, -webkit-transform 0.5s 0.6s ease-out;
  -moz-transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.6s ease-out, opacity 0.5s 0.6s ease-out, -moz-transform 0.5s 0.6s ease-out;
  transition: color 0.5s 0.001s ease-out, background 0.5s 0.001s ease-out, visibility 0.5s 0.6s ease-out, opacity 0.5s 0.6s ease-out, transform 0.5s 0.6s ease-out;
}
.single-product-image-container.thumbnails-bottom .slider .slides li .product-preview {
  margin-bottom: 30px !important;
}
.single-product-image-container.thumbnails-bottom .thumbnails .slides li {
  margin-right: 10px !important;
}
.single-product-image-container.thumbnails-left:before,
.single-product-image-container.thumbnails-right:before,
.single-product-image-container.thumbnails-left:after,
.single-product-image-container.thumbnails-right:after {
  content: ' ';
  display: table;
}
.single-product-image-container.thumbnails-left:after,
.single-product-image-container.thumbnails-right:after {
  clear: both;
}
.single-product-image-container.thumbnails-left .slider,
.single-product-image-container.thumbnails-right .slider {
  width: 80%;
  float: right;
}
.single-product-image-container.thumbnails-left .slider .slides li .product-preview,
.single-product-image-container.thumbnails-right .slider .slides li .product-preview {
  margin-bottom: 0;
}
.single-product-image-container.thumbnails-left .thumbnails,
.single-product-image-container.thumbnails-right .thumbnails {
  width: 19%;
  float: left;
}
.single-product-image-container.thumbnails-left .thumbnails .slides li,
.single-product-image-container.thumbnails-right .thumbnails .slides li {
  display: block;
  float: none !important;
  margin-bottom: 15px !important;
}
.single-product-image-container.thumbnails-left .thumbnails .slides li:last-child,
.single-product-image-container.thumbnails-right .thumbnails .slides li:last-child {
  margin-bottom: 0 !important;
}
.single-product-image-container.thumbnails-left .thumbnails .flex-direction-nav li {
  left: 47%;
}
.single-product-image-container.thumbnails-right .thumbnails .flex-direction-nav li {
  left: 56%;
}
.sidebar-left .single-product-image-container.thumbnails-right .thumbnails .flex-direction-nav li,
.sidebar-right .single-product-image-container.thumbnails-right .thumbnails .flex-direction-nav li {
  left: 49%;
}
.sidebar-left .single-product-image-container.thumbnails-left .thumbnails .flex-direction-nav li,
.sidebar-right .single-product-image-container.thumbnails-left .thumbnails .flex-direction-nav li {
  left: 51%;
}
.single-product-image-container.thumbnails-left .thumbnails .flex-direction-nav li a,
.single-product-image-container.thumbnails-right .thumbnails .flex-direction-nav li a {
  transform: rotateZ(90deg);
  -ms-transform: rotateZ(90deg);
  -webkit-transform: rotateZ(90deg);
}
.single-product-image-container.thumbnails-left .thumbnails .flex-direction-nav li.flex-nav-prev,
.single-product-image-container.thumbnails-right .thumbnails .flex-direction-nav li.flex-nav-prev {
  top: 0;
  transform: translateY(-100%) translateX(-50%);
  -ms-transform: translateY(-100%) translateX(-50%);
  -webkit-transform: translateY(-100%) translateX(-50%);
}
.single-product-image-container.thumbnails-left .thumbnails .flex-direction-nav li.flex-nav-next,
.single-product-image-container.thumbnails-right .thumbnails .flex-direction-nav li.flex-nav-next {
  right: auto;
  top: auto;
  bottom: 0;
  transform: translateY(100%) translateX(-50%);
  -ms-transform: translateY(100%) translateX(-50%);
  -webkit-transform: translateY(100%) translateX(-50%);
}
.single-product-image-container.thumbnails-left .thumbnails:hover .flex-direction-nav li.flex-nav-prev,
.single-product-image-container.thumbnails-right .thumbnails:hover .flex-direction-nav li.flex-nav-prev,
.single-product-image-container.thumbnails-left .thumbnails:hover .flex-direction-nav li.flex-nav-next,
.single-product-image-container.thumbnails-right .thumbnails:hover .flex-direction-nav li.flex-nav-next {
  transform: translateY(0) translateX(-50%);
  -ms-transform: translateY(0) translateX(-50%);
  -webkit-transform: translateY(0) translateX(-50%);
}
.single-product-image-container.thumbnails-left .slider {
  float: right;
}
.single-product-image-container.thumbnails-left .thumbnails {
  float: left;
}
.single-product-image-container.thumbnails-right .slider {
  float: left;
}
.single-product-image-container.thumbnails-right .thumbnails {
  float: right;
}
.single-product-image-container.thumbnails-right .thumbnails .slides {
  float: right;
  width: auto !important;
}
.collection-container {
  padding-right: 0;
  padding-left: 0;
  height: 100%;
}
.collection-container .collection {
  position: relative;
  min-height: 490px;
}
.collection-container .collection.fullheight {
  height: 100%;
}
.collection-container .collection.tall {
  height: 980px;
}
.collection-container .collection.medium {
  height: 640px;
}
.collection-container .collection.short {
  height: 490px;
}
.collection-container .collection .collection-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
}
.collection-container .collection .contents {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translateX(-50%) translateY(-50%);
  -moz-transform: translateX(-50%) translateY(-50%);
  transform: translateX(-50%) translateY(-50%);
  color: white;
  text-align: center;
  width: 80%;
}
.collection-container .collection .contents h3 {
  font-size: 32px;
}
.collection-container .collection .contents .button {
  margin-top: 18px;
}
.product-filter-container ul li {
  display: inline-block;
  margin-left: 7px;
  margin-bottom: 10px;
}
.product-filter-container ul li span {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  text-transform: uppercase;
  display: inline-block;
  padding: 11px 24px;
  border: 1px solid #676767;
  cursor: pointer;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.product-filter-container ul li span:hover {
  background: #444444;
  color: white;
}
.product-filter-container ul li.active span {
  background: #444444;
  color: white;
}
.product-filter-container.product-filter-container-style2,
.product-filter-container.product-filter-container-style3 {
  overflow: hidden;
}
.product-filter-container.product-filter-container-style2 ul,
.product-filter-container.product-filter-container-style3 ul {
  display: inline-block;
  margin: 0 auto;
  position: relative;
}
.product-filter-container.product-filter-container-style2 ul:before,
.product-filter-container.product-filter-container-style3 ul:before {
  content: '';
  display: inline-block;
  width: 100%;
  height: 1px;
  background: #ddd;
  position: absolute;
  top: 35%;
  left: -100%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  transform: translateY(-50%);
}
.product-filter-container.product-filter-container-style2 ul:after,
.product-filter-container.product-filter-container-style3 ul:after {
  content: '';
  display: inline-block;
  width: 100%;
  height: 1px;
  background: #ddd;
  position: absolute;
  top: 35%;
  right: -100%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  transform: translateY(-50%);
}
.product-filter-container.product-filter-container-style2 ul li,
.product-filter-container.product-filter-container-style3 ul li {
  margin-left: 25px;
  margin-right: 25px;
}
.product-filter-container.product-filter-container-style2 ul li span,
.product-filter-container.product-filter-container-style3 ul li span {
  font-weight: 400;
  border: none;
  padding: 0;
  position: relative;
  color: #444;
}
.product-filter-container.product-filter-container-style2 ul li span:before,
.product-filter-container.product-filter-container-style3 ul li span:before {
  content: '';
  display: inline-block;
  width: 5px;
  height: 5px;
  background: #444;
  position: relative;
  top: -2px;
  margin-right: 6px;
}
.product-filter-container.product-filter-container-style2 ul li span:hover,
.product-filter-container.product-filter-container-style3 ul li span:hover {
  background: none;
  color: inherit;
  font-weight: 600;
}
.product-filter-container.product-filter-container-style2 ul li.active span,
.product-filter-container.product-filter-container-style3 ul li.active span {
  background: none;
  font-weight: 600;
}
.product-filter-container.product-filter-container-style3 ul:before,
.product-filter-container.product-filter-container-style3 ul:after {
  content: none;
}
.product-filter-controls select {
  background-color: #f9f9f9;
  border: 1px solid #eee;
  padding: 15px 11px 14px 18px;
  min-width: 200px;
  margin-right: 30px;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.product-filter-controls select:focus {
  border-color: #bbb;
  outline: none;
}
.product-filter-controls .product-view-controls {
  margin-top: 16px;
}
.product-filter-controls .product-view-controls li {
  display: inline-block;
  margin-right: 12px;
}
.product-filter-controls .product-view-controls li:last-child {
  margin-right: 0;
}
.product-filter-controls .product-view-controls li a {
  color: #444;
}
.product-filter-controls .product-view-controls li a:hover {
  color: #ff4d4d;
}
.product-filter-controls .product-view-controls li a i {
  font-size: 1.1em;
}
.product-filter-controls .product-view-controls li.product-compare i {
  margin-right: 3px;
}
.products-container .product {
  margin-bottom: 55px;
}
.products-container.product-category.grid-view .product {
  margin-bottom: 83px;
}
.products-container.product-category.grid-view.sidebar-on .product {
  margin-bottom: 68px;
}
.products-container.product-category.list-view .product {
  padding-bottom: 50px;
  margin-bottom: 70px;
  border-bottom: 1px solid #ddd;
}
.products-container.product-category.list-view div[class^="col-"]:last-child .product {
  border-bottom: none;
}
.products-container.product-category.list-view.sidebar-on .product {
  padding-bottom: 30px;
  margin-bottom: 50px;
}
.product-slider {
  position: relative;
}
.product-slider .owl-controls {
  position: absolute;
  bottom: 10px;
  left: 50%;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
  transform: translateY(5px) translateX(-50%);
  -ms-transform: translateY(5px) translateX(-50%);
  -webkit-transform: translateY(5px) translateX(-50%);
}
.product-slider .owl-controls .owl-dots .owl-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  background: rgba(255, 255, 255, 0.5);
  -webkit-border-radius: 50em;
  -moz-border-radius: 50em;
  border-radius: 50em;
  margin: 0 3px;
}
.product-slider .owl-controls .owl-dots .owl-dot.active {
  background: rgba(255, 255, 255, 0.9);
}
.product-slider:hover .owl-controls {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) translateX(-50%);
  -ms-transform: translateY(0) translateX(-50%);
  -webkit-transform: translateY(0) translateX(-50%);
}
.shopping-cart .shopping-cart-table {
  width: 100%;
}
.shopping-cart .shopping-cart-table thead {
  border-bottom: 1px solid #dddddd;
}
.shopping-cart .shopping-cart-table thead th {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  padding: 0 20px 20px;
}
.shopping-cart .shopping-cart-table tbody tr {
  border-bottom: 1px solid #dddddd;
}
.shopping-cart .shopping-cart-table tbody tr:last-child {
  border-bottom: none;
}
.shopping-cart .shopping-cart-table tbody td {
  font-weight: 500;
  padding: 50px 20px 55px;
  vertical-align: middle;
}
.shopping-cart .shopping-cart-table tbody td input {
  font-weight: 400;
}
.shopping-cart .shopping-cart-table tbody .product-image img {
  max-width: 100px;
}
.shopping-cart .shopping-cart-table tbody .product-name {
  font-weight: 400;
}
.shopping-cart .shopping-cart-table tbody .product-name h6 {
  margin-bottom: 3px;
}
.shopping-cart .shopping-cart-table tbody .product-name ul {
  padding-left: 12px;
  margin-bottom: 0;
}
.shopping-cart .shopping-cart-table tbody .refresh-quantity {
  font-size: 0.7em;
  text-shadow: 0 0 1px #000;
  color: #000;
  margin-top: 14px;
  display: inline-block;
  position: relative;
  top: -4px;
  margin-left: 15px;
}
.shopping-cart .shopping-cart-table tbody .remove-item {
  font-size: 1.25em;
  color: #e55793;
  text-shadow: 0 0 1px #e55793;
  margin-top: 17px;
  display: inline-block;
}
.shopping-cart .cart-next-step {
  margin-bottom: 35px;
}
.shopping-cart .cart-next-step p {
  margin-bottom: 6px;
}
.shopping-cart .cart-next-step input {
  margin-right: 7px;
}
.shopping-cart .cart-totals-container {
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  padding-top: 30px;
  padding-bottom: 20px;
  margin-bottom: 70px;
}
.shopping-cart .cart-totals-container p {
  text-align: right;
  margin-bottom: 3px;
}
.shopping-cart .cart-totals-container p span {
  font-weight: 500;
  color: #ff5048;
  margin-left: 45px;
}
.shopping-cart.two-columns .shopping-cart-table {
  margin-bottom: 10px;
}
.shopping-cart.two-columns .cart-totals-container {
  margin-bottom: 0;
}
.shopping-cart.two-columns .cart-next-step {
  margin-bottom: 60px;
}
.shopping-cart.two-columns .cart-next-step .input-container {
  margin-bottom: 7px;
}
.checkout-container .checkout-step .step-name {
  background: #f9f9f9;
  border: 1px solid #eee;
  padding: 20px;
  margin-bottom: 10px;
}
.checkout-container .checkout-step .step-name h6 {
  margin-bottom: 0;
}
.checkout-container .checkout-step .step-name.active {
  background: #eee;
  border-color: #ddd;
}
.checkout-container.checkout-container-style1 .step-contents {
  margin-top: 40px;
  margin-bottom: 90px;
}
.product-quickview-container {
  background: white;
  padding: 27px !important;
  position: relative;
}
.sidebar-left .product.single-product .product-details .product-info h4,
.sidebar-right .product.single-product .product-details .product-info h4 {
  margin-bottom: 11px;
}
.sidebar-left .product.single-product .product-details .product-rating,
.sidebar-right .product.single-product .product-details .product-rating {
  margin-top: 35px;
}
.sidebar-left .single-product-image-container.thumbnails-bottom .slider .slides li .product-preview,
.sidebar-right .single-product-image-container.thumbnails-bottom .slider .slides li .product-preview {
  margin-bottom: 20px !important;
}
.sidebar-left .single-product-image-container.thumbnails-left .slider,
.sidebar-right .single-product-image-container.thumbnails-left .slider,
.sidebar-left .single-product-image-container.thumbnails-right .slider,
.sidebar-right .single-product-image-container.thumbnails-right .slider {
  width: 75%;
  float: right;
}
.sidebar-left .single-product-image-container.thumbnails-left .slider .slides li .product-preview,
.sidebar-right .single-product-image-container.thumbnails-left .slider .slides li .product-preview,
.sidebar-left .single-product-image-container.thumbnails-right .slider .slides li .product-preview,
.sidebar-right .single-product-image-container.thumbnails-right .slider .slides li .product-preview {
  margin-bottom: 0;
}
.sidebar-left .single-product-image-container.thumbnails-left .thumbnails,
.sidebar-right .single-product-image-container.thumbnails-left .thumbnails,
.sidebar-left .single-product-image-container.thumbnails-right .thumbnails,
.sidebar-right .single-product-image-container.thumbnails-right .thumbnails {
  width: 30%;
  float: left;
}
.sidebar-left .single-product-image-container.thumbnails-left .thumbnails .slides li,
.sidebar-right .single-product-image-container.thumbnails-left .thumbnails .slides li,
.sidebar-left .single-product-image-container.thumbnails-right .thumbnails .slides li,
.sidebar-right .single-product-image-container.thumbnails-right .thumbnails .slides li {
  display: block;
  float: none !important;
  margin-bottom: 20px !important;
}
.sidebar-left .single-product-image-container.thumbnails-left + .product-details-container,
.sidebar-right .single-product-image-container.thumbnails-left + .product-details-container,
.sidebar-left .single-product-image-container.thumbnails-right + .product-details-container,
.sidebar-right .single-product-image-container.thumbnails-right + .product-details-container {
  padding-left: 45px;
}
.sidebar-left .single-product-image-container.thumbnails-left .thumbnails .slides li,
.sidebar-right .single-product-image-container.thumbnails-left .thumbnails .slides li,
.sidebar-left .single-product-image-container.thumbnails-right .thumbnails .slides li,
.sidebar-right .single-product-image-container.thumbnails-right .thumbnails .slides li {
  margin-bottom: 16px !important;
}
.sidebar-left .single-product-image-container.thumbnails-left .thumbnails .slides li:last-child,
.sidebar-right .single-product-image-container.thumbnails-left .thumbnails .slides li:last-child,
.sidebar-left .single-product-image-container.thumbnails-right .thumbnails .slides li:last-child,
.sidebar-right .single-product-image-container.thumbnails-right .thumbnails .slides li:last-child {
  margin-bottom: 0 !important;
}
.sidebar-left .single-product-image-container.thumbnails-left .slider,
.sidebar-right .single-product-image-container.thumbnails-left .slider {
  float: right;
  margin-right: -30px;
}
.sidebar-left .single-product-image-container.thumbnails-left .slider .flex-viewport,
.sidebar-right .single-product-image-container.thumbnails-left .slider .flex-viewport {
  float: right;
}
.sidebar-left .single-product-image-container.thumbnails-left .thumbnails,
.sidebar-right .single-product-image-container.thumbnails-left .thumbnails {
  float: left;
}
.sidebar-left .single-product-image-container.thumbnails-right .slider,
.sidebar-right .single-product-image-container.thumbnails-right .slider {
  float: left;
}
.sidebar-left .single-product-image-container.thumbnails-right .slider .flex-viewport,
.sidebar-right .single-product-image-container.thumbnails-right .slider .flex-viewport {
  float: left;
}
.sidebar-left .single-product-image-container.thumbnails-right .thumbnails,
.sidebar-right .single-product-image-container.thumbnails-right .thumbnails {
  float: right;
  margin-right: -30px;
}
.sidebar-left .single-product-image-container.thumbnails-right .thumbnails .slides,
.sidebar-right .single-product-image-container.thumbnails-right .thumbnails .slides {
  float: right;
}
.w1740 .product .single-product-image-container .thumbnails .slides li {
  width: 150px !important;
  margin-bottom: 25px !important;
}
.w1740 .product .single-product-image-container .thumbnails .slides li:last-child {
  margin-bottom: 0 !important;
}
.w1740 .product .product-details-container .product-info {
  margin-bottom: 36px;
}
.w1740 .product .product-details-container .product-info h4 {
  margin-bottom: 10px;
}
.w1740 .product .product-details-container .product-description > h6 {
  margin-bottom: 28px;
}
.w1740 .product .product-details-container .product-description .row p {
  margin-bottom: 10px;
}
.w1740 .product .product-details-container .product-options form {
  margin-bottom: 0;
}
.w1740 .product-quickview-container .product .product-details-container .product-options form {
  margin-bottom: 55px;
}
/*************************************************************
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
.................. 8. MAIN SUBSCRIBE FORM ...................
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
*************************************************************/
.subscribe-form-container-style2 {
  padding-right: 40px;
  padding-left: 40px;
}
.subscribe-form-container-style2 header i {
  float: left;
  margin-right: 25px;
  margin-bottom: 0;
}
.subscribe-form-container-style2 header h4 {
  margin-bottom: 10px;
}
.subscribe-form-container-style2 header p:last-child {
  margin-bottom: 0;
}
.subscribe-form-container-style2 .main-subscribe-form {
  width: 100%;
}
.subscribe-form-container-style2 header .icon-container,
.subscribe-form-container-style3 header .icon-container {
  float: left;
  margin-right: 25px;
  margin-bottom: 0;
}
.subscribe-form-container-style2 header .icon-container i {
  margin-right: 0;
}
.subscribe-form-container-style3 header .icon-container i {
  margin-bottom: 0;
}
.subscribe-form-container-style2 header .contents,
.subscribe-form-container-style3 header .contents {
  overflow: hidden;
}
.subscribe-form-container-style3 header h4 {
  margin-bottom: 10px;
}
.subscribe-form-container-style3 header p:last-child {
  margin-bottom: 0;
}
.subscribe-form-container-style3 .main-subscribe-form {
  width: 100%;
}
.main-subscribe-form {
  text-align: right;
}
.main-subscribe-form.main-subscribe-form-style3 {
  text-align: center;
}
.main-subscribe-form #subscribe-email {
  width: 470px;
  height: 60px;
  background: white;
  border: 1px solid #ddd;
  padding-right: 17px;
  padding-left: 17px;
  margin-right: 7px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.main-subscribe-form #subscribe-email:focus {
  border-color: #4d4d4d;
  outline: none;
}
.main-subscribe-form input[type=submit] {
  height: 60px;
  background: #ff4d4d;
  color: white;
  border: none;
  text-transform: uppercase;
  padding-right: 44px;
  padding-left: 44px;
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.main-subscribe-form.main-subscribe-form-style2 #subscribe-email {
  width: 375px;
}
.main-subscribe-form.main-subscribe-form-style2 input[type=submit] {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  background: white;
  color: #444;
  border: 2px solid #4d4d4d;
  padding-right: 30px;
  padding-left: 30px;
}
.main-subscribe-form.main-subscribe-form-style2 input[type=submit]:hover {
  background: #222;
  border-color: #222;
  color: white;
}
.subscribe-form-result {
  font-family: 'Montserrat', sans-serif;
  font-weight: 400;
  margin-top: 10px;
}
.subscribe-form-result a {
  color: #ff4d4d;
}
/*************************************************************
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
..................... 9. 404 PAGE ......................
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
*************************************************************/
.error404-container {
  text-align: center;
}
.error404-container .error-icon {
  float: left;
}
.error404-container .error-icon i {
  font-size: 7em;
  position: relative;
  top: 31px;
}
.error404-container .error-message {
  overflow: hidden;
  float: left;
  margin-left: 37px;
}
.error404-container .error-message h1 {
  font-size: 80px;
  margin-bottom: 8px;
}
.error404-container .error-message h2 {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: normal;
  text-transform: none;
  margin-bottom: 41px;
}
.error404-container .error-message .button {
  padding: 16px 45px;
}
/*************************************************************
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
..................... 10. MAIN FOOTER ......................
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
*************************************************************/
/* BizFunctional */
.main-footer {
  border-bottom: 1px solid #333;
  color: #999;
  font-weight: 400;
  font-size: 15px;
}
/* BizFunctional */
.main-footer-links {
  margin: 25px 7px;
  font-weight: bold;
  font-size: 16px;
}
/* BizFunctional */
.social-icons {
  margin-left: 20px;
}
/* BizFunctional */
.social-icons-sm {
  margin-left: 3px;
}
/* BizFunctional */
.main-footer a {
  color: #999;
}
.main-footer .widget .widget-title {
  color: #aaa;
  position: relative;
  margin-bottom: 53px;
}
.main-footer .widget .widget-title.short-underline {
  margin-bottom: 40px;
  padding-bottom: 22px;
}
.main-footer .widget .widget-title.short-underline:after {
  content: '';
  display: inline-block;
  width: 60px;
  height: 1px;
  background: #aaa;
  position: absolute;
  bottom: 0;
  left: 0;
}
.main-footer .widget .widget-title.long-underline {
  margin-bottom: 57px;
  padding-bottom: 15px;
}
.main-footer .widget .widget-title.long-underline:after {
  content: '';
  display: inline-block;
  width: 85px;
  height: 1px;
  background: #aaa;
  position: absolute;
  bottom: 0;
  left: 0;
}
.main-footer .widget .widget-title.left-red-border {
  padding-left: 11px;
}
.main-footer .widget .widget-title.left-red-border:before {
  content: '';
  display: inline-block;
  width: 3px;
  height: 20px;
  position: absolute;
  top: 0;
  left: 0;
  background: #ff4c4c;
}
.main-footer .widget .widget-title.carrot-down {
  padding-bottom: 24px;
}
.main-footer .widget .widget-title.carrot-down:after {
  content: '';
  display: inline-block;
  width: 0;
  height: 0;
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
  border-top: 9px solid #bdbdbd;
  border-right: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 5px solid transparent;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.main-footer .widget .widget-title.sides-line span:first-child {
  position: relative;
}
.main-footer .widget .widget-title.sides-line span:first-child:after {
  content: '';
  display: inline-block;
  width: 54px;
  height: 1px;
  position: absolute;
  top: 9px;
  left: 123%;
  background: #222222;
}
.main-footer .widget .widget-title.sides-line span:first-child:before {
  content: '';
  display: inline-block;
  width: 54px;
  height: 1px;
  position: absolute;
  top: 9px;
  right: 123%;
  background: #222222;
}
.main-footer .widget .widget-title.sides-line span:nth-child(2) {
  display: block;
  margin-top: 19px;
}
.main-footer .widget .widget-title.updown-lines {
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  margin-bottom: 35px ;
  padding-top: 22px;
  padding-bottom: 22px;
}
.main-footer .widget .button {
  color: #aaa;
  border: 1px solid #666;
  padding: 13px 40px;
}
.main-footer .widget .button:hover {
  color: #222222;
  background: white;
  border-color: white;
}
.main-footer .widget.bordered-bottom {
  border-bottom: 1px solid #333;
}
.main-footer .widget-about .logo {
  display: inline-block;
}
.main-footer .widget-about .read-more-button-style1 {
  font-weight: 500;
  padding-left: 38px;
  margin-top: 16px;
  display: block;
  position: relative;
  color: #bdbdbd;
}
.main-footer .widget-about .read-more-button-style1:before {
  content: 'î†­';
  font-family: 'knight';
  line-height: 1.8em;
  text-align: center;
  color: white;
  background: #ff4d4d;
  display: inline-block;
  width: 24px;
  height: 24px;
  position: absolute;
  top: 0;
  left: 0;
  -webkit-border-radius: 50em;
  -moz-border-radius: 50em;
  border-radius: 50em;
}
.main-footer .widget-about .read-more-button-style2 {
  display: block;
  text-transform: uppercase;
  font-weight: 700;
  margin-top: 11px;
  color: #ff4c4c;
}
.main-footer .widget-about p.highlights {
  border-bottom: 1px solid #333;
  padding-bottom: 25px;
}
.main-footer .widget-about p.highlights strong {
  font-size: 18px;
  font-weight: 400;
  line-height: 1.8em;
  color: #aaa;
  display: block;
}
.main-footer .widget-about.bordered-bottom {
  padding-bottom: 7px;
}
.main-footer .widget-contact-info.widget-contact-info-style1 address p {
  line-height: 2.7em;
  margin-bottom: 0;
}
.main-footer .widget-contact-info.widget-contact-info-style1 address p i {
  font-size: 1.2em;
  margin-right: 12px;
  position: relative;
  top: 2px;
  float: left;
  display: inline-block;
  width: 6%;
}
.main-footer .widget-contact-info.widget-contact-info-style1 address p i.large-icon {
  font-size: 1.6em;
}
.main-footer .widget-contact-info.widget-contact-info-style1 address p span {
  float: left;
  overflow: hidden;
  display: inline-block;
  width: 89%;
}
.main-footer .widget-contact-info.widget-contact-info-style1 address p a {
  color: #ff4d4d;
}
.main-footer .widget-contact-info.widget-contact-info-style1.big-icons address p i {
  font-size: 1.6em;
}
.main-footer .widget-contact-info.widget-contact-info-style2 address p {
  border-bottom: 1px solid #444;
  padding-bottom: 22px;
  margin-bottom: 16px;
}
.main-footer .widget-contact-info.widget-contact-info-style2 address p strong,
.main-footer .widget-contact-info.widget-contact-info-style2 address p span {
  display: block;
}
.main-footer .widget-contact-info.widget-contact-info-style2 address p strong {
  font-weight: 500;
  color: #aaa;
  margin-bottom: 12px;
}
.main-footer .widget-contact-info.widget-contact-info-style2 address p:last-child {
  border-bottom: none;
}
.main-footer .widget-contact-form form ::-webkit-input-placeholder {
  color: #666;
}
.main-footer .widget-contact-form form :-moz-placeholder {
  color: #666;
}
.main-footer .widget-contact-form form ::-moz-placeholder {
  color: #666;
}
.main-footer .widget-contact-form form :-ms-input-placeholder {
  color: #666;
}
.main-footer .widget-contact-form form input,
.main-footer .widget-contact-form form textarea {
  background: none;
  border: 1px solid #333;
  width: 100%;
  color: white;
  position: relative;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.main-footer .widget-contact-form form input:focus,
.main-footer .widget-contact-form form textarea:focus {
  outline: none;
}
.main-footer .widget-contact-form form input + i {
  line-height: 1em;
  position: absolute;
  top: 40%;
  right: 25px;
  font-size: 1.5em;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
}
.main-footer .widget-contact-form form input.email + i {
  font-size: 1.1em;
}
.main-footer .widget-contact-form form input[type=submit] {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  text-transform: uppercase;
  color: #666;
  width: auto;
  float: right;
  margin-right: 15px;
}
.main-footer .widget-contact-form form .form-results{
  margin-top: 20px;
}
.main-footer .widget-contact-form form .form-results a{
  color: #ff4d4d;
}
.main-footer .widget-contact-form .status-container {
  padding-top: 24px;
}
.main-footer .widget-contact-form .status-container .statusMessage {
  display: none;
  font-weight: 600;
}
.main-footer .widget-contact-form.widget-contact-form-style1 input,
.main-footer .widget-contact-form.widget-contact-form-style1 textarea {
  border: 1px solid #333;
  padding: 18px 24px;
  margin-bottom: 35px;
}
.main-footer .widget-contact-form.widget-contact-form-style1 input:focus,
.main-footer .widget-contact-form.widget-contact-form-style1 textarea:focus {
  border-color: #aaa;
}
.main-footer .widget-contact-form.widget-contact-form-style1 input[type=submit] {
  margin-top: 5px;
  margin-bottom: 0;
  padding-right: 40px;
  padding-left: 40px;
}
.main-footer .widget-contact-form.widget-contact-form-style1 input[type=submit]:hover {
  border-color: #aaa;
  color: white;
}
.main-footer .widget-contact-form.widget-contact-form-style1 input[type=submit]:hover:active {
  background: white;
  border-color: white;
  color: #222222;
}
.main-footer .widget-contact-form.widget-contact-form-style2 input,
.main-footer .widget-contact-form.widget-contact-form-style2 textarea {
  padding: 18px 24px;
  margin-bottom: 21px;
  position: relative;
}
.main-footer .widget-contact-form.widget-contact-form-style2 input:focus,
.main-footer .widget-contact-form.widget-contact-form-style2 textarea:focus {
  border-color: #fe4c4c;
}
.main-footer .widget-contact-form.widget-contact-form-style2 input[type=submit] {
  margin-top: 5px;
  margin-bottom: 0;
  padding-right: 40px;
  padding-left: 40px;
}
.main-footer .widget-contact-form.widget-contact-form-style2 input[type=submit]:hover {
  border-color: #fe4c4c;
  color: white;
}
.main-footer .widget-contact-form.widget-contact-form-style2 input[type=submit]:hover:active {
  background: #fe4c4c;
  border-color: #fe4c4c;
  color: white;
}
.main-footer .widget-contact-form.widget-contact-form-style3 input,
.main-footer .widget-contact-form.widget-contact-form-style3 textarea {
  padding: 13px 17px;
  margin-bottom: 21px;
  position: relative;
}
.main-footer .widget-contact-form.widget-contact-form-style3 input:focus,
.main-footer .widget-contact-form.widget-contact-form-style3 textarea:focus {
  border-color: #fe4c4c;
}
.main-footer .widget-contact-form.widget-contact-form-style3 input[type=submit] {
  width: 100%;
  margin-top: 5px;
  margin-right: 0;
  margin-bottom: 0;
  padding-right: 40px;
  padding-left: 40px;
  float: none;
}
.main-footer .widget-contact-form.widget-contact-form-style3 input[type=submit]:hover {
  border-color: #fe4c4c;
  color: white;
}
.main-footer .widget-contact-form.widget-contact-form-style3 input[type=submit]:hover:active {
  background: #fe4c4c;
  border-color: #fe4c4c;
  color: white;
}
.main-footer .widget-socials {
  display: inline-block;
}
.main-footer .widget-socials ul {
  margin-bottom: 0;
}
.main-footer .widget-socials ul li {
  float: left;
}
.main-footer .widget-socials ul li a {
  color: #666;
}
.main-footer .widget-socials ul li a:hover {
  color: white;
}
.main-footer .widget-socials.widget-socials-big ul li {
  margin-left: 22px;
}
.main-footer .widget-socials.widget-socials-big ul li a i {
  font-size: 1.65em;
  position: relative;
  top: 10px;
}
.main-footer .widget-socials.widget-socials-big ul li:first-child {
  margin-left: 0;
}
.main-footer .widget-socials.widget-socials-style1 ul li {
  margin-left: 22px;
}
.main-footer .widget-socials.widget-socials-style1 ul li a i {
  font-size: 1.35em;
  position: relative;
  top: 10px;
}
.main-footer .widget-socials.widget-socials-style1 ul li:first-child {
  margin-left: 0;
}
.main-footer .widget-socials.widget-socials-style2 ul li {
  margin-bottom: 14px;
}
.main-footer .widget-socials.widget-socials-style2 ul li a {
  color: #666;
}
.main-footer .widget-socials.widget-socials-style2 ul li a i {
  float: left;
  display: inline-block;
  width: 39px;
  height: 39px;
  font-size: 1.15em;
  border: 1px solid #666;
  text-align: center;
  line-height: 2.3em;
  margin-right: 6px;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.main-footer .widget-socials.widget-socials-style2 ul li a p {
  float: left;
  overflow: hidden;
  line-height: 1em;
}
.main-footer .widget-socials.widget-socials-style2 ul li a p strong {
  font-weight: 500;
  display: block;
  margin-top: 6px;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.main-footer .widget-socials.widget-socials-style2 ul li a p span {
  font-size: 10px;
  display: block;
  margin-top: -1px;
  color: #444444;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.main-footer .widget-socials.widget-socials-style2 ul li a:hover i {
  color: #aaa;
  border-color: #a9a9a9;
}
.main-footer .widget-socials.widget-socials-style2 ul li a:hover strong {
  color: #aaa;
}
.main-footer .widget-socials.widget-socials-style2 ul li a:hover span {
  color: #666;
}
.main-footer .widget-socials.widget-socials-style3 ul li {
  margin-right: 9px;
  margin-bottom: 9px;
}
.main-footer .widget-socials.widget-socials-style3 ul li a {
  display: inline-block;
  width: 32px;
  height: 32px;
  text-align: center;
  border: 1px solid #666;
}
.main-footer .widget-socials.widget-socials-style3 ul li a i {
  font-size: 1.3em;
  line-height: 1.6em;
}
.main-footer .widget-socials.widget-socials-style3 ul li a:hover {
  border-color: #aaa;
}
.main-footer .widget-socials.widget-socials-style3.rounded ul li a {
  -webkit-border-radius: 50em;
  -moz-border-radius: 50em;
  border-radius: 50em;
}
.main-footer .widget-socials.widget-socials-style4 ul li {
  display: block;
  width: 100%;
  margin-bottom: 8px;
}
.main-footer .widget-socials.widget-socials-style4 ul li a {
  font-family: 'Montserrat', sans-serif;
  font-weight: 500;
  line-height: 3.6em;
  text-transform: uppercase;
  display: inline-block;
  width: 170px;
  height: 50px;
  border: 1px solid #666;
  color: #666;
}
.main-footer .widget-socials.widget-socials-style4 ul li a:hover {
  color: #222222;
  background: white;
  border-color: white;
}
.main-footer .widget-socials.widget-socials-style4 ul li:last-child {
  margin-bottom: 0;
}
.main-footer .widget-socials.widget-socials-style5 ul li {
  display: block;
  width: 100%;
  margin-bottom: 13px;
}
.main-footer .widget-socials.widget-socials-style5 ul li a {
  font-family: 'Montserrat', sans-serif;
}
.main-footer .widget-socials.widget-socials-style5 ul li a i {
  font-size: 1.35em;
  margin-right: 17px;
}
.main-footer .widget-latest-posts ul li article .post-thumb {
  float: left;
  display: inline-block;
}
.main-footer .widget-latest-posts ul li article .post-contents h1 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 400;
}
.main-footer .widget-latest-posts.widget-latest-posts-style1 ul li {
  border-bottom: 1px solid #333;
  margin-bottom: 23px;
  padding-bottom: 20px;
}
.main-footer .widget-latest-posts.widget-latest-posts-style1 ul li article .post-thumb {
  width: 41px;
  height: 41px;
  margin-right: 18px;
}
.main-footer .widget-latest-posts.widget-latest-posts-style1 ul li article .post-thumb a {
  display: inline-block;
  width: 100%;
  height: 100%;
  position: relative;
}
.main-footer .widget-latest-posts.widget-latest-posts-style1 ul li article .post-thumb a:after {
  content: 'î„ ';
  display: inline-block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  color: white;
  background: #bdbdbd;
  text-align: center;
  font-family: 'knight';
  font-size: 1.2em;
  line-height: 2.6em;
  font-weight: 300;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.main-footer .widget-latest-posts.widget-latest-posts-style1 ul li article .post-contents {
  overflow: hidden;
}
.main-footer .widget-latest-posts.widget-latest-posts-style1 ul li article .post-contents h1 {
  font-size: 16px;
  margin-bottom: 11px;
  text-transform: none;
}
.main-footer .widget-latest-posts.widget-latest-posts-style1 ul li article .post-contents h1 a {
  color: #aaa;
}
.main-footer .widget-latest-posts.widget-latest-posts-style1 ul li article .post-contents p {
  font-weight: 300;
  margin-bottom: 0;
}
.main-footer .widget-latest-posts.widget-latest-posts-style1 ul li article .post-contents time {
  color: #bdbdbd;
}
.main-footer .widget-latest-posts.widget-latest-posts-style1 ul li article:hover .post-thumb a:after {
  opacity: 1;
  visibility: visible;
}
.main-footer .widget-latest-posts.widget-latest-posts-style1 ul li:last-child {
  border-bottom: none;
}
.main-footer .widget-latest-posts.widget-latest-posts-style2 ul li {
  border-bottom: 1px solid #333;
  padding-bottom: 25px;
  margin-bottom: 17px;
}
.main-footer .widget-latest-posts.widget-latest-posts-style2 ul li article .post-contents h1 {
  font-size: 18px;
  text-transform: none;
  line-height: 1.8em;
  margin-bottom: 0;
}
.main-footer .widget-latest-posts.widget-latest-posts-style2 ul li article .post-contents h1 a {
  color: #aaa;
}
.main-footer .widget-latest-posts.widget-latest-posts-style2 ul li article .post-contents h1 a:hover {
  color: #ff4c4c;
}
.main-footer .widget-latest-posts.widget-latest-posts-style2 ul li:last-child {
  border-bottom: 0;
}
.main-footer .widget-latest-posts.widget-latest-posts-style3 ul li {
  margin-bottom: 31px;
}
.main-footer .widget-latest-posts.widget-latest-posts-style3 ul li article .post-thumb {
  width: 83px;
  height: 83px;
}
.main-footer .widget-latest-posts.widget-latest-posts-style3 ul li article .post-thumb a {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 100%;
}
.main-footer .widget-latest-posts.widget-latest-posts-style3 ul li article .post-thumb a:after {
  content: 'î„ ';
  font-family: 'knight';
  font-size: 1.75em;
  line-height: 3.4em;
  color: white;
  text-align: center;
  display: inline-block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(255, 79, 72, 0.8);
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.main-footer .widget-latest-posts.widget-latest-posts-style3 ul li article .post-thumb:hover a:after {
  opacity: 1;
  visibility: visible;
}
.main-footer .widget-latest-posts.widget-latest-posts-style3 ul li article .post-thumb:hover + .post-contents p {
  color: #aaa;
}
.main-footer .widget-latest-posts.widget-latest-posts-style3 ul li article .post-thumb:hover + .post-contents time {
  color: #ff4e47;
}
.main-footer .widget-latest-posts.widget-latest-posts-style3 ul li article .post-contents {
  margin-left: 96px;
}
.main-footer .widget-latest-posts.widget-latest-posts-style3 ul li article .post-contents h1 {
  font-size: 16px;
  text-transform: none;
  line-height: 1.2em;
  margin-bottom: 14px;
}
.main-footer .widget-latest-posts.widget-latest-posts-style3 ul li article .post-contents h1 a {
  color: #aaa;
}
.main-footer .widget-latest-posts.widget-latest-posts-style3 ul li article .post-contents p {
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.main-footer .widget-latest-posts.widget-latest-posts-style3 ul li article .post-contents p:first-child {
  margin-top: -3px;
}
.main-footer .widget-latest-posts.widget-latest-posts-style3 ul li article .post-contents p:last-of-type {
  margin-bottom: 0;
}
.main-footer .widget-latest-posts.widget-latest-posts-style3 ul li article .post-contents time {
  color: #bdbdbd;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.main-footer .widget-latest-posts.widget-latest-posts-style3 ul li:last-child {
  margin-bottom: 0;
}
.main-footer .widget-latest-posts.widget-latest-posts-style3 ul li.bordered-bottom {
  border-bottom: 1px solid #333;
  padding-bottom: 33px;
  margin-bottom: 37px;
}
.main-footer .widget-latest-posts.widget-latest-posts-style3 ul li.bordered-bottom:last-child {
  border-bottom: none;
}
.main-footer .widget-latest-posts.widget-latest-posts-style4 ul li {
  border-bottom: 1px solid #333;
  padding-bottom: 11px;
  padding-top: 14px;
}
.main-footer .widget-latest-posts.widget-latest-posts-style4 ul li article .post-contents h1 {
  font-size: 14px;
  text-transform: none;
  float: left;
  margin-bottom: 0;
}
.main-footer .widget-latest-posts.widget-latest-posts-style4 ul li article .post-contents h1 a {
  color: #aaa;
}
.main-footer .widget-latest-posts.widget-latest-posts-style4 ul li article .post-contents h1 a:hover {
  color: #ff4c4c;
}
.main-footer .widget-latest-posts.widget-latest-posts-style4 ul li article .post-contents time {
  float: right;
}
.main-footer .widget-latest-posts.widget-latest-posts-style4 ul li:first-child {
  padding-top: 0;
}
.main-footer .widget-latest-posts.widget-latest-posts-style4 ul li:last-child {
  border-bottom: none;
}
.main-footer .widget-latest-posts.widget-latest-posts-style5 ul li {
  border-bottom: 1px solid #333;
  padding-bottom: 11px;
  padding-top: 14px;
}
.main-footer .widget-latest-posts.widget-latest-posts-style5 ul li article .post-contents h1 {
  font-size: 14px;
  text-transform: none;
  float: left;
  margin-bottom: 0;
}
.main-footer .widget-latest-posts.widget-latest-posts-style5 ul li article .post-contents h1 a {
  color: #666;
}
.main-footer .widget-latest-posts.widget-latest-posts-style5 ul li article .post-contents h1 a:hover {
  color: #aaa;
}
.main-footer .widget-latest-posts.widget-latest-posts-style5 ul li:first-child {
  padding-top: 0;
}
.main-footer .widget-tags ul li {
  display: inline-block;
  margin-right: 4px;
  margin-bottom: 7px;
}
.main-footer .widget-tags ul li a {
  display: inline-block;
  padding: 10px 21px 9px 20px;
  border: 1px solid #333;
  color: #666;
}
.main-footer .widget-tags ul li a:hover {
  color: #aaa;
  border-color: #a9a9a9;
}
.main-footer .widget-links-list.widget-links-list-style1 ul li a {
  display: block;
  color: #666;
  border-bottom: 1px solid #333;
  padding-left: 26px;
  padding-top: 14px;
  padding-bottom: 14px;
  position: relative;
}
.main-footer .widget-links-list.widget-links-list-style1 ul li a:before {
  content: '';
  display: inline-block;
  width: 5px;
  height: 5px;
  position: absolute;
  top: 50%;
  left: 8px;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  background: #666;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.main-footer .widget-links-list.widget-links-list-style1 ul li a:hover {
  color: #aaa;
}
.main-footer .widget-links-list.widget-links-list-style1 ul li a:hover:before {
  background: #ff4c4c;
}
.main-footer .widget-links-list.widget-links-list-style1 ul li:first-child a {
  padding-top: 0;
}
.main-footer .widget-links-list.widget-links-list-style1 ul li:first-child a:before {
  top: 30%;
}
.main-footer .widget-links-list.widget-links-list-style1 ul li:last-child a {
  border-bottom: none;
}
.main-footer .widget-links-list.widget-links-list-style1.borderless ul li a {
  border-bottom: none;
  padding-bottom: 14px;
}
.main-footer .widget-links-list.widget-links-list-style2 {
  text-align: center;
}
.main-footer .widget-links-list.widget-links-list-style2 ul li {
  line-height: 1.5em;
  margin-bottom: 8px;
}
.main-footer .widget-links-list.widget-links-list-style2 ul li a {
  display: inline-block;
  padding: 6px 15px;
  border: 1px solid transparent;
  color: #666;
}
.main-footer .widget-links-list.widget-links-list-style2 ul li a:hover {
  color: #aaa;
  border-color: #a9a9a9;
}
.main-footer .widget-links-list.widget-links-list-style2:hover .widget-title.carrot-down:after {
  border-top-color: #ff4d4d;
}
.main-footer .widget-links-list.widget-links-list-style3 ul li {
  line-height: 1.5em;
  margin-bottom: 1.5em;
}
.main-footer .widget-links-list.widget-links-list-style3 ul li a {
  color: #666;
}
.main-footer .widget-links-list.widget-links-list-style3 ul li a:hover {
  color: #aaa;
}
.main-footer .widget-links-list.widget-links-list-style3 ul.carrot-onhover li a {
  position: relative;
}
.main-footer .widget-links-list.widget-links-list-style3 ul.carrot-onhover li a:before {
  content: '';
  display: inline-block;
  width: 0;
  height: 0;
  position: absolute;
  top: 50%;
  left: -17px;
  transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  border-top: 3px solid transparent;
  border-right: 3px solid transparent;
  border-bottom: 3px solid transparent;
  border-left: 6px solid transparent;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.main-footer .widget-links-list.widget-links-list-style3 ul.carrot-onhover li a:hover:before {
  border-left-color: #aaa;
}
.main-footer .widget-flickr-feed ul li {
  display: inline-block;
}
.main-footer .widget-flickr-feed ul li a {
  display: inline-block;
  width: 100%;
  height: 100%;
  position: relative;
}
.main-footer .widget-flickr-feed ul li a:after {
  content: '';
  display: inline-block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background: white;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.main-footer .widget-flickr-feed ul li a img {
  display: inline-block;
  width: 83px;
  height: 83px;
}
.main-footer .widget-flickr-feed ul li a:hover:after {
  opacity: 0.8;
  visibility: visible;
}
.main-footer .widget-flickr-feed ul.flickr-3columns li {
  margin-right: 10px;
  margin-bottom: 10px;
}
.main-footer .widget-flickr-feed ul.flickr-3columns li:nth-child(3n) {
  margin-right: 0;
}
.main-footer .widget-flickr-feed ul.flickr-4columns li {
  margin-right: 12px;
  margin-bottom: 11px;
}
.main-footer .widget-flickr-feed ul.flickr-4columns li:nth-child(4n) {
  margin-right: 0;
}
.main-footer .widget-flickr-feed .view-more-button {
  display: block;
  margin-top: 15px;
  color: #aaa;
  font-weight: 300;
}
.main-footer .widget-subscribe form {
  position: relative;
}
/* BizFunctional */
.main-footer .widget-subscribe form input[type=text] {
  color:#999999;
  background: none;
  border: 0px solid #333;
  padding-top: 18px;
  padding-bottom: 18px;
  padding-left: 22px;
  padding-right: 22px;
  width: 100%;
  line-height: 1em;
}
.main-footer .widget-subscribe form input[type=submit] {
  background: #ff4c4c;
  color: white;
  border: none;
}
.main-footer .widget-subscribe form .subscribe-form-result {
  display: none;
  margin-top: 11px;
}
.main-footer .widget-subscribe form ::-webkit-input-placeholder {
  font-weight: 300;
  color: #666;
}
.main-footer .widget-subscribe form :-moz-placeholder {
  font-weight: 300;
  color: #666;
}
.main-footer .widget-subscribe form ::-moz-placeholder {
  font-weight: 300;
  color: #666;
}
.main-footer .widget-subscribe form :-ms-input-placeholder {
  font-weight: 300;
  color: #666;
}
.main-footer .widget-subscribe.widget-subscribe-style1 p {
  color: #aaa;
}
.main-footer .widget-subscribe.widget-subscribe-style1 form input[type=text] {
  padding-right: 61px;
}
.main-footer .widget-subscribe.widget-subscribe-style1 form input[type=submit] {
  font-family: 'knight';
  font-size: 1.4em;
  position: absolute;
  top: 1px;
  right: 1px;
  display: inline-block;
  width: 49px;
  height: 49px;
}
.main-footer .widget-subscribe.widget-subscribe-style1 form .subscribe-form-result {
  display: none;
  margin-top: 11px;
}
.main-footer .widget-subscribe.widget-subscribe-style1 form ::-webkit-input-placeholder {
  font-style: italic;
}
.main-footer .widget-subscribe.widget-subscribe-style1 form :-moz-placeholder {
  font-style: ita;
}
.main-footer .widget-subscribe.widget-subscribe-style1 form ::-moz-placeholder {
  font-style: ital;
}
.main-footer .widget-subscribe.widget-subscribe-style1 form :-ms-input-placeholder {
  font-style: italic;
}
.main-footer .widget-subscribe.widget-subscribe-style2 input[type=text] {
  margin-bottom: 10px;
  position: relative;
}
.main-footer .widget-subscribe.widget-subscribe-style2 i {
  position: absolute;
  top: 18px;
  right: 20px;
  font-size: 1.1em;
}
.main-footer .widget-subscribe.widget-subscribe-style2 input[type=submit] {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  text-transform: uppercase;
  display: block;
  width: 100%;
  padding-top: 14px;
  padding-bottom: 12px;
}
.main-footer .widget-subscribe.widget-subscribe-style3 p span {
  display: block;
}
.main-footer .widget-subscribe.widget-subscribe-style3 input[type=text] {
  width: 75%;
  position: relative;
  margin-right: 1.5%;
  border-color: #ddd;
  color: #222222;
}
.main-footer .widget-subscribe.widget-subscribe-style3 input[type=submit] {
  font-family: 'Montserrat', sans-serif;
  font-weight: 700;
  text-transform: uppercase;
  width: 20%;
  padding-top: 16px;
  padding-bottom: 13px;
}
.main-footer .widget-latest-comments ul li {
  border-bottom: 1px solid #333;
  padding-bottom: 23px;
  margin-bottom: 21px;
}
.main-footer .widget-latest-comments ul li h6 {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  line-height: 1.8em;
  font-weight: 400;
  text-transform: none;
  margin-bottom: 0;
  color: #aaa;
}
.main-footer .widget-latest-comments ul li h6 a {
  color: #aaa;
}
.main-footer .widget-latest-comments ul li h6 a:hover {
  color: #ff4c4c;
}
.main-footer .widget-latest-comments ul li p {
  margin-bottom: 0;
}
.main-footer .widget-latest-comments ul li p a {
  color: #666;
}
.main-footer .widget-latest-comments ul li p a:hover {
  color: #aaa;
}
.main-footer .widget-latest-comments ul li:last-child {
  border-bottom: none;
}
.main-footer .widget-archives ul li {
  border-bottom: 1px solid #333;
  padding-bottom: 23px;
  margin-bottom: 21px;
}
.main-footer .widget-archives ul li h6 {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  line-height: 1.8em;
  font-weight: 400;
  text-transform: none;
  margin-bottom: 0;
  color: #aaa;
}
.main-footer .widget-archives ul li h6 a {
  color: #aaa;
}
.main-footer .widget-archives ul li h6 a:hover {
  color: #ff4c4c;
}
.main-footer .widget-archives ul li p {
  margin-bottom: 0;
}
.main-footer .widget-archives ul li p a {
  color: #666;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.main-footer .widget-archives ul li p a:hover {
  color: #aaa;
}
.main-footer .widget-archives ul li:last-child {
  border-bottom: none;
}
.main-footer .widget-twitter ul li {
  position: relative;
  border-bottom: 1px solid #333;
  padding-top: 12px;
  padding-bottom: 14px;
  padding-left: 48px;
}
.main-footer .widget-twitter ul li:before {
  content: '\f099';
  font-family: 'fontAwesome';
  font-size: 1.2em;
  line-height: 1.6em;
  text-align: center;
  display: inline-block;
  width: 26px;
  height: 26px;
  position: absolute;
  top: 19px;
  left: 3px;
  color: #222222;
  background: #999;
  -webkit-border-radius: 50em;
  -moz-border-radius: 50em;
  border-radius: 50em;
}
.main-footer .widget-twitter ul li p {
  margin-bottom: 0;
  color: #aaa;
}
.main-footer .widget-twitter ul li p a {
  color: #ff4d4d;
}
.main-footer .widget-twitter ul li p span {
  display: block;
  color: #444444;
}
.main-footer .widget-twitter ul li:first-child {
  padding-top: 0;
}
.main-footer .widget-twitter ul li:first-child:before {
  top: 6px;
}
.main-footer .widget-twitter ul li:last-child {
  border-bottom: none;
}
.main-footer .widget-logo.widget-logo-style1 {
  text-align: center;
}
.main-footer .widget-logo.widget-logo-style2 {
  text-align: center;
}
.main-footer .widget-logo.widget-logo-style2 figure {
  position: relative;
}
.main-footer .widget-promo {
  color: #aaa;
  margin-top: 55px;
}
/* BizFunctional */
.copyright-footer {
  color: #bbb;
  background-color: #252525;
  width:100%;
  margin-bottom:0px;
}
.main-footer .widget-text p span {
  display: block;
}
.main-footer .widget-text a {
  color: #ff4d4d;
}
.main-footer .widget-text .read-more-link {
  display: block;
}
.main-footer .widget-text.widget-text-style2 p {
  font-size: 14px;
  margin-bottom: 0;
  color: white;
}
.main-footer .widget-text.widget-text-style2 p span {
  line-height: 1.5em;
  margin-bottom: 2px;
}
.main-footer .widget-copyright {
  text-align: center;
}
.main-footer .widget-copyright .logo {
  display: block;
  margin-bottom: 28px;
}
.main-footer .widget-copyright p {
  color: #aaa;
  font-weight: 400;
  line-height: 1.8em;
}
.main-footer .widget-copyright p span {
  display: block;
}
.main-footer .widget-copyright.widget-copyright-style2 p {
  color: #eee;
}
.main-footer .widget-store-items ul li {
  border-bottom: 1px solid #333;
  padding-bottom: 11px;
  margin-bottom: 26px;
}
.main-footer .widget-store-items ul li article .item-thumb {
  float: left;
  width: 85px;
  height: 85px;
  margin-right: 31px;
}
.main-footer .widget-store-items ul li article .item-details h1 {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 400;
  text-transform: none;
  line-height: 1.2em;
  margin-top: 2px;
  margin-bottom: 13px;
}
.main-footer .widget-store-items ul li article .item-details h1 a {
  color: #aaa;
}
.main-footer .widget-store-items ul li article .item-details .item-rating {
  margin-bottom: 8px;
}
.main-footer .widget-store-items ul li article .item-details .item-rating span {
  margin-right: 4px;
}
.main-footer .widget-store-items ul li article .item-details .item-rating .filled-star i {
  color: #ff4c4c;
}
.main-footer .widget-store-items ul li article .item-details .item-rating .empy-star i {
  color: #aaa;
}
.main-footer .widget-store-items ul li article .item-details .item-price {
  font-weight: 500;
  color: #aaa;
  margin-bottom: 0;
}
.main-footer .widget-store-items ul li article .item-details .item-price del {
  font-weight: 400;
  color: #666;
  margin-right: 16px;
}
.main-footer .widget-store-items ul li:last-child {
  border-bottom: none;
}
.main-footer .widget-instagram-feed ul li {
  display: inline-block;
  width: 83px;
  height: 83px;
  margin-right: 10px;
  margin-bottom: 11px;
}
.main-footer .widget-instagram-feed ul.insta-3columns li:nth-child(3n) {
  margin-right: 0;
}
.main-footer .widget-instagram-feed ul.insta-4columns li:nth-child(4n) {
  margin-right: 0;
}
.main-footer .widget-instagram-feed .view-on-instagram-button {
  color: #aaa;
  font-weight: 300;
}
.main-footer .widget-instagram-feed .view-on-instagram-button i {
  color: #ff4c4c;
  margin-right: 12px;
}
.main-footer .right-bordered {
  position: relative;
  border-right: none;
}
.main-footer .right-bordered:after {
  content: '';
  display: inline-block;
  width: 1px;
  height: 100%;
  position: absolute;
  top: 0;
  right: 0;
  background: #333;
}
.main-footer .right-skewed-border {
  position: relative;
}
.main-footer .right-skewed-border:after {
  content: '';
  display: inline-block;
  width: 1px;
  height: 100%;
  position: absolute;
  top: 0;
  right: 15px;
  transform: skew(15deg);
  -ms-transform: skew(15deg);
  -webkit-transform: skew(15deg);
  background: #333;
}
.main-footer .footer-nav {
  float: right;
  padding-top: 13px;
}
.main-footer .footer-nav ul li {
  display: inline-block;
}
.main-footer .footer-nav ul li a {
  color: #666;
  -webkit-transition: 0.3s all 0.001s ease-out;
  -moz-transition: 0.3s all 0.001s ease-out;
  transition: 0.3s all 0.001s ease-out;
}
.main-footer .footer-nav ul li a:hover {
  color: #ff4c4c;
}
.main-footer .footer-nav ul li.active a {
  color: #ff4c4c;
}
.main-footer .footer-nav.style1 ul {
  margin-bottom: 0;
}
.main-footer .footer-nav.style1 ul li {
  margin-left: 30px;
}
.main-footer .footer-nav.style1 ul li a {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  text-transform: uppercase;
}
.main-footer .footer-nav.style2 ul li {
  margin-left: 28px;
}
.main-footer .footer-bottom-bar {
  border-top: 1px solid #333;
  padding-top: 28px;
  padding-bottom: 25px;
}
.main-footer .footer-bottom-bar p {
  margin-bottom: 0;
  line-height: 3.5em;
}
.main-footer .footer-bottom-bar .widget {
  float: right;
}
.main-footer .footer-bottom-bar .widget.widget-socials-style1 {
  margin-top: 4px;
}
.main-footer .footer-bottom-bar .copyright {
  float: left;
}
.main-footer .footer-bottom-bar .socials-container {
  float: right;
}
.main-footer .footer-bottom-bar .socials-container p {
  display: inline-block;
  float: left;
  margin-right: 12px;
}
.main-footer .footer-bottom-bar.align-center .copyright {
  float: none;
}
.main-footer .footer-bottom-bar.align-center .socials-container {
  float: none;
}
.main-footer .footer-bottom-bar.align-center .widget {
  float: none;
}
.main-footer .footer-bottom-bar.footer-bottom-bar-style2 .widget {
  float: none;
}
.main-footer .footer-bottom-bar.footer-bottom-bar-style2 .widget.widget-socials-style1 {
  margin-top: inherit;
}
.main-footer .footer-bottom-bar.footer-bottom-bar-style2 .widget.widget-socials-style3 ul li a {
  border-color: #454545;
}
.main-footer .footer-bottom-bar.footer-bottom-bar-style2 .widget.widget-socials-style3 ul li a:hover {
  color: white !important;
  background: #ff4d4d;
  border-color: #ff4d4d;
}
.main-footer .footer-bottom-bar.footer-bottom-bar-style2 .copyright {
  float: none;
}
.main-footer .footer-bottom-bar.footer-bottom-bar-style2 .socials-container {
  float: none;
}
.main-footer .footer-bottom-bar.footer-bottom-bar-style2 .socials-container p {
  display: inline-block;
  margin-right: inherit;
}
.main-footer .footer-bottom-bar.milky-white-bg {
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}
.main-footer .footer-bottom-bar.milky-white-bg a:hover {
  color: #ff4d4d;
}
.main-footer.white-bg,
.main-footer.milky-white-bg {
  border-top: 1px solid #ddd;
  border-bottom: none;
}
.main-footer.white-bg .right-bordered:after,
.main-footer.milky-white-bg .right-bordered:after,
.main-footer.white-bg .right-skewed-border:after,
.main-footer.milky-white-bg .right-skewed-border:after {
  background: #ddd;
}
.main-footer.white-bg .widget,
.main-footer.milky-white-bg .widget {
  border-color: #ddd;
}
.main-footer.white-bg .widget .widget-title,
.main-footer.milky-white-bg .widget .widget-title {
  color: #444444;
}
.main-footer.white-bg .widget .widget-title:after,
.main-footer.milky-white-bg .widget .widget-title:after {
  background: #ddd;
}
.main-footer.white-bg .widget .widget-title.carrot-down:after,
.main-footer.milky-white-bg .widget .widget-title.carrot-down:after {
  background: none;
}
.main-footer.white-bg .widget ul li,
.main-footer.milky-white-bg .widget ul li {
  border-color: #ddd;
}
.main-footer.white-bg .widget ul li.bordered-bottom,
.main-footer.milky-white-bg .widget ul li.bordered-bottom {
  border-color: #ddd;
}
.main-footer.white-bg .widget ul li article,
.main-footer.milky-white-bg .widget ul li article {
  border-color: #ddd;
}
.main-footer.white-bg .widget ul li article h1 a,
.main-footer.milky-white-bg .widget ul li article h1 a {
  color: #444444;
}
.main-footer.white-bg .widget ul li article h1 a:hover,
.main-footer.milky-white-bg .widget ul li article h1 a:hover {
  color: #ff4d4d;
}
.main-footer.white-bg .widget ul li a,
.main-footer.milky-white-bg .widget ul li a {
  border-color: #ddd;
  color: #444444;
}
.main-footer.white-bg .widget p,
.main-footer.milky-white-bg .widget p {
  border-color: #ddd;
}
.main-footer.white-bg .widget address p,
.main-footer.milky-white-bg .widget address p {
  border-color: #ddd;
}
.main-footer.white-bg .widget .button,
.main-footer.milky-white-bg .widget .button {
  color: #444444;
  border-color: #ccc;
}
.main-footer.white-bg .widget .button:hover,
.main-footer.milky-white-bg .widget .button:hover {
  background: #222222;
  color: white !important;
  border-color: white;
}
.main-footer.white-bg .widget-tags ul li a:hover,
.main-footer.milky-white-bg .widget-tags ul li a:hover {
  border-color: #ccc;
  color: #ff4d4d;
}
.main-footer.white-bg .widget-links-list ul li a:hover,
.main-footer.milky-white-bg .widget-links-list ul li a:hover {
  color: #ff4d4d;
}
.main-footer.white-bg .widget-socials ul,
.main-footer.milky-white-bg .widget-socials ul {
  margin-bottom: 0;
}
.main-footer.white-bg .widget-socials ul li a:hover,
.main-footer.milky-white-bg .widget-socials ul li a:hover {
  color: #ff4d4d;
  border-color: #ccc;
}
.main-footer.white-bg .widget-socials.widget-socials-style3.rounded ul li,
.main-footer.milky-white-bg .widget-socials.widget-socials-style3.rounded ul li {
  margin-right: 20px;
}
.main-footer.white-bg .widget-socials.widget-socials-style3.rounded ul li a,
.main-footer.milky-white-bg .widget-socials.widget-socials-style3.rounded ul li a {
  color: #ccc !important;
  border-color: #ccc;
}
.main-footer.white-bg .widget-socials.widget-socials-style3.rounded ul li a:hover,
.main-footer.milky-white-bg .widget-socials.widget-socials-style3.rounded ul li a:hover {
  color: #444444 !important;
  border-color: #444444;
}
.main-footer.white-bg .widget-contact-form form input,
.main-footer.milky-white-bg .widget-contact-form form input,
.main-footer.white-bg .widget-contact-form form textarea,
.main-footer.milky-white-bg .widget-contact-form form textarea {
  border-color: #ddd;
  color: #444444;
}
.main-footer.white-bg .widget-contact-form form input[type=submit],
.main-footer.milky-white-bg .widget-contact-form form input[type=submit] {
  border-color: transparent;
  color: white;
}
.main-footer.white-bg .widget-contact-form form input[type=submit].white-bg,
.main-footer.milky-white-bg .widget-contact-form form input[type=submit].white-bg {
  color: #444444;
}
.main-footer.white-bg .widget-subscribe form input,
.main-footer.milky-white-bg .widget-subscribe form input,
.main-footer.white-bg .widget-subscribe form textarea,
.main-footer.milky-white-bg .widget-subscribe form textarea {
  border-color: #ddd;
  color: #444444;
}
.main-footer.white-bg .widget-subscribe form input[type=submit],
.main-footer.milky-white-bg .widget-subscribe form input[type=submit] {
  border-color: transparent;
  color: white;
}
.main-footer.white-bg .widget-logo-style2 figure:before,
.main-footer.milky-white-bg .widget-logo-style2 figure:before,
.main-footer.white-bg .widget-logo-style2 figure:after,
.main-footer.milky-white-bg .widget-logo-style2 figure:after {
  background: #ddd;
}
.main-footer.white-bg .footer-bottom-bar,
.main-footer.milky-white-bg .footer-bottom-bar {
  border-color: #ddd;
}
/******************************** changes ******************************************/
.hidden-lg {
    display: none !important;
}
* {
    box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
}

*::before, *::after {
    box-sizing: border-box;
}
.main-nav [class^="icon-"], .main-nav [class*=" icon-"] {
    background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
    display: inline-block;
    height: auto;
    line-height: 14px;
    margin-top: 1px;
    vertical-align: text-top;
    width: auto;
}
/*.row-fluid .portfolio-item-container.span4.branding {
    margin-left: 0;
    margin-right: 0;
    width: 33.33%;
}*/
.row-fluid .portfolio-item-container.span4 {
    margin-left: 0 !important;
    padding-left: 15px;
    padding-right: 15px;
    width: 33.33%;
      box-sizing: border-box;
}
.home-mesonry .row-fluid .portfolio-item-container.span4.branding {
    margin-left: 0;
    margin-right: 0;
    padding-left: 60px;
    padding-right: 0px;
    width: 400px;
}
.portfolio-item-wrapper.portfolio-item-wrapper-style2.row-fluid {
    margin-left: 0px;
    width: 100%;
}
.home-mesonry .portfolio-item-wrapper.portfolio-item-wrapper-style2.row-fluid {
    margin-left: -45px;
    width: auto;
}
.portfolio-container.portfolio-masonry.columns3 {
    /*margin-left: -45px;
    width: auto;*/
}
.join-discussion {
    padding-left: 200px;
    padding-right: 200px;
    width: 1170px;
  max-width:100%;
}
.our-story {
    width: 1170px;
  max-width:100%;
}
.width-full{
   width: 1170px;
  max-width:100%;
}
.portfolio-container.portfolio-masonry.columns3.latest-portfolio {
    margin-left: 0;
}
.latest-portfolio .portfolio-item-container.span4 {
    margin-left: 0;
    width: 33.33%;
  padding-left:15px;
  padding-right:15px;
}
.margin-testimonial {
    max-width: 100%;
    padding: 0 208px;
    width: 1170px;
}
.donate-box.span12 {
    margin-left: 5px;
    width: 1200px;
    max-width: 100%;
}
/*.donate-today {
    float: right;
    margin-right: 37px;
}*/
.main-footer .widget-subscribe.widget-subscribe-style1 form input[type="text"] {
    height: auto;
    margin-bottom: 0;
    padding-right: 61px;
  font-size: 15px;
}
.main-bar .container-fluid {
    padding-left: 0;
    padding-right: 0;
}
/*.main-header.style2 .main-bar.style2 {
  padding-top: 0px;
  padding-bottom: 0px;
}*/
.main-nav .nav > li > a:hover, .main-nav .nav > li > a:focus {
    background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
}
.main-footer.dark-bg .span12 {
    margin-left: 0;
}
/*.logo-container {
    padding-left: 20px;
}*/
.row-fluid .event-section-box.offset1:first-child {
    margin-left: 7.25%;
    width: 85.6%;
}
.row-fluid .event-section-box .span5 {
    width: 42.6667%;
}
.row-fluid .category-section-box.offset2:first-child {
    margin-left: 15.8%;
}
.main-contents .row-fluid .category-section-box {
    width: 68.5%;
}
.span9.pull-left.prod-section2 {
    margin-left: 0px;
}
.prod-section2 .product.span4 {
    width: 31.1%;
}
.prod-section2 .row-fluid [class*="span"] {
    margin-left: 3.35%;
}
input, button, select, textarea {
    font-family: "Montserrat",sans-serif;
}
.portfolio-text {
    background: #c93827 none repeat scroll 0 0;
    color: #ffffff;
    min-height: 200px;
    padding: 15px 15px 50px 15px;
    text-align: center;
}
.portfolio-text h2 {
    font-size: 28px;
    font-weight: bold;
    letter-spacing: 1px;
    line-height: 30px;
    margin: 0 0 13px;
    text-align: center;
}
.portfolio-text p {
    font-size: 15px;
    font-weight: 300;
    letter-spacing: 0.5px;
    line-height: 16px;
    margin: 0 0 17px;
    padding-left: 15px;
    text-align: left;
    word-spacing: 0.5px;
}
.portfolio-text.we_do {
    background: #a62b1c none repeat scroll 0 0;
    padding-left: 23px;
    padding-top: 14px;
}
.we_are_btn {
    border: 3px solid #ffffff;
    color: #ffffff;
    display: inline-block;
    font-size: 17px;
    padding: 14px 20px;
    text-transform: uppercase;
    width: 212px;
}
.we_are_btn:hover {
    background: #444444 none repeat scroll 0 0 ;
    color: #ffffff ;
}
.portfolio-text.we_do > p {
    padding: 0;
}
.portfolio-text.how_join {
    background: #862012 none repeat scroll 0 0;
}
.header-fullhome , .title-barBF{
    position: relative;
  overflow:hidden;
}
.container.banner-caption {
    left: 0;
    margin: 0 auto;
   /* position: absolute;*/
    right: 0;
    top: 0;
}
/*
.banner-img img {
    display: block;
    max-width: none;
    width: auto;
    min-width: 100%;

}*/
.main-header .main-bar .nav {
float:left;
}
#main-nav .serach-icon1 {
    float: right;
}
.innerbanner-img > img {
    left: 50%;
    margin: 0 auto;
    max-width: none;
    position: absolute;
    right: 0;
    min-width: 100%;
    width:auto;
    transform: translate(-50%, 0px);
  -moz-transform: translate(-50%, 0px);
  -webkit-transform: translate(-50%, 0px);
  -ms-transform: translate(-50%, 0px);
}

.clearfix:before,
.clearfix:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.clearfix:after {
  clear: both;
}

/*************************************************************
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
..................... 11. MEDIA QUERIES ......................
::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
*************************************************************/

@media screen and (max-width: 1450px) {
	.logo-container {
		padding-left:15px !important;
	}
}

@media screen and (max-width: 1399px) {
	.logo-container {
		width: 350px;
		padding-top:25px !important;
		padding-left:15px !important;
	}
}
@media screen and (max-width: 1199px) {
	.logo-container {
		width: 290px;
		padding-top:30px !important;
		padding-left:15px !important;
	}
	.main-nav ul li{margin-left: 14px;}
}

@media screen and (max-width: 991px) {
	.mobile-header-wrapper.style2 .logo-container{width: 260px;}
}

@media screen and (max-width: 767px) {
	.mobile-header-wrapper.style2 .logo-container{width: 200px;}
}

@media screen and (max-width: 570px) {
	.mobile-header-wrapper.style2 .logo-container {
		margin: 0 0 0 120px;
		text-align: left;
	}
}

@media screen and (max-width: 520px) {
	.mobile-header-wrapper.style2 .logo-container {
		margin: 0 0 0 80px;
	}
}

@media screen and (max-width: 390px) {
	.mobile-header-wrapper.style2 .logo-container{margin:0 0 0 43px; width: 170px;}
}

@media screen and (max-width: 480px) {

  .portfolio-container.portfolio-masonry .portfolio-item-container .portfolio-item .portfolio-item-details {
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 100%;
    margin-left: 0;
    margin-right: 0;
  }
  .main-footer .widget-contact-info.widget-contact-info-style1 address p i {
    width: 5% !important;
  }
  .main-footer .widget-contact-info.widget-contact-info-style1 address p span {
    width: 90% !important;
  }
  .audio-container.audio-container-style2 .contents .song-details {
    width: 70% !important;
  }
  .title-bar h1 {
    font-size: 46px;
  }
  .title-bar h6 {
    font-size: 22px;
  }
  .icon-container {
    padding-top: 22px;
    top: -40px;
  }
  .mobile-header-wrapper.style2 .logo-container {
    padding-right:20px;
}
    .mobile-header-wrapper.style2 .shifter-handle {
        left: 20px;
    }

    .mobile-header-wrapper.style2 .search-form-trigger {
        right: 97px;
    }

    .mobile-header-wrapper.style2 .cart-link {
        right: 67px;
    }

    .mobile-header-wrapper.style2 .contact-link {
        right: 37px;
    }
    .mobile-header-wrapper.style2 .login-link {

      right: 7px;
      top:77%;

    }
}
@media screen and (max-width: 767px) {
.event-section-body-right {
  border:none !important;
}

  .mobile-header-wrapper.style1 .logo-container {
    max-width: 200px;
  }
  .mobile-header-wrapper.style1 .shifter-handle {
    margin-top: 17px;
  }
  .error404-container .error-icon,
  .error404-container .error-message {
    float: none;
    display: block;
  }
  .error404-container .error-icon {
    margin-bottom: 40px;
  }
  .error404-container .error-message {
    margin-left: 0;
  }
  .preloader-container .preloader-screen .preloader-bar {
    width: 300px;
  }
  .main-subscribe-form.main-subscribe-form-style2 input[type=submit],
  .main-subscribe-form.main-subscribe-form-style2 #subscribe-email {
    width: 100%;
  }
  .main-subscribe-form.main-subscribe-form-style2 #subscribe-email {
    margin-bottom: 10px;
  }
  .product-filter-controls .pull-left,
  .product-filter-controls .pull-right {
    float: none !important;
  }
  .product-filter-controls select {
    margin-bottom: 33px;
  }
  .single-product-image-container .thumbnails {
    display: none;
  }
  .single-product-image-container .slider{
    width: 100% !important;
  }
  .sidebar-left .single-product-image-container.thumbnails-left .slider .slides li .product-preview,
  .sidebar-right .single-product-image-container.thumbnails-left .slider .slides li .product-preview,
  .sidebar-left .single-product-image-container.thumbnails-right .slider .slides li .product-preview,
  .sidebar-right .single-product-image-container.thumbnails-right .slider .slides li .product-preview {
    height: auto !important;
  }
  .main-subscribe-form-style3 #subscribe-email,
  .main-subscribe-form-style3 input[type=submit] {
    width: 100%;
  }
  .main-subscribe-form-style3 #subscribe-email {
    margin-bottom: 10px;
  }

  .portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .portfolio-item-details {
    padding: 30px 25px 22px;
  }
  .portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .overlay {
    text-align: left;
    margin-top: 20px;
  }
  .portfolio-container .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .overlay a {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translateX(0);
    -ms-transform:     translateX(0);
    transform:         translateX(0);
  }
  .main-footer .footer-bottom-bar .copyright,
  .main-footer .footer-bottom-bar .widget.widget-socials {
    float: none;
    width: 100%;
    text-align: center;
  }
  .main-footer .footer-bottom-bar .widget.widget-socials ul li {
    float: none;
    display: inline-block;
  }
  .main-footer .right-skewed-border:after {
    content: none;
  }
  .main-footer .widget-logo.widget-logo-style2 figure:before,
  .main-footer .widget-logo.widget-logo-style2 figure:after {
    content: none;
  }
  .main-footer .widget-subscribe.widget-subscribe-style3 input[type=text] {
    width: 100%;
    margin-bottom: 10px;
  }
  .main-footer.white-bg .widget-subscribe form input[type=submit], .main-footer.milky-white-bg .widget-subscribe form input[type=submit] {
    width: 100%;
  }
  .post-container.blog-post-single .post .entry-comments-container .entry-comments > ol .comment .children .comment {
    padding-left: 15px;
  }
  .post-container.blog-post-single .post .entry-footer .entry-author .author-avatar {
    width: 100%;
    margin-right: 0;
    margin-bottom: 30px;
  }
  .post-container.blog-post-single .post .entry-footer .entry-author .author-bio {
    overflow: visible;
  }
  .audio-container.audio-container-style2 .contents {
    position: relative;
    bottom: auto;
    left: auto;
    padding: 60px 50px 40px 70px;
    background: rgba(0, 0, 0, 1);
    width: 100%;
  }
  .audio-container.audio-container-style2 .contents .song-details {
    width: 80%;
  }
  html body {
    padding-left: 0;
    padding-right: 0;
}
.span10.offset1.white-bg.event-section-box.align-center {
    position: relative;
}
.row-fluid .event-section-box .span5 {
    width: 100%;
}
.event-section-box .event-section-body-right-row {
    display: inline-block;
    vertical-align: top;
    width: 100%;
}
.cat1 {
    padding-left: 15px;
    padding-right: 15px;
}
.cat1 .span8.offset2 {
    margin-left: 0 !important;
    position: relative;
    width: 100%;
}
.caaa-onlinestore {
    padding-left: 15px;
    padding-right: 15px;
}
.prod-section2 .product.span4 {
    margin-left: 0 !important;
    width: 100%;
}
}
@media (max-height: 800px) {

  body.fixed-nav .fixed-menu .menu-container {
    margin-bottom: 100px;
  }
  body.fixed-nav .fixed-menu .bottom-sec {
    position: relative;
    bottom: auto;
    padding-bottom: 30px;
  }
}
@media screen and (max-width: 991px) {
/* BizFunctional */
.header-large{
    display:none;
}
.header-small{
    display:normal;
}


  table {
    overflow: hidden;
  }
  table tbody,
  table td,
  table tr {
    position: relative;
  }
  table tbody {
    border: 1px solid #ccc;
  }
  table td:before {
    content: attr(data-th);
    display: inline-block;
    width: 35%;
    position: absolute;
    top: 17px;
    left: 30px;
  }
  .title-bar.title-bar-style2 h6 {
    margin-top: 160px;
  }
  .sticky-menu {
    display: none;
  }
  .shopping-cart .shopping-cart-table {
    margin-bottom: 75px;
  }
  .shopping-cart .shopping-cart-table tbody td {
    padding: 30px 20px 35px;
  }
  .shopping-cart .shopping-cart-table tbody td:before {
    font-family: 'Montserrat', sans-serif;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    top: 30px;
    left: 20px;
  }
  .shopping-cart .shopping-cart-table tbody .product-image img {
    width: 100%;
  }
  .subscribe-form-container-style2,
  .subscribe-form-container-style2 .main-subscribe-form,
  .subscribe-form-container-style3,
  .subscribe-form-container-style3 .main-subscribe-form {
    text-align: center;
  }
  .subscribe-form-container-style2 header .icon-container,
  .subscribe-form-container-style3 header .icon-container {
    float: none;
    margin-bottom: 40px;
    margin-right: 0;
  }
  .subscribe-form-container-style2 header .icon-container i {
    float: none;
  }
  .subscribe-form-container-style2 .main-subscribe-form,
  .subscribe-form-container-style3 .main-subscribe-form {
    margin-top: 30px;
  }
  .single-product-image-container .thumbnails {
    margin-bottom: 43px;
  }
  .product .product-options form select {
    margin-bottom: 10px;
  }
  .product.single-product .product-options form .input-container {
    margin-bottom: 30px;
  }
  .product-quickview-container {
    padding-top: 40px !important;
  }
  .product.single-product .product-details-container .product-review {
    float: none;
  }
  .product.single-product .product-details-container .product-info {
    margin-bottom: 15px;
  }
  .product .product-details .product-price {
    padding-left: 15px;
  }
  .w1740 .product .product-details .product-price {
    padding-left: 30px;
  }
  .product .product-details .product-info {
    margin-bottom: 15px;
  }
  .product.single-product .product-details-container .product-price {
    padding-left: 0 !important;
  }
  .single-product-image-container.thumbnails-left .slider .slides li .product-preview,
  .single-product-image-container.thumbnails-right .slider .slides li .product-preview {
    margin-bottom: 35px;
  }
  .single-product-image-container .thumbnails .slides li {
    width: 125px !important;
  }
  .sidebar-left .product.single-product .product-details-container .product-info {
    margin-bottom: 15px;
  }
  .product.single-product .product-details-container .product-review-container {
    margin-bottom: 35px;
  }
  .product.single-product .product-details-container .product-review .product-rating {
    margin-top: 0;
    display: inline-block;
  }
  .product.single-product .product-details-container .product-review .review-counter {
    margin-bottom: 0;
  }
  .sidebar-left .single-product-image-container.thumbnails-left,
  .sidebar-right .single-product-image-container.thumbnails-left,
  .sidebar-left .single-product-image-container.thumbnails-right,
  .sidebar-right .single-product-image-container.thumbnails-right {
    margin-bottom: 35px;
  }
  .sidebar-left .single-product-image-container.thumbnails-left .slider,
  .sidebar-right .single-product-image-container.thumbnails-left .slider,
  .sidebar-left .single-product-image-container.thumbnails-right .slider,
  .sidebar-right .single-product-image-container.thumbnails-right .slider {
    width: 80%;
  }
  .sidebar-left .single-product-image-container.thumbnails-left .thumbnails,
  .sidebar-right .single-product-image-container.thumbnails-left .thumbnails,
  .sidebar-left .single-product-image-container.thumbnails-right .thumbnails,
  .sidebar-right .single-product-image-container.thumbnails-right .thumbnails {
    width: 125px;
  }
  .sidebar-left .single-product-image-container.thumbnails-left .slider .slides li .product-preview,
  .sidebar-right .single-product-image-container.thumbnails-left .slider .slides li .product-preview,
  .sidebar-left .single-product-image-container.thumbnails-right .slider .slides li .product-preview,
  .sidebar-right .single-product-image-container.thumbnails-right .slider .slides li .product-preview {
    height: 548px;
  }
  .sidebar-left .single-product-image-container.thumbnails-left .slider,
  .sidebar-right .single-product-image-container.thumbnails-left .slider,
  .sidebar-left .single-product-image-container.thumbnails-left .slider .flex-viewport,
  .sidebar-right .single-product-image-container.thumbnails-left .slider .flex-viewport {
    margin-right: 0;
    /*float: none;*/
  }
  .sidebar-left .single-product-image-container.thumbnails-left + .product-details-container,
  .sidebar-right .single-product-image-container.thumbnails-left + .product-details-container,
  .sidebar-left .single-product-image-container.thumbnails-right + .product-details-container,
  .sidebar-right .single-product-image-container.thumbnails-right + .product-details-container {
    padding-left: 15px;
  }
  .sidebar-left .single-product-image-container.thumbnails-left .slider .slides li .product-preview img,
  .sidebar-right .single-product-image-container.thumbnails-left .slider .slides li .product-preview img,
  .sidebar-left .single-product-image-container.thumbnails-right .slider .slides li .product-preview img,
  .sidebar-right .single-product-image-container.thumbnails-right .slider .slides li .product-preview img {
    width: 100%;
  }
  .sidebar-left .single-product-image-container.thumbnails-left .thumbnails .flex-direction-nav li,
  .sidebar-right .single-product-image-container.thumbnails-left .thumbnails .flex-direction-nav li,
  .sidebar-left .single-product-image-container.thumbnails-right .thumbnails .flex-direction-nav li,
  .sidebar-right .single-product-image-container.thumbnails-right .thumbnails .flex-direction-nav li {
    left: 50%;
  }
  .latest-postbox .post-content h1,
  .main-footer .widget-latest-posts.widget-latest-posts-style1 ul li article .post-contents h1,
  .main-footer .widget-latest-posts.widget-latest-posts-style2 ul li article .post-contents h1,
  .main-footer .widget-latest-posts.widget-latest-posts-style3 ul li article .post-contents h1,
  .main-footer .widget-latest-posts.widget-latest-posts-style4 ul li article .post-contents h1,
  .main-footer .widget-latest-posts.widget-latest-posts-style5 ul li article .post-contents h1,
  .main-footer .widget-store-items ul li article .item-details h1 {
    font-size: 14px !important;
  }
  .sm-borderless {
    border: none !important;
  }
  .portfolio-fullwidth.columns3 .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .overlay {
    text-align: left;
  }
  .portfolio-fullwidth.columns3 .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .overlay a {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translateX(0);
    -ms-transform:     translateX(0);
    transform:         translateX(0);
  }
  .portfolio-container .portfolio-item-wrapper .portfolio-item-container {
    width: 100%;
  }
  .portfolio-container .portfolio-filter-container ul {
    float: none !important;
  }
  .portfolio-project-details .project-info .related-project-container {
    display: none;
  }
  .portfolio-project-details.info-right .project-info {
    float: none;
  }
  .portfolio-container.portfolio-timeline .portfolio-item-wrapper:before,
  .portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.left-positioned:before,
  .portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container.left-positioned:after {
    content: none;
  }
  .main-footer .footer-bottom-bar .socials-container > p {
    display: none;
  }
  .main-footer .widget-contact-info.widget-contact-info-style1 address p i {
    width: 3%;
  }
  .main-footer .widget-contact-info.widget-contact-info-style1 address p span {
    width: 93%;
  }
  .main-footer .widget-contact-info.widget-contact-info-style1 address p span {
    width: 93%;
  }
  .main-footer .widget .widget-title {
    margin-bottom: 35px !important;
  }
  .main-footer .widget-flickr-feed ul.flickr-3columns li {
    width: 81px;
    height: auto;
  }
  .main-footer .widget-flickr-feed ul li a img {
    width: 100%;
    height: auto;
  }
  .main-footer .widget-flickr-feed ul.flickr-4columns li:nth-child(4n),
  .main-footer .widget-flickr-feed ul.flickr-3columns li:nth-child(3n) {
    margin-right: 10px;
  }
  .main-footer .widget-flickr-feed .view-more-button {
    float: none !important;
    margin-bottom: 35px;
  }
.main-footer .widget-subscribe.widget-subscribe-style1, .main-footer .widget-subscribe.widget-subscribe-style2 {
    margin-bottom: 60px;
    margin-left: auto;
    margin-right: auto;
    max-width: 400px !important;
}
  .main-footer .widget-subscribe.widget-subscribe-style1 form input[type=text] {
    margin-bottom: 10px;
    max-width:400px !important;
  }
  .main-footer .widget-subscribe.widget-subscribe-style1 form input[type=submit] {
    position: relative;
    top: auto;
    right: auto;
    display: block;
    width: 100%;
    max-width:400px !important;
  }
  .main-footer .footer-bottom-bar .footer-nav,
  .main-footer .footer-bottom-bar .socials-container,
  .main-footer .footer-bottom-bar .widget-socials,
  .main-footer .footer-bottom-bar .copyright {
    float: none;
    text-align: center;
    display: block;
  }
  .main-footer .footer-bottom-bar * + [class^=col-] .widget-socials {
    text-align: left;
  }
  .main-footer .footer-bottom-bar .widget + .copyright,
  .main-footer .footer-bottom-bar .footer-nav + .copyright,
  .main-footer .footer-bottom-bar .socials-container + .copyright {
    margin-top: 15px;
  }
  .main-footer .footer-bottom-bar .widget-socials ul li {
    float: none;
    display: inline-block;
  }
  .main-footer .right-bordered {
    border: none;
  }
  .main-footer .right-bordered:after {
    content: none;
  }
  .main-footer .widget-instagram-feed ul.insta-3columns li:nth-child(3n) {
    margin-right: 10px;
  }
  .main-footer .widget-instagram-feed ul.insta-3columns li {
    width: 81px;
    height: 81px;
  }
  .post-container.medium-post-thumb .post .entry-image,
  .post-container.medium-post-thumb .mejs-container.mejs-video {
    margin-bottom: 60px !important;
  }
  .post-container.blog-timeline .article-container.left-positioned:before,
  .post-container.blog-timeline .article-container.left-positioned:after,
  .post-container.blog-timeline .article-container.right-positioned:before,
  .post-container.blog-timeline .article-container.right-positioned:after {
    content: none;
  }
  .post-container.blog-timeline:before {
    content: none;
  }
  .main-sidebar .widget.widget-instagram ul li {
    width: 10%;
    height: auto;
    margin-right: 7px;
    margin-bottom: 7px;
  }
  .main-sidebar .widget.widget-subscribe .subscribe-form .subscribe-email {
    margin-bottom: 15px;
  }
  .main-sidebar .widget.widget-subscribe .subscribe-form input[type=submit] {
    width: 100%;
  }
  .sidebar-left .post-container,
  .sidebar-right .post-container {
    float: none !important;
  }
  body.fixed-nav{
    padding-left: 0 !important;
  }
  .main-header.overlay-header .main-header-inner,
  .main-header.fixed-header .main-header-inner {
    display: none;
  }
  .main-nav.offcanvas-menu:not(.mobile-nav),
  .fixed-menu,
  .main-header .breadcrumbs-container {
    display: none;
  }
  .main-nav.offcanvas-menu {
    width: 270px;
  }
  .main-nav.offcanvas-menu ul {
    padding: 73px 27px;
  }
  .main-nav.mobile-nav .product,
  .main-nav.mobile-nav .blog-post,
  .main-nav.mobile-nav .socials,
  .main-nav.mobile-nav .shifter-handle {
    display: none !important;
  }
  .shifter-open .shifter-page,
  .shifter-open .shifter-header {
    -webkit-transform: translate3D(-270px, 0, 0);
        -ms-transform: translate3D(-270px, 0, 0);
            transform: translate3D(-270px, 0, 0);
  }
  .shifter-enabled.shifter-left.shifter-open .shifter-page,
  .shifter-enabled.shifter-left.shifter-open .shifter-header {
    -webkit-transform: translate3D(270px, 0, 0);
        -ms-transform: translate3D(270px, 0, 0);
            transform: translate3D(270px, 0, 0);
  }
  .no-csstransforms3d .shifter-enabled.shifter-left .shifter-navigation {
    left: -270px;
  }
  .no-csstransforms3d .shifter-enabled.shifter-open .shifter-page {
    left: -270px;
  }
  .shifter-open.mobile-header-style2 .shifter-page,
  .shifter-open.mobile-header-style2 .shifter-header {
    -webkit-transform: translate3D(0, 0, 0);
        -ms-transform: translate3D(0, 0, 0);
            transform: translate3D(0, 0, 0);
  }
  .shifter-enabled.shifter-left.shifter-open.mobile-header-style2 .shifter-page,
  .shifter-enabled.shifter-left.shifter-open.mobile-header-style2 .shifter-header {
    -webkit-transform: translate3D(0, 0, 0);
        -ms-transform: translate3D(0, 0, 0);
            transform: translate3D(0, 0, 0);
  }
  .no-csstransforms3d .shifter-enabled.shifter-left.mobile-header-style2 .shifter-navigation {
    left: 0;
  }
  .no-csstransforms3d .shifter-enabled.shifter-open.mobile-header-style2 .shifter-page {
    left: 0;
  }
  .w1740 .product .single-product-image-container .thumbnails .slides li {
    width: 120px !important;
    margin-bottom: 24px !important;
  }
  .hidden-lg {
    display: block !important;
}
.portfolio-container.portfolio-masonry.columns3 {
    margin-left: 0;
    width: 100%;
}
.portfolio-item-container.span4.branding {
    margin-left: 0;
    padding-left: 15px;
    padding-right: 15px;
}
.margin-testimonial {
    padding: 0 10px;
}
.donate-box .row-fluid .span7 {
    width: 100%;
}
.donate-box .row-fluid .donate-today {
    width: 100%;
}
.main-footer .container .span12 {
    padding-left: 15px;
    padding-right: 15px;
}
.join-discussion {
    padding-left: 15px;
    padding-right: 15px;
}
.header-small .span7 {
    width: 75% !important;
    padding-left: 15px;
    padding-right: 15px;
}
.row-fluid .portfolio-item-container.span4.branding {
    margin-left: 0;
    width: 100%;
}
.main-nav.offcanvas-menu .search-form input[type="search"] {
    background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
    border-radius: 0;
  -moz-border-radius: 0;
  -webkit-border-radius: 0;
  -ms-border-radius: 0;
    box-shadow: none;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  -ms-box-shadow: none;
    box-sizing: border-box;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -ms-box-sizing: border-box;
}
.evnt-detail .span8.offset2 {
    margin-left: 0;
}
.evnt-detail {
    padding-left: 15px;
    padding-right: 15px;
}
.abt-detail {
    padding-left: 15px;
    padding-right: 15px;
}
.home-mesonry .row-fluid .portfolio-item-container.span4.branding {
    padding-left: 0;
    width: 100%;
}
.home-mesonry .portfolio-item-wrapper.portfolio-item-wrapper-style2.row-fluid {
    margin-left: 0;
}
.portfolio-container.portfolio-masonry.columns3.home-mesonry {
    padding-left: 30px;
    padding-right: 30px;
}
.span12.work-california {
    padding-left: 15px;
    padding-right: 15px;
}
.span3.pull-right.prod-category-section{
    display: inline-block;
    float: none;
    padding-left: 15px;
    padding-right: 15px;
    width: 100%;
}
.row-fluid .span9.prod-section2 {
    width: 100%;
  padding-left:15px;
  padding-right:15px;
}
.banner-img img {
    display: block;
    max-width: none;
    min-width: 100%;
    width: 100%;
}
body .header-fullhome {
    height: auto;
}
}
@media screen and (min-width: 992px){
/* BizFunctional */
.header-large{
    display:normal;
}
.header-small{
    display:none;
}
  .main-wrapper{
    padding-top: 0 !important;
  }
  .main-nav ul li.woocommerce-menu .mobile-woocommerce-dropdown{
    display: none !important;
  }
  .main-nav.mobile-nav{
    display: none;
  }
  .mobile-woocommerce-menu {
    display: none !important;
  }
}

@media screen and (min-width: 992px) and (max-width: 1199px) {

  .main-nav.extra-padding ul li{
    margin-left: 20px;
  }
  .main-header.style4 .main-bar .options li{
    margin-right: 10px;
  }
  .main-nav ul li.mega-menu.multi-column .sub-menu .menu-column ul.arrow-list li:not(.column-title) a,
  .main-nav ul li.mega-menu.full-width .sub-menu .menu-column ul.arrow-list li:not(.column-title) a{
    padding-left: 25px;
  }
  .main-nav ul li.mega-menu.multi-column .sub-menu .menu-column ul.arrow-list li:not(.column-title) a:before,
  .main-nav ul li.mega-menu.full-width .sub-menu .menu-column ul.arrow-list li:not(.column-title) a:before{
    left: 0;
  }
  .main-subscribe-form.main-subscribe-form-style2 #subscribe-email {
    width: 300px;
  }
  .w1740 .main-subscribe-form.main-subscribe-form-style2 #subscribe-email {
    width: 295px;
  }
  .product .product-options form select {
    margin-bottom: 10px;
  }
  .product.single-product .product-options form .input-container {
    margin-bottom: 30px;
  }
  .w1740 .product.style1 .product-preview .overlay,
  .sidebar-right .product.style1 .product-preview .overlay,
  .sidebar-left .product.style1 .product-preview .overlay {
    display: none;
  }
  .single-product-image-container.thumbnails-left .slider,
  .single-product-image-container.thumbnails-right .slider {
    width: 76%;
  }
  .single-product-image-container .thumbnails .slides li {
    width: 90px !important;
  }
  .sidebar-right .product.single-product .product-details-container .product-info,
  .sidebar-left .product.single-product .product-details-container .product-info {
    margin-bottom: 15px;
  }
  .sidebar-right .product.single-product .product-details-container .product-review-container,
  .sidebar-left .product.single-product .product-details-container .product-review-container {
    width: 100% !important;
    margin-bottom: 35px;
  }
  .sidebar-right .product.single-product .product-details-container .product-review,
  .sidebar-left .product.single-product .product-details-container .product-review {
    float: left;
  }
  .sidebar-right .product.single-product .product-details-container .product-review .product-rating,
  .sidebar-left .product.single-product .product-details-container .product-review .product-rating {
    margin-top: 0;
    display: inline-block;
  }
  .sidebar-right .product.single-product .product-details-container .product-review .review-counter,
  .sidebar-left .product.single-product .product-details-container .product-review .review-counter {
    margin-bottom: 0;
  }
  .sidebar-left .single-product-image-container.thumbnails-left .thumbnails .slides li,
  .sidebar-right .single-product-image-container.thumbnails-left .thumbnails .slides li,
  .sidebar-left .single-product-image-container.thumbnails-right .thumbnails .slides li,
  .sidebar-right .single-product-image-container.thumbnails-right .thumbnails .slides li {
    width: 78px !important;
  }
  .sidebar-left .single-product-image-container.thumbnails-left .thumbnails .flex-direction-nav li,
  .sidebar-right .single-product-image-container.thumbnails-left .thumbnails .flex-direction-nav li,
  .sidebar-left .single-product-image-container.thumbnails-right .thumbnails .flex-direction-nav li,
  .sidebar-right .single-product-image-container.thumbnails-right .thumbnails .flex-direction-nav li {
    left: 51%;
  }
  .main-subscribe-form-style3 #subscribe-email {
    width: 400px;
  }
  .portfolio-fullwidth.columns3 .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .overlay {
    text-align: left;
  }
  .portfolio-fullwidth.columns3 .portfolio-item-wrapper.portfolio-item-wrapper-style2 .portfolio-item .overlay a {
    opacity: 1;
    visibility: visible;
    -webkit-transform: translateX(0);
    -ms-transform:     translateX(0);
    transform:         translateX(0);
  }
  .portfolio-container.portfolio-masonry .portfolio-item-container .portfolio-item .portfolio-item-details {
    bottom: 0;
    margin: 0;
    width: 100%;
    height: 100%;
  }
  .main-footer .widget-contact-info.widget-contact-info-style1 address p i {
    width: 5%;
  }
  .main-footer .widget-socials.widget-socials-style2 ul li {
    margin-bottom: 0;
  }
  .main-footer .widget-socials.widget-socials-style2 ul li a {
    text-align: center;
  }
  .main-footer .widget-socials.widget-socials-style2 ul li a i {
    width: 100%;
  }
  .main-footer .widget-socials.widget-socials-style2 ul li a p {
    width: 100%;
  }
  .main-footer .widget-flickr-feed ul li a img {
    width: 64px;
    height: 64px;
  }
  .main-footer .widget-flickr-feed ul.flickr-3columns li {
    width: 64px;
    height: 64px;
    margin-right: 5px;
    margin-bottom: 5px;
  }
  .main-footer .widget-subscribe.widget-subscribe-style1 {
    margin-bottom: 60px;
  }
  .main-footer .widget-subscribe.widget-subscribe-style1 form input[type=text] {
    margin-bottom: 10px;
  }
  .main-footer .widget-subscribe.widget-subscribe-style1 form input[type=submit] {
    position: relative;
    top: auto;
    right: auto;
    display: block;
    width: 100%;
  }
  .main-footer .footer-nav.style1 ul li,
  .main-footer .footer-nav.style2 ul li {
    margin-left: 10px;
  }
  .main-footer .widget-store-items ul li article .item-details {
    overflow: hidden;
  }
  .main-footer .widget-store-items ul li article .item-details .item-rating span {
    margin-right: 0;
  }
  .main-footer .widget-store-items ul li article .item-details .item-price del {
    display: block;
  }
  .main-footer .widget-instagram-feed ul li {
    width: 64px;
    height: 64px;
  }
  .main-footer .right-skewed-border:after {
    right: 0;
  }
  .mejs-container .mejs-inner .mejs-controls .mejs-time-rail {
    margin-left: 30px;
  }
  .sidebar-right .mejs-container .mejs-inner .mejs-controls .mejs-time-rail,
  .sidebar-left .mejs-container .mejs-inner .mejs-controls .mejs-time-rail {
    margin-top: -17px;
  }
  .mejs-container .mejs-inner .mejs-controls {
    padding-right: 20px;
    padding-left: 16px;
  }
  .main-sidebar .widget.widget-instagram ul li {
    width: 100px;
    height: 100px;
  }
  .main-sidebar .widget.widget-subscribe .subscribe-form .subscribe-email {
    margin-bottom: 10px;
  }
  .main-sidebar .widget.widget-subscribe .subscribe-form input[type=submit] {
    width: 100%;
  }
  .latest-postbox.latest-postbox-style3 {
    margin-bottom: 32px !important;
  }
  .portfolio-container.columns3 .portfolio-item-container,
  .portfolio-container.columns4 .portfolio-item-container {
    width: 50%;
  }
  .portfolio-container.columns6 .portfolio-item-container,
  .portfolio-container.columns5 .portfolio-item-container {
    width: 33.33333333333333%;
  }
  .portfolio-container.columns3 .portfolio-item-container.branding{
    width: 50%;
}
.row-fluid.portfolio-item-wrapper .branding[class*="span"]:first-child {
    margin-left: 60px;
}
.margin-testimonial {
    padding: 0 66px;
}
.join-discussion {
    padding-left: 100px;
    padding-right: 100px;
}
.home-mesonry .row-fluid .portfolio-item-container.span4.branding {
    padding-left: 60px;
    width: 50%;
}
.prod-section2 .product.span4 {
    width: 29.1%;
}

}
@media screen and (max-width: 1199px) {

  .main-header.style3 .main-bar .menu-container .right-menu > ul > li {
    margin-right: 20px;
  }
  .main-header.style3 .main-bar .menu-container .right-menu.short-spacing > ul > li {
    margin-right: 17px;
  }
  .main-header.style3 .main-bar .menu-container .right-menu.short-spacing > ul .icons li{
    display: none;
  }
  .main-header.style3 .main-bar .menu-container .left-menu > ul > li {
    margin-left: 18px;
  }
  .main-header.style3 .main-bar .menu-container .right-menu > ul .icons li {
    margin-left: 15px;
  }
  .main-header.style3 .main-bar .menu-container .right-menu > ul .icons {
    margin-right: 0;
  }
  .main-header.style2 .main-bar.style2{
    padding-right: 15px;
    padding-left: 15px;
  }
  .main-nav.style4 .navigation > ul.top-nav > li {
    margin-left: 13px;
  }
  .main-nav.style4 .navigation > ul.top-nav > li:first-child {
    margin-left: 20px;
  }
  .main-nav.style4 .navigation > ul.top-nav > li > a {
    letter-spacing: 1px;
  }
  .main-nav.style4 .navigation > ul.top-nav > li.has-children > a:after{
    margin-left: 3px;
  }
  .main-nav.style4 .navigation > ul.bottom-nav li {
    margin-left: 10px;
  }
  .main-nav.style4 .navigation > ul.bottom-nav li:before {
    margin-right: 16px;
  }
  .main-nav.style4 .navigation + .shopping-menu > li{
    margin-left: 15px;
  }
  .main-nav.style5 > ul > li,
  .main-nav.style5 .shopping-menu > li:first-child {
    margin-left: 20px;
  }
}

@media screen and (min-width: 1200px) {
  /*--------------- GENERAL STYLES ---------------*/
  .container {
    padding-right: 0;
    padding-left: 0;
  }
  .portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container:last-child,
  .portfolio-container.portfolio-timeline .portfolio-item-wrapper .portfolio-item-container:nth-last-child(2) {
    margin-bottom: 0;
  }
}
@media screen and (max-width: 1200px) {

  .w1740 .product .single-product-image-container .thumbnails .slides li {
    width: 79px !important;
    margin-bottom: 10px !important;
  }
}
@media screen and (max-width: 1740px) {

  .w1740 .product .single-product-image-container .thumbnails .slides li {
    width: 102px !important;
    margin-bottom: 16px !important;
  }
  .icon-search::before{content: none !important;}
}
/* BizFunctional */
.menu-item {
	padding-top: 30px;
	padding-bottom: 30px;
}

.color_link{color: #c83827 !important;}

@media screen and (max-width: 1460px) {}

@media screen and (max-width: 1450px) {
	.menu-item {
	    padding-top: 15px ;
	}
	  /* Controls social media */
	.main-footer-links{
	      display:none;
	}
	  /* What was this? */
	.sticky-menu .main-nav{
	      /*padding-top: 0px;
	      padding-right: 0px;*/
	}

	  /* Shrinking the logo */
	.logo-container{
	      max-width:380px;
	}
}

	.sticky-logo-container{
	      max-width:290px;
	}


/* ==========================================================================
   Helper classes
   ========================================================================== */
.ir {
  background-color: transparent;
  border: 0;
  overflow: hidden;
  /* IE 6/7 fallback */
  *text-indent: -9999px;
}
.ir:before {
  content: "";
  display: block;
  width: 0;
  height: 150%;
}
/*
 * Hide from both screenreaders and browsers: h5bp.com/u
 */
.hidden {
  display: none !important;
  visibility: hidden;
}
/*
 * Hide only visually, but have it available for screenreaders: h5bp.com/v
 */
.visuallyhidden {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}
/*
 * Extends the .visuallyhidden class to allow the element to be focusable
 * when navigated to via the keyboard: h5bp.com/p
 */
.visuallyhidden.focusable:active,
.visuallyhidden.focusable:focus {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto;
}
/*
 * Hide visually and from screenreaders, but maintain layout
 */
.invisible {
  visibility: hidden;
}
/*
 * Clearfix: contain floats
 *
 * For modern browsers
 * 1. The space content is one way to avoid an Opera bug when the
 *    `contenteditable` attribute is included anywhere else in the document.
 *    Otherwise it causes space to appear at the top and bottom of elements
 *    that receive the `clearfix` class.
 * 2. The use of `table` rather than `block` is only necessary if using
 *    `:before` to contain the top-margins of child elements.
 */
.clearfix:before,
.clearfix:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.clearfix:after {
  clear: both;
}
/*
 * For IE 6/7 only
 * Include this rule to trigger hasLayout and contain floats.
 */
.clearfix {
  *zoom: 1;
}
/* ==========================================================================
   Print styles.
   Inlined to avoid required HTTP connection: h5bp.com/r
   ========================================================================== */
@media print {
  * {
    background: transparent !important;
    color: #000 !important;
    /* Black prints faster: h5bp.com/s */
    box-shadow: none !important;
    text-shadow: none !important;
  }
  a,
  a:visited {
    text-decoration: underline;
  }
  a[href]:after {
    content: " (" attr(href) ")";
  }
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  /*
     * Don't show links for images, or javascript/internal links
     */
  .ir a:after,
  a[href^="javascript:"]:after,
  a[href^="#"]:after {
    content: "";
  }
  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  thead {
    display: table-header-group;
    /* h5bp.com/t */
  }
  tr,
  img {
    page-break-inside: avoid;
  }
  img {
    max-width: 100% !important;
  }
  @page {
    margin: 0.5cm;
  }
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    page-break-after: avoid;
  }
}
.icon-search::before{content: none !important;}
.donate:hover {
	background: #444 none repeat scroll 0 0 !important;
	background-color: #444!important;
	color: white !important;
}
    .crumbs { float: right;margin: 0;padding: 0;list-style: none;font-size: 11px;color: #c4c4c4;}
.crumbs li { display: inline-block;line-height: 54px;}
.crumbs li:after { content: "\f054"; display: inline-block;position: relative;top: -1px;padding: 0 0 0 3px;font: 6px/54px 'FontAwesome';}
.crumbs li:first-child:after, .crumbs li:last-child:after {display: none;}
.crumbs a { padding: 0 4px;text-decoration: none;color: #c4c4c4;-webkit-transition: color 0.2s ease;transition: color 0.2s ease;}
.crumbs a:hover { color: #ee4a37;}

.nav-tabs > .active > a{ color:#000; background:#c93827; }
 .nav-tabs>.active>a:hover { color:#000!important; background:#c93827!important; }
.nav-tabs a { color:#455b68; background:#d3d3d3; }
 .nav-tabs a:hover { color:#455b68!important; background:#d3d3d3!important; }
.nav-tabs>li>a { margin-right:23px; background:#ececec; }
.nav-tabs>li:last-child>a { margin-right:auto; }
.nav { margin-bottom:0px; }
.tab-content { border:2px solid #ddd; min-height:220px; padding:10px; margin-bottom:20px; background:#fff; }

div.sticky-main-nav {
	/*margin-top: 30px;*/
}

h1.wow, h3.wow, div.wow {
	color: #545454;
}

table {
	margin: 0 auto;
}

/****23/7/18****/
.zoneresource .SectionTitleTextBold a span{color: #18297d !important}
.button.button-medium.white-bg:hover span {color: #fff !important; }
/*a span, .btn.colored span, .zoneresource td em, .zoneresource td em strong{color: #18296D !important;}*/
/****Event page****/
/*
#Main1563137 .row-fluid.align-center.eventImages{position: relative;}
#Main1563137 .row-fluid.align-center.eventImages::after {position: absolute; right: 0; top: 50%; transform: translateY(-50%); background: url('../images/misc/icon1.png'); content: ""; width: 30px; height: 84px; background-repeat: no-repeat; }
#Main1563137 .span12.white-bg.category-section-box1.align-center * {text-align: center !important; }
*/
.event-box .btn{color: #18297d; height: 40px; width: 140px; font-size: 13px; font-weight: 500; border-radius: 0px; border: 2px solid black; line-height: 1; }
.span6.event-left, .span6.event-right {float: none; display: inline-block; margin: 0 -2px; vertical-align: middle; }
.event-right .block {display: block; }
.event-right{position: relative; padding-left: 50px}
.event-right:after{position: absolute; content: ""; left:15px; top: 30px; background: url(/userimages/splitter.png) no-repeat; width: 30px; height: 84px;}
.event-left img {width: 100%; }
.row-fluid.event-lt-rt {margin-bottom: 50px; margin-top: 30px; }
.event-box {padding-bottom: 50px; border-bottom: 1px solid lightgrey; margin-bottom: 30px; }
.event-noimage:after{background: none;}
.event-noimage{padding-left: 0px;}
@media screen and (max-width: 767px) {
.event-right::after {left: 50%; top: 0; width: 30px; height: 84px; transform: rotate(90deg); margin-left: -15px; }
.event-right{ padding: 70px 15px 0; }
.row-fluid.event-lt-rt {margin-bottom: 0;}
}