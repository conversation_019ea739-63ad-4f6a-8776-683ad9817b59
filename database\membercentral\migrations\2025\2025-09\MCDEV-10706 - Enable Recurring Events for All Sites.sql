USE membercentral
GO

-- Create temporary table to track sites that need the feature enabled
DECLARE @sites TABLE (siteID int PRIMARY KEY, siteCode varchar(10), orgCode varchar(10));

-- Get all active sites that currently have recurringEvents disabled
INSERT INTO @sites (siteID, siteCode, orgCode)
SELECT DISTINCT s.siteID, s.siteCode, o.orgCode
FROM dbo.siteFeatures AS sf
INNER JOIN dbo.sites AS s ON s.siteID = sf.siteID
INNER JOIN dbo.organizations AS o ON o.orgID = s.orgID
INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = s.siteResourceID 
    AND sr.siteResourceStatusID = 1  -- Only active sites
WHERE sf.recurringEvents = 0;

-- Enable recurring events for all sites that don't have it enabled
-- Using the standard enableSiteFeature procedure for consistency
DECLARE @siteID int, @siteCode varchar(10), @orgCode varchar(10);

SELECT @siteID = min(siteID) FROM @sites;
WHILE @siteID IS NOT NULL BEGIN
    SELECT @siteCode = siteCode, @orgCode = orgCode FROM @sites WHERE siteID = @siteID;
    
    BEGIN TRY
		EXEC dbo.enableSiteFeature @siteID=@siteID, @toolTypeList='RecurringEvents';
    END TRY
    BEGIN CATCH
        PRINT 'ERROR enabling recurring events for site ' + @siteCode + ' (' + @orgCode + '): ' + ERROR_MESSAGE();
    END CATCH
    
    SELECT @siteID = min(siteID) FROM @sites WHERE siteID > @siteID;
END

GO