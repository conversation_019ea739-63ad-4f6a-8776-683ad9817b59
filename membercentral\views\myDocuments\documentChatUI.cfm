<cfset local.dataStruct= attributes.data>
<cfif structKeyExists(application.objCMS,"getPlatformCacheBusterKey")>
	<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
<cfelse>
	<cfset local.assetCachingKey = "">
</cfif>
<cfoutput>
<!DOCTYPE html>
<html lang="en">
	<head>
		<cftry>
			#application.objCMS.getFrontendErrorTrackingCode()#
			<cfcatch type="any"></cfcatch>
		</cftry>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<title>#local.dataStruct.qryAIExpert.first_name# #local.dataStruct.qryAIExpert.last_name# - TrialSmith Expert Chat</title>

		<!--- latest versions of bootstrap 5.x and jquery --->
		<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.7/css/bootstrap.min.css" rel="stylesheet">
		<script defer src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.7/js/bootstrap.bundle.min.js"></script>
		<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

		<!--- relevant platformwide includes from cmsFunctions.renderTemplate() --->
		<link rel="stylesheet" type="text/css" href="/assets/common/images/font-awesome/6.7.2/css/all.min.css" onerror="MCJSErrorReporting.cssLoadError(event)">
		<script defer type='text/javascript' src='/assets/common/javascript/handlebars/handlebars-v4.7.8.js' onerror="MCJSErrorReporting.scriptLoadError(event)"></script>
		<script defer type='text/javascript' src='/assets/common/javascript/platformUtils.js#local.assetCachingKey#' onerror="MCJSErrorReporting.scriptLoadError(event)"></script>
		<script defer type='text/javascript' src='/assets/common/javascript/ajax.js#local.assetCachingKey#' onerror="MCJSErrorReporting.scriptLoadError(event)"></script>
		<script type='text/javascript'>
			var #ToScript(arguments.event.getValue('mc_siteInfo.ajaxAppInstanceID'),'ajaxAppInstanceID')#
			var #ToScript(arguments.event.getValue('mc_siteInfo.appProxyAppInstanceID'),'appProxyAppInstanceID')#
			var #ToScript(local.dataStruct.expDocChatResURL,'ts_expdocchatlink')#
			var #toScript(application.regEx.email, "mc_emailregex")#

			var MCPromises = {
			<cfif listFindNoCase("beta,newbeta",application.MCEnvironment) AND application.objPlatform.getCurrentHostname() eq "#arguments.event.getValue('mc_siteInfo.sitecode')#beta.membercentral.com">
				BackendPlatformServices: MCLoader.loadJS('//clickbeta.membercentral.com/assets/common/javascript/backendPlatformServices.js#local.assetCachingKey#').catch(MCJSErrorReporting.promiseRejectionHandler)
			<cfelse>
				BackendPlatformServices: MCLoader.loadJS('//#application.paths.backendPlatform.hostname#/assets/common/javascript/backendPlatformServices.js#local.assetCachingKey#').catch(MCJSErrorReporting.promiseRejectionHandler)
			</cfif>
			};
			try { MCKeepAlive.startKeepAlive(300,120); } catch (error) {console.error('Failed to start keepalive',error)}
		</script>
		<script type='text/javascript' src='/assets/common/javascript/expertdocchat.js#local.assetCachingKey#'></script>
		<style>
			html, body { margin:0;padding:0;width:100%;height:100%;}
			main {overflow-y:hidden;}
			.depo-scroll-col{max-height:70vh;overflow-y:auto;padding-bottom:50px;}
			.add-depo-card.selected {border:2px solid ##0d6efd;background-color:##eef6ff;}
			.depo-summary-card {width:410px !important;}

			<!--- Medium devices (tablets, less than 992px) --->
			@media (max-width: 991.98px) {
				main {overflow-y:auto;}
				.depo-scroll-col {margin-bottom:50px;}
				.depo-summary-card {width:100% !important;}
			}
		</style>
		<script defer src="/sitecomponents/COMMON/webcomponents/trialsmith-document-chat/trialsmith-chat-component.umd.js#local.assetCachingKey#"></script>
	</head>
	<body class="d-flex flex-column min-vh-100">
		<header class="bg-light p-3">
			<div class="d-flex justify-content-between align-items-center">
				<h4 class="fw-bold mb-0">
					#local.dataStruct.qryAIExpert.first_name# #local.dataStruct.qryAIExpert.last_name# <span>| TrialSmith Expert Chat</span>
    			</h4>
				<a href="/?pg=myDocuments&tab=PTSAI" type="button" class="btn btn-outline-primary btn-sm shadow-sm">
					<i class="fa-solid fa-arrow-left me-1"></i> Back to AI Expert Case Files
				</a>
			</div>
		</header>

		<main class="d-flex flex-column flex-grow-1 p-3">
			<cfif local.dataStruct.qryAIExpert.recordCount>
				<nav>
					<div class="nav nav-tabs" id="nav-tab" role="tablist">
						<button class="nav-link" id="nav-adddepo-tab" data-bs-toggle="tab" data-bs-target="##nav-adddepo" type="button" role="tab" aria-controls="nav-adddepo" aria-selected="false">Add Depositions</button>
						<button class="nav-link active" id="nav-qa-tab" data-bs-toggle="tab" data-bs-target="##chatinterface" type="button" role="tab" aria-controls="nav-qa" aria-selected="true">Ask Questions</button>
						<button class="nav-link" id="nav-rwnotes-tab" data-bs-toggle="tab" data-bs-target="##nav-rwnotes" type="button" role="tab" aria-controls="nav-rwnotes" aria-selected="false">Review Notes</button>
					</div>
				</nav>
				<div class="tab-content flex-grow-1 d-flex flex-column" id="nav-tabContent">
					<div class="tab-pane flex-column flex-grow-1 fade show active" id="chatinterface" role="tabpanel" aria-labelledby="nav-qa-tab" tabindex="0">
						<trialsmith-chat
							api-base-url="/trialsmithdocumentchatapi"
							project-id="#local.dataStruct.qryAIExpert.agentUserUID#"
							webviewer-lib-path="/sitecomponents/COMMON/javascript/webviewer/11.7.1/"
						>
							<suggestion-item
								key="suggestion-1"
								prompt="This expert has been hired by the defense and my goal is to impeach him. Give me - a concise synopsis of this expert's areas of expertise and qualifications in bulleted list format - the top 5 areas discussed that are related to his expertise"
								label="Top Areas Discussed">
							</suggestion-item>

							<suggestion-item
								key="suggestion-2"
								prompt="I'm researching this expert and I would like information about each of the cases represented by the transcripts. Give me a list of the case citations dates of the cases."
								label="Case List">
							</suggestion-item>
						</trialsmith-chat>
					</div>
					<div class="tab-pane flex-column flex-grow-1 fade" id="nav-adddepo" role="tabpanel" aria-labelledby="nav-adddepo-tab" tabindex="0">
						<div class="row p-3 pb-0 h-100">
							<div class="col-12 col-lg px-2">
								<div class="card h-100">
									<h5 class="card-header bg-light">Depositions Already Added for Analysis</h5>
									<div id="listassocdepos" class="card-body depo-scroll-col"></div>
								</div>
							</div>
							<div class="col-12 col-lg mt-lg-0 mt-3 px-2">
								<div class="card h-100">
									<h5 class="card-header bg-light">Depositions Available to Add</h5>
									<div id="listadddepos" class="card-body depo-scroll-col"></div>
								</div>
							</div>
							<div class="col-12 col-lg-auto mt-lg-0 mt-3 px-2">
								<div id="addDepoSummaryCard" class="card h-100 depo-summary-card">
									<h5 class="card-header bg-light">Pay to Add Depositions</h5>
									<div id="listsummarydepos" class="card-body depo-scroll-col"></div>
								</div>
								<div id="depoPurchaseConfirmationCard" class="card h-100 depo-summary-card d-none">
									<h5 class="card-header bg-light">Payment Confirmation</h5>
									<div id="depoPurchaseConfirmation" class="card-body depo-scroll-col"></div>
								</div>
							</div>
						</div>
					</div>
					<div class="tab-pane flex-column flex-grow-1 fade" id="nav-rwnotes" role="tabpanel" aria-labelledby="nav-rwnotes-tab" tabindex="0">
						<h1>Coming Soon</h1>
					</div>
				</div>
			<cfelse>
				<div class="tsAppHeading">AI Expert Not Found</div>
				<br/>
				<div class="tsAppBodyText">The AI Expert was not found. <a href="javascript:self.close();">Close this window</a> to return to your purchased AI Experts.</div>
			</cfif>
		</main>

		<footer class="bg-light p-3 mt-auto">
			<p>Footer</p>
		</footer>
	</body>
</html>
</cfoutput>
