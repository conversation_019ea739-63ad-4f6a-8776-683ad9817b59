-- MCDEV-10706: Quick Status Check
-- Use this script to quickly verify the current state of recurring events feature across all sites

USE membercentral
GO

PRINT '=== MCDEV-10706: Recurring Events Feature Status ===';
PRINT 'Execution Time: ' + CONVERT(varchar(20), GETDATE(), 120);
PRINT '';

-- Overall summary
SELECT 
    'OVERALL SUMMARY' AS [Status Check],
    COUNT(*) AS [Total Active Sites],
    SUM(CASE WHEN sf.recurringEvents = 1 THEN 1 ELSE 0 END) AS [Sites With Recurring Events],
    SUM(CASE WHEN sf.recurringEvents = 0 THEN 1 ELSE 0 END) AS [Sites Without Recurring Events],
    CAST(SUM(CASE WHEN sf.recurringEvents = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS decimal(5,2)) AS [Percentage Enabled]
FROM dbo.siteFeatures sf
INNER JOIN dbo.sites s ON s.siteID = sf.siteID
INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID 
    AND sr.siteResourceStatusID = 1;

-- Breakdown by site type
SELECT 
    CASE WHEN sf.subscriptions = 1 THEN 'AMS Sites' ELSE 'Non-AMS Sites' END AS [Site Type],
    COUNT(*) AS [Total Sites],
    SUM(CASE WHEN sf.recurringEvents = 1 THEN 1 ELSE 0 END) AS [With Recurring Events],
    SUM(CASE WHEN sf.recurringEvents = 0 THEN 1 ELSE 0 END) AS [Without Recurring Events],
    CAST(SUM(CASE WHEN sf.recurringEvents = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS decimal(5,2)) AS [Percentage Enabled]
FROM dbo.siteFeatures sf
INNER JOIN dbo.sites s ON s.siteID = sf.siteID
INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID 
    AND sr.siteResourceStatusID = 1
GROUP BY sf.subscriptions
ORDER BY sf.subscriptions DESC;

-- Migration readiness check
DECLARE @sitesNeedingMigration int;
SELECT @sitesNeedingMigration = COUNT(*)
FROM dbo.siteFeatures sf
INNER JOIN dbo.sites s ON s.siteID = sf.siteID
INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID 
    AND sr.siteResourceStatusID = 1
WHERE sf.recurringEvents = 0;

SELECT 
    'MIGRATION STATUS' AS [Check Type],
    @sitesNeedingMigration AS [Sites Needing Migration],
    CASE 
        WHEN @sitesNeedingMigration = 0 THEN 'COMPLETE: All sites have recurring events enabled'
        ELSE 'PENDING: ' + CAST(@sitesNeedingMigration AS varchar(10)) + ' sites need migration'
    END AS [Status]

-- Existing recurring events data summary
SELECT 
    'EXISTING DATA' AS [Check Type],
    (SELECT COUNT(*) FROM dbo.ev_recurringSeries) AS [Total Recurring Series],
    (SELECT COUNT(*) FROM dbo.ev_events WHERE recurringSeriesID IS NOT NULL) AS [Events In Series],
    (SELECT COUNT(DISTINCT siteID) FROM dbo.ev_recurringSeries) AS [Sites With Recurring Data];

-- Sample of sites without recurring events (if any)
IF @sitesNeedingMigration > 0 BEGIN
    PRINT '';
    PRINT 'SAMPLE SITES WITHOUT RECURRING EVENTS:';
    PRINT '======================================';
    
    SELECT TOP 10
        s.siteCode AS [Site Code],
        o.orgCode AS [Org Code],
        o.orgName AS [Organization Name],
        CASE WHEN sf.subscriptions = 1 THEN 'AMS' ELSE 'Non-AMS' END AS [Site Type]
    FROM dbo.siteFeatures sf
    INNER JOIN dbo.sites s ON s.siteID = sf.siteID
    INNER JOIN dbo.organizations o ON o.orgID = s.orgID
    INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID 
        AND sr.siteResourceStatusID = 1
    WHERE sf.recurringEvents = 0
    ORDER BY s.siteCode;
    
    IF @sitesNeedingMigration > 10 BEGIN
        PRINT '';
        PRINT 'Note: Showing first 10 sites. Total sites needing migration: ' + CAST(@sitesNeedingMigration AS varchar(10));
    END
END
ELSE BEGIN
    PRINT '';
    PRINT '✓ SUCCESS: All active sites have recurring events enabled.';
END

-- Quick validation for common issues
DECLARE @amsWithoutRecurring int, @inactiveSitesWithRecurring int;

SELECT @amsWithoutRecurring = COUNT(*)
FROM dbo.siteFeatures sf
INNER JOIN dbo.sites s ON s.siteID = sf.siteID
INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID 
    AND sr.siteResourceStatusID = 1
WHERE sf.subscriptions = 1 AND sf.recurringEvents = 0;

SELECT @inactiveSitesWithRecurring = COUNT(*)
FROM dbo.siteFeatures sf
INNER JOIN dbo.sites s ON s.siteID = sf.siteID
INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID 
    AND sr.siteResourceStatusID != 1
WHERE sf.recurringEvents = 1;

IF @amsWithoutRecurring > 0 OR @inactiveSitesWithRecurring > 0 BEGIN
    PRINT '';
    PRINT 'POTENTIAL ISSUES DETECTED:';
    PRINT '==========================';
    
    IF @amsWithoutRecurring > 0
        PRINT '⚠ WARNING: ' + CAST(@amsWithoutRecurring AS varchar(10)) + ' AMS sites do not have recurring events enabled';
    
    IF @inactiveSitesWithRecurring > 0
        PRINT '⚠ INFO: ' + CAST(@inactiveSitesWithRecurring AS varchar(10)) + ' inactive sites have recurring events enabled';
END

PRINT '';
PRINT '=== End of Status Check ===';

GO
