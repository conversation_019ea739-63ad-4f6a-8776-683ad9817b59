USE membercentral
GO

-- MCDEV-10706: Rollback Script
-- Use this script ONLY if you need to rollback the recurring events enablement
-- This will restore the feature to AMS-only sites (original MCDEV-10081 state)

PRINT 'MCDEV-10706: ROLLBACK SCRIPT';
PRINT '============================';
PRINT 'WARNING: This will disable recurring events for non-AMS sites!';
PRINT '';

-- Verify current state before rollback
PRINT '1. CURRENT STATE BEFORE ROLLBACK';
PRINT '================================';

SELECT 
    CASE WHEN sf.subscriptions = 1 THEN 'AMS Sites' ELSE 'Non-AMS Sites' END AS SiteType,
    COUNT(*) AS TotalSites,
    SUM(CASE WHEN sf.recurringEvents = 1 THEN 1 ELSE 0 END) AS WithRecurringEvents,
    SUM(CASE WHEN sf.recurringEvents = 0 THEN 1 ELSE 0 END) AS WithoutRecurringEvents
FROM dbo.siteFeatures sf
INNER JOIN dbo.sites s ON s.siteID = sf.siteID
INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID 
    AND sr.siteResourceStatusID = 1
GROUP BY sf.subscriptions
ORDER BY sf.subscriptions DESC;

-- Check for any recurring events data on non-AMS sites that would be affected
PRINT '';
PRINT '2. RECURRING EVENTS DATA ON NON-AMS SITES';
PRINT '=========================================';

SELECT 
    'Data Impact Check' AS CheckType,
    COUNT(DISTINCT rs.siteID) AS NonAMSSitesWithRecurringData,
    COUNT(rs.seriesID) AS TotalRecurringSeriesOnNonAMS,
    COUNT(e.eventID) AS TotalEventsInRecurringSeriesOnNonAMS
FROM dbo.ev_recurringSeries rs
INNER JOIN dbo.siteFeatures sf ON sf.siteID = rs.siteID AND sf.subscriptions = 0
LEFT JOIN dbo.ev_events e ON e.recurringSeriesID = rs.seriesID;

-- Show specific non-AMS sites that have recurring events data
IF EXISTS (
    SELECT 1 FROM dbo.ev_recurringSeries rs
    INNER JOIN dbo.siteFeatures sf ON sf.siteID = rs.siteID AND sf.subscriptions = 0
) BEGIN
    PRINT '';
    PRINT 'WARNING: The following non-AMS sites have recurring events data:';
    
    SELECT DISTINCT
        s.siteCode,
        o.orgCode,
        o.orgName,
        COUNT(rs.seriesID) AS RecurringSeriesCount,
        COUNT(e.eventID) AS EventsInSeriesCount
    FROM dbo.ev_recurringSeries rs
    INNER JOIN dbo.siteFeatures sf ON sf.siteID = rs.siteID AND sf.subscriptions = 0
    INNER JOIN dbo.sites s ON s.siteID = rs.siteID
    INNER JOIN dbo.organizations o ON o.orgID = s.orgID
    LEFT JOIN dbo.ev_events e ON e.recurringSeriesID = rs.seriesID
    GROUP BY s.siteCode, o.orgCode, o.orgName
    ORDER BY s.siteCode;
    
    PRINT '';
    PRINT 'CRITICAL WARNING: Rolling back will disable recurring events functionality';
    PRINT 'for these sites, but their existing recurring events data will remain.';
    PRINT 'Consider the impact before proceeding with rollback.';
END

-- Pause for manual confirmation (uncomment the following lines if you want a manual confirmation step)
/*
PRINT '';
PRINT 'MANUAL CONFIRMATION REQUIRED:';
PRINT 'Are you sure you want to proceed with the rollback?';
PRINT 'This action will disable recurring events for all non-AMS sites.';
PRINT '';
PRINT 'To proceed, uncomment the rollback execution section below and re-run this script.';
RETURN;
*/

-- =====================================================
-- ROLLBACK EXECUTION
-- =====================================================
-- UNCOMMENT THE SECTION BELOW TO EXECUTE THE ROLLBACK

/*
PRINT '';
PRINT '3. EXECUTING ROLLBACK';
PRINT '====================';

-- Get list of non-AMS sites that will be affected
DECLARE @sitesToDisable TABLE (siteID int PRIMARY KEY, siteCode varchar(10), orgCode varchar(10));

INSERT INTO @sitesToDisable (siteID, siteCode, orgCode)
SELECT s.siteID, s.siteCode, o.orgCode
FROM dbo.siteFeatures sf
INNER JOIN dbo.sites s ON s.siteID = sf.siteID
INNER JOIN dbo.organizations o ON o.orgID = s.orgID
INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID 
    AND sr.siteResourceStatusID = 1
WHERE sf.subscriptions = 0 AND sf.recurringEvents = 1;

DECLARE @sitesToDisableCount int;
SELECT @sitesToDisableCount = COUNT(*) FROM @sitesToDisable;

PRINT 'Sites to disable recurring events: ' + CAST(@sitesToDisableCount AS varchar(10));

-- Disable recurring events for non-AMS sites
UPDATE dbo.siteFeatures 
SET recurringEvents = 0 
WHERE siteID IN (SELECT siteID FROM @sitesToDisable);

PRINT 'Rollback completed. Disabled recurring events for ' + CAST(@@ROWCOUNT AS varchar(10)) + ' non-AMS sites.';
*/

-- =====================================================
-- POST-ROLLBACK VERIFICATION
-- =====================================================
-- UNCOMMENT THE SECTION BELOW TO VERIFY ROLLBACK RESULTS

/*
PRINT '';
PRINT '4. POST-ROLLBACK VERIFICATION';
PRINT '=============================';

-- Verify rollback results
SELECT 
    CASE WHEN sf.subscriptions = 1 THEN 'AMS Sites' ELSE 'Non-AMS Sites' END AS SiteType,
    COUNT(*) AS TotalSites,
    SUM(CASE WHEN sf.recurringEvents = 1 THEN 1 ELSE 0 END) AS WithRecurringEvents,
    SUM(CASE WHEN sf.recurringEvents = 0 THEN 1 ELSE 0 END) AS WithoutRecurringEvents,
    CASE 
        WHEN sf.subscriptions = 1 AND SUM(CASE WHEN sf.recurringEvents = 0 THEN 1 ELSE 0 END) > 0 
        THEN 'ERROR: Some AMS sites disabled'
        WHEN sf.subscriptions = 0 AND SUM(CASE WHEN sf.recurringEvents = 1 THEN 1 ELSE 0 END) > 0 
        THEN 'ERROR: Some non-AMS sites still enabled'
        ELSE 'SUCCESS: Rollback completed correctly'
    END AS RollbackStatus
FROM dbo.siteFeatures sf
INNER JOIN dbo.sites s ON s.siteID = sf.siteID
INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID 
    AND sr.siteResourceStatusID = 1
GROUP BY sf.subscriptions
ORDER BY sf.subscriptions DESC;

-- List any sites that have unexpected status after rollback
SELECT 
    s.siteCode,
    o.orgCode,
    CASE WHEN sf.subscriptions = 1 THEN 'AMS' ELSE 'Non-AMS' END AS SiteType,
    sf.recurringEvents,
    CASE 
        WHEN sf.subscriptions = 1 AND sf.recurringEvents = 0 THEN 'ERROR: AMS site disabled'
        WHEN sf.subscriptions = 0 AND sf.recurringEvents = 1 THEN 'ERROR: Non-AMS site still enabled'
        ELSE 'OK'
    END AS Status
FROM dbo.siteFeatures sf
INNER JOIN dbo.sites s ON s.siteID = sf.siteID
INNER JOIN dbo.organizations o ON o.orgID = s.orgID
INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID 
    AND sr.siteResourceStatusID = 1
WHERE (sf.subscriptions = 1 AND sf.recurringEvents = 0) 
   OR (sf.subscriptions = 0 AND sf.recurringEvents = 1)
ORDER BY s.siteCode;

PRINT '';
PRINT 'ROLLBACK VERIFICATION COMPLETED';
*/

PRINT '';
PRINT '=== MCDEV-10706 Rollback Script Complete ===';
PRINT 'NOTE: Rollback execution is commented out for safety.';
PRINT 'Uncomment the execution sections if rollback is truly needed.';

GO
