#menu ul	{ list-style: none; margin:0; padding:0; z-index: 9999;}
#menu li  { display:block; }
#menu a   { display:block; text-decoration:none; }
/* LEVEL ONE ******************************************************************************************************* */
ul.dropdown      { position: relative; }
ul.dropdown li   { 
float:left; 
font-family:'Open Sans'; 
font-size:13px; 
line-height:80px;
}

ul.dropdown li a {  color: #fff;  padding: 0 5px; }
ul.dropdown a:hover { color: #28292b; background: #ffbc01; }
ul.dropdown li.hover, ul.dropdown li:hover { position: relative; }


/* 	LEVEL TWO ******************************************************************************************************** */
ul.dropdown ul.sub_menu{ 
  border-right:1px solid #525355;
  border-bottom:1px solid #525355;
  border-left:1px solid #525355;
 }
ul.dropdown ul.sub_menu     { width:200px; visibility:hidden; position:absolute; top:100%; left:0; }
ul.dropdown ul.sub_menu li 	{ font-weight:normal; float:none; font-size:10pt; line-height: 20px;}
/* IE 6 & 7 Needs Inline Block */
ul.dropdown ul.sub_menu li a       { padding:0; margin:0;}
ul.dropdown ul.sub_menu li a       { 
  background:#2d2e31; 
  border-top:1px solid #525355; 
  border-right:none;  
  color: #ffbc01;
  display:block; 
  padding:4px 8px; 
} 

ul.dropdown ul.sub_menu li a:hover  { background:#4f5052; color:#ffbc01; } 

/* LEVEL THREE ********************************************************************************************************* */
ul.dropdown ul.sub_menu ul.sub_menu  { left: 100%; top: 0; }
ul.dropdown li:hover > ul 			  { visibility: visible; }
div.AnyTime-win {z-index:9999}