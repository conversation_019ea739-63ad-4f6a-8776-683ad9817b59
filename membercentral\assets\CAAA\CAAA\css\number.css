/*! formstone v0.5.3 [number.css] 2015-04-17 | MIT License | formstone.it */

/**
	 * @class
	 * @name .fs-number-element
	 * @type element
	 * @description Target elmement
	 */
/**
	 * @class
	 * @name .fs-number
	 * @type element
	 * @description Base widget class
	 */
/**
	 * @class
	 * @name .fs-number.fs-number-disabled
	 * @type modifier
	 * @description Indicates disabled state
	 */
.fs-number {
  position: relative;
  border-radius: 3px;
  margin: 0 0 10px 0;
  overflow: hidden;
  /**
		 * @class
		 * @name .fs-number-arrow
		 * @type element
		 * @description Arrow elmement
		 */
  /**
		 * @class
		 * @name .fs-number-arrow:after
		 * @type element
		 * @description Arrow icon
		 */
  /**
		 * @class
		 * @name .fs-number-arrow.fs-number-up
		 * @type modifier
		 * @description Indicates up state
		 */
  /**
		 * @class
		 * @name .fs-number-arrow.fs-number-down
		 * @type modifier
		 * @description Indicates up state
		 */
}
.fs-number,
.fs-number:after,
.fs-number:before,
.fs-number *,
.fs-number *:after,
.fs-number *:before {
  box-sizing: border-box;
  -webkit-transition: none;
          transition: none;
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
      -ms-user-select: none !important;
          user-select: none !important;
}
.fs-number-element {
  width: 100%;
  background: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 3px;
  color: #222222;
  font-size: 15px;
  line-height: 1;
  overflow: hidden;
  padding: 0 10px;
  -moz-appearance: textfield;
}
.fs-number-element::-webkit-inner-spin-button,
.fs-number-element::-webkit-outer-spin-button {
  margin: 0;
  -webkit-appearance: none;
}
.fs-number-element::-ms-clear {
  display: none;
}
.fs-number-element:focus {
  background-color: #ffffff;
}
.fs-number-disabled .fs-number-element {
  background: #ffffff;
  border-color: #cccccc;
  color: #cccccc;
}
.fs-number-arrow {
  width: 25px;
  height: 50%;
  position: absolute;
  right: 0;
  z-index: 1;
  background: #ffffff;
  border: 1px solid #cccccc;
  cursor: pointer;
  display: block;
  overflow: hidden;
  text-indent: 200%;
  white-space: nowrap;
}
.no-opacity .fs-number-arrow {
  text-indent: -999999px;
}
.fs-number-arrow:focus {
  outline: none;
}
.fs-number-arrow:after {
  width: 0;
  height: 0;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  content: '';
  display: block;
  margin: auto;
}
.fs-number-arrow.fs-number-up {
  top: 0;
}
.fs-number-arrow.fs-number-up:after {
  border-bottom: 5px solid #666666;
}
.fs-number-arrow.fs-number-down {
  bottom: 0;
  border-top: none;
}
.fs-number-arrow.fs-number-down:after {
  border-top: 5px solid #666666;
}
.fs-number-disabled .fs-number-arrow {
  cursor: default;
}
.fs-number-disabled .fs-number-arrow:after {
  border-top-color: #cccccc;
  border-bottom-color: #cccccc;
}
/*
	@media screen and (min-width: 740px) {
		.stepper:hover .stepper-input { background-color: #fff; }

		.stepper .stepper-step:hover { background-color: #F9F9F9; }

		.stepper.disabled .stepper-arrow { background: #fff; border-color: #eee; cursor: default; }
	}

	.stepper.disabled .stepper-input { background: #fff; border-color: #eee; color: #ccc; }
	.stepper.disabled .stepper-arrow { background: #fff; border-color: #eee; cursor: default; }
*/
