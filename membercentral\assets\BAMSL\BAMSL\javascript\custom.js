$(window).on("load resize", function(e) {
	if ($(window).width() < 980) {
		$('.megaMenuSection >p ').addClass('kk');
	} else {
		$('.megaMenuSection >p ').removeClass('kk');
		$('.mainMenuOnclick ').slideUp();
	}
});
	
$(document).ready(function() {
	if($("#websiteCarousel").text().length == 0) $("#websiteCarousel").parents(".progresReportBox").hide();
	
    $('header .dropdown-menu input[type=text]').blur(function() {
        $(this).parent().closest('.dropdown').removeClass('hoverMenu').addClass('hoverMenu');
    })
    .focus(function() {
        $(this).parent().closest('.dropdown').removeClass('hoverMenu');
    });

	$(".btn-navbar").click(function() {
        $("body").toggleClass("overlay");
    });
    $(".menu-arrow").click(function() {
        $(this).parents(".dropdown").toggleClass('open-droupdown');
        $(this).parents(".dropdown").children(".dropdown-menu").slideToggle();
        $('.mainMenuMob').slideUp();
    });

    $('.mainMenuOnclickBtn').click(function() {

        $(this).parents('.mainMenuMob').find('.mainMenuOnclick').slideToggle();
        $(this).parents('.mainMenuMob').find('.mainMenuOnclick').addClass('kkk');
    });

    $('body').delegate('.kk', 'click', function() {
        $(this).next('.mainMenuMob').slideToggle();
        $('.mainMenuOnclick').slideUp();
    });

    var _siteLogoAnchor = $('.siteBrandSection a').addClass('navbar-brand');
    var _siteLogoImg = $('.siteBrandSection a img').addClass('img-responsive');
	var _printUrl = $('.logoSubUrl').parent().html();
	
    var _siteLogoContent = $('.siteBrandSection').html();
    $('.siteBrandSection a').insertAfter('.siteBrandSection');
	$('.navbar-brand').append(_printUrl);
	$('.printHeader').append($('.navbar-brand').html());
	$('.printHeader').css('width',$('.navbar-brand img').width()+'px');
	$('.printHeaderContact').append('<ul>'+$('.contactSection div div ul').html()+'</ul>');
	
    $(".slider .owl-carousel").owlCarousel({
        items: 1,
        margin: 0,
        loop: true,
        autoplay: true,
        autoPlaySpeed: 5000,
        touchDrag: false,
        mouseDrag: false
    });

    if ($('#featuredPartnersContent').length != 0) {
        var _heading = $('#featuredPartnersContent > div > div  > h3');
        var _images = $('#featuredPartnersContent > div > div > ul > li');
        var _button = $('#featuredPartnersContent > div > div  >div > a');

        $('#featuredPartnersSection').append(_heading[0].outerHTML);
        $('#featuredPartnersSection').append('<div class="sliderFrame"><div class="owl-carousel owl-theme"></div></div>');
        $('#featuredPartnersSection').append(_button[0].outerHTML);

        _images.each(function() {
            $('#featuredPartnersSection .sliderFrame .owl-carousel').append('<div class="item">' + $(this).html() + '</div>');
        });
    }

    if ($('#rightSponsorsContent').length != 0) {
        var _images = $('#rightSponsorsContent >  ul > li');

        $('.rightSponsorsSection').append('<div class="owl-carousel owl-theme rightSponsorsCarousel"></div>');
        _images.each(function() {
            $('.rightSponsorsCarousel').append('<div class="item">' + $(this).html() + '</div>');
        });
    }
	if ($('.rightSponsorsSection').length != 0) {
		$(".rightSponsorsCarousel").owlCarousel({
			items: 1,
			margin: 0,
			loop: true,
			autoplay: true,
			autoPlaySpeed: 5000,
			touchDrag: false,
			mouseDrag: false,
			dots: false,
			center: true,
			nav:false,
			onInitialize : function(elem){
				randomizeInitialSlideCustom(elem,'rightSponsorsCarousel');
			}
		});
	}
    if ($(".sliderFrame").length != 0 && $(".sliderFrame").is(':visible')) {
        $(".sliderFrame .owl-carousel").owlCarousel({
            loop: true,
            margin: 10,
            nav: true,
            navText: ["<img src='images/prev.png'>", "<img src='images/next.png'>"],
            loop: $('#featuredPartnersContent > div > div > ul > li').length > 7,
            autoplay: true,
            autoPlaySpeed: 2000,
            responsive: {
                0: {
                    items: 1,
                    nav: false,
                    margin: 15,
                },
                481: {
                    items: 2,
                    nav: false,
                    margin: 15,
                },
                600: {
                    items: 3,
                    nav: false,
                    margin: 15,
                },
                768: {
                    items: 5
                },
                980: {
                    items: 7
                },
                1200: {
                    items: 7
                }
            }
        });
    }
	if($('.eventBoxTop').length != 0) {
		$('.eventBoxTop').each(function() {
			var _this = $(this);
			if (_this.children('h3')[0] != undefined)
				var _headingContent = _this.children('h3')[0].outerHTML;
			_this.next().prepend(_headingContent);
			_this.children('h3').hide();
		});
		

		$('.eventBoxTop > div:first-child').addClass('eventImgBox');
		$('.eventBoxTop > div:nth-child(2)').addClass('eventimgText');

		$('.eventBoxBottom h3 > a').addClass('seeAll');
		$('.eventBoxBottom').each(function() {
			var _this = $(this);
			if (_this.children('div').hasClass('mcMergeTemplate')) {
				_this.children('div.mcMergeTemplate').children('div:first-child').addClass('eventTextBox');
				_this.children('div.mcMergeTemplate').children('div:last-child').addClass('eventBtnBox');
				_this.children('div.mcMergeTemplate').children('div.eventTextBox').children('div').addClass('eventTextSection');
			} else {
				_this.find('div:nth-child(2)').addClass('eventTextBox');
				_this.find('div:nth-child(3)').addClass('eventBtnBox');
				_this.children('div').children('div').addClass('eventTextSection');
			}

		});
	}
    $('ul.nav > li').has('ul').addClass('dropdown');
    $('ul.nav > li').has('ul').children('a').addClass('dropdown-toggle');
    $('ul.nav > li > ul').addClass('dropdown-menu row-fluid');
    $('ul.nav > li > ul > li').addClass('span3 megaMenuSection');
    $('ul.nav > li > ul > li:first-child').addClass('xsHidden979');
    $('ul.nav > li > ul > li:first-child > div').addClass('heading text-center');
    $('ul.nav > li > ul > li > ul').addClass('mainMenu');
    $('li.megaMenuSection > ul').addClass('mainMenu');
    $('.nav li.dropdown').addClass('hoverMenu');

    addSearchSection();
    addmemberCenterSection();
    resizeMenuChange();
    $(window).resize(function() {
        resizeMenuChange()
    });

    $(".mainMenuOnclickBtn").click(function() {
        $(this).parents(".megaMenuSection").children('ul').children('li').children(".mainMenuOnclick").slideToggle();
    });

    $('.mainNavigationWrapper').show();
	
	$('header .dropdown-menu input[type=text]').blur(function() {
		$(this).parent().closest('.dropdown').removeClass('hoverMenu').addClass('hoverMenu');
	}).focus(function() {
		$(this).parent().closest('.dropdown').removeClass('hoverMenu');
	});
	
    if ($('.mainNavigationWrapper .mc_form_login').length && typeof $._data($('.mainNavigationWrapper .mc_form_login')[0], "events") == "undefined") {
        mcValidateLogins($('.mainNavigationWrapper'));
    }
    var zoneEObj = $('.zoneEwrap ul').html();
    if(zoneEObj != undefined){
		liCnt = $('.zoneEwrap ul > li').length;
        arrZoneE = $('.zoneEwrap ul > li');
        sIdx = Math.floor(Math.random() * liCnt); 
        if(liCnt > 1 && localStorage.prevIdx != undefined){          
            if(sIdx == localStorage.prevIdx){
                arrNewIdx = [];
                for(i = 0; i < liCnt; i++){
                    if(i != sIdx){
                        arrNewIdx.push(i);
                    }                    
                }
                sIdxNew = Math.floor(Math.random() * arrNewIdx.length); 
                sIdx = arrNewIdx[sIdxNew];
            }
        }
        
        $('.zoneEwrap ul > li').hide();
        $($('.zoneEwrap ul > li')[sIdx]).show() ;
        localStorage.prevIdx = sIdx;
    }
});

function addSearchSection(){
	var _searchSection = $('#searchSectionContentTop').html();
	var _searchSectionBottom = $('#searchSectionContentBottom').html();
	var _navObject = $('.mainNavigationWrapper > ul');
	_navObject.prepend(_searchSection).append(_searchSectionBottom);;
}
function addmemberCenterSection(){
	var _memberCenterSection = $('#memberCenterSectionContentTop').html();
	var _memberCenterSectionBottom = $('#memberCenterSectionContentBottom').html();
	var _navObject = $('.mainNavigationWrapper > ul');
	_navObject.prepend(_memberCenterSection).append(_memberCenterSectionBottom);
}

function resizeMenuChange() {
	if ($(window).width() < 768) {

        $('.innerEventBox').find('.eventimgText');

        $('.eventimgText').append("<span class='menu-arrow'></span>");

        $(".eventimgText").children(".menu-arrow").click(function() {
            $(this).toggleClass('openBox');
            $(this).parents(".eventBoxTop").parents(".eventBoxFrame").children(".eventBoxBottom").slideToggle();
        });
    }
	if ($(window).width() < 980) {
		$(".btn-navbar").click(function() {
			$("body").toggleClass("overlay");
		});

		$(".dropdown").append("<span class='menu-arrow'></span>");
		
		$(".menu-arrow").click(function() {
			$(this).parents(".dropdown").toggleClass('open-droupdown');
			$(this).parents(".dropdown").children(".dropdown-menu").slideToggle();
		});
		
		$(".btn-navbar").click(function() {
			$(".dropdown").removeClass('open-droupdown');
			$(".dropdown-menu").hide();
		});

		$(".mainMenuMobBtn").click(function() {
			$(this).toggleClass('textUnderline');
			$(this).parents(".megaMenuSection").toggleClass('closeBox');
			$(this).parents(".megaMenuSection").children(".mainMenuMob").slideToggle();
			$(this).parents(".megaMenuSection").children(".mainMenuOnclickBtn").slideToggle();
			$(this).parents(".megaMenuSection").children(".mainMenuOnclickBtn").toggleClass('openBoxInner');
		});

	}
	
}


function randomizeInitialSlideCustom(owlSelector, zoneName){
    var _children = $(owlSelector.target).children();
    var slideCount = _children.length;	
    var maxIndex = slideCount-1;	
    var randomizedSlides = [];
    if(maxIndex > 0){
        var startIndex = getNewStartIndexCustom(maxIndex, zoneName);
        for (var i = startIndex; i <= maxIndex; i++) { 
            randomizedSlides.push(_children[i]);
        }
        for (var i = 0; i < startIndex; i++) { 
            randomizedSlides.push(_children[i]);
        }
        $.each( randomizedSlides, function( index, value ) {
            $(value).appendTo($(owlSelector.target));
        });
    }		
}

function getNewStartIndexCustom(maxIndex, zoneName){
    var startIndex = randomNumberFromRangeCustom(0, maxIndex);
    var startIndexKey = 'startIndex' + zoneName;
    if(localStorage[startIndexKey] != undefined ){
        if(localStorage[startIndexKey] != startIndex)
            localStorage[startIndexKey] = startIndex;
        else {
            localStorage[startIndexKey] = (startIndex + 1 > maxIndex) ? startIndex - 1 : startIndex + 1;
        }
    } else {
        localStorage[startIndexKey] = startIndex;
    }
    return localStorage[startIndexKey];
}

function randomNumberFromRangeCustom(min,max)	{
    return Math.floor(Math.random()*(max-min+1)+min);
}