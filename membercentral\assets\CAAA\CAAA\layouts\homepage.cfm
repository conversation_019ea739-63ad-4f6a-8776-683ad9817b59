<cfoutput>
	<cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "direct"><!--- DISPLAY THE FOLLOWING IN DIRECT MODE --->
		<cfinclude template="directMode.cfm" />
	<cfelse>
		<!DOCTYPE html>
		<html xmlns="//www.w3.org/1999/xhtml">
			<head>
				<cfinclude template="head.cfm">
				<link rel="stylesheet" href="/css/homepage.css">
			</head>
			<body class="shifter shifter-left offcanvas-menu-left offcanvas-menu-white mobile-header-style2 sticky-header">
				<!-- /preloader-container -->
				<cfinclude template="header.cfm">
			    <div class="main-wrapper shifter-page">
			        <!-- Start main-header -->
			        <div class="main-header style2 fixed-header">
			            <div class="main-header-inner">
			                <div class="main-bar style2 padding-15 white-bg">
			                    <div class="container-fluid">
			                        <div class="row-fluid">
			                            <div class="logo-container">
			                                #application.objCMS.renderZone(zone='I',event=event, mode='div')#
			                            </div><!-- /logo-container -->
			                            <div class="menu-container clearfix nav-collapse collapse">
			                                <div class="main-nav active-style1 style1" id="main-nav" style="top: 20px;">
												<cfif structKeyExists(local.strMenus,"primaryNav")>
													#local.strMenus.primaryNav.menuHTML.rawcontent#
												</cfif>

												<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
													<ul class="nav">
														<li>
															<a class="menu-item color_link" href="/?pg=login" class="">Login</a>
														</li>
													</ul>
												<cfelse>
													<ul class="nav">
														<li>
															<a class="menu-item" href="/?logout" class="">Logout</a>
														</li>
													</ul>
												</cfif>

			                                     <ul class="clearfix serach-icon1">
			                                        <li class="icon icon-search dropdown">
			                                        	<cfsavecontent variable="local.zoneJ">
															#application.objCMS.renderZone(zone='J',event=event, mode='div')#
														</cfsavecontent>
														<cfset local.zoneJ = replaceNoCase(local.zoneJ, '<div id="zoneJ" class="zonewrapper">', "", "All")>
														<cfif structKeyExists(event.getValue("mc_pageDefinition").pageZones,"J")>
															<cfloop array="#event.getValue("mc_pageDefinition").pageZones['J']#" index="local.thisItem">							
															    <cfset local.zoneJ = replaceNoCase(local.zoneJ, '<div id="J#local.thisItem.resourceNodeAttributes.siteResourceID#" class="zoneresource">', "", "All")>
																<cfset local.zoneJ = replaceNoCase(local.zoneJ, "<p>", "", "All")>
																<cfset local.zoneJ = replaceNoCase(local.zoneJ, "</p>", "", "All")>
																<cfset local.zoneJ = replaceNoCase(local.zoneJ, "</div>", "", "All")>
															</cfloop>	
														</cfif>   
						            					#local.zoneJ#
			                                            <div class="sub-menu dropdown-menu dropdown-search-form">
			                                                <form name="searchbox" id="searchbox" action="/?pg=search" method="post" class="search-form"> 
			                                                	<input name="s_a" id="s_a" type="hidden" value="doSearch" /> 
																<input name="s_frm" id="s_frm" type="hidden" value="1" />
			                                                    <input class="dark-bg" type="text" name="s_key_all" id="s_key_all" placeholder="Search">
			                                                    <input type="submit" value="&gt;">
			                                                </form>
			                                            </div><!-- /sub-menu -->
			                                        </li>
			                                    </ul>
			                                </div>
			                            </div><!-- /menu-container -->
			                        </div>
			                    </div><!-- /container -->
			                </div><!-- /main-bar -->
			            </div><!-- /main-header-inner -->
						<div class="title-barBF topBar">
							<div class="title-barBF-imageless"></div>
							
						</div>
			            <div class="title-barBF header-fullhome">
							<!-- banner Start -->
							<div class="banner">
								<div class="owl-carousel bannerSlider">
									<cfset local.zoneBContent = event.getValue("mc_pageDefinition").pageZones['B']>
									<cfset local.zoneAContent = event.getValue("mc_pageDefinition").pageZones['A']>
							
									<cfloop from="1" to="#arrayLen(local.zoneBContent)#" index="local.thisItem" >
										<cfset local.mainSliderImage = replaceNoCase(replaceNoCase(local.zoneBContent[local.thisItem].data,'<p>', "", "All"),'</p>', "", "All")/>
										<cfif lcase(trim(local.zoneBContent[local.thisItem].view)) EQ 'echo' AND len(trim(local.mainSliderImage))>
											<div class="item">
												<div class="carouselImg">
													#local.mainSliderImage#
												</div>
												<cfif application.objCMS.getZoneItemCount(zone='D',event=event) AND ArrayLen(local.zoneAContent) GTE local.thisItem  AND len(trim(local.zoneAContent[local.thisItem].data))>
													<div class="carouselCaption">
														#local.zoneAContent[local.thisItem].data#
													</div>
												</cfif>
											</div>
										</cfif>
									</cfloop>                            
								</div>    
							</div> 
							<div class="services-section">
								<div class="container">                                
									<span class="servicesWrapper" style="display:none">
										<cfif application.objCMS.getZoneItemCount(zone='E',event=event)>                          
											<cfset local.servicesContent = event.getValue("mc_pageDefinition").pageZones['E']>
											<cfloop from="1" to="#arrayLen(local.servicesContent)#" index="local.thisItem" >
												<cfset local.serviceContent = local.servicesContent[local.thisItem].data/>
												<cfif len(trim(local.serviceContent))>
													#local.serviceContent#
												</cfif>
											</cfloop>
										</cfif>
									</span>
									<span class="servicesHolder" style="display:none"></span>
								</div>
							</div>
			            </div><!-- /header-banner -->
						
			        </div>
					
			        <!-- Start main-contents -->
			        <div class="main-contents">
						<cfif application.objCMS.getZoneItemCount(zone='C',event=event)>
							<div class="container">
								<div class="row-fluid">
									<div class="span12 work-california">
										<div class="section-title section-title-style3 align-center">
											#application.objCMS.renderZone(zone='C',event=event, mode='div')#
										</div>
									</div>
								</div><!-- /row -->
							</div><!-- /contianer -->
						</cfif>
						
						<div class="main-contents">
							<div class="container">
								#application.objCMS.renderZone(zone='L',event=event, mode='div')#
							</div><!-- /container -->
							<div class="newslink-section wow fadeInDown">
								<div class="toggle white-bg">
									<h6 class="footer-copyright">
										#application.objCMS.renderZone(zone='F',event=event, mode='div')#
									</h6>
								</div>
							</div>
						</div>
						<cfif application.objCMS.getZoneItemCount(zone='S',event=event) and 
							application.objCMS.getZoneItemCount(zone='V',event=event) and
							application.objCMS.getZoneItemCount(zone='U',event=event)>
							<div class="container">
								<div class="portfolio-container portfolio-masonry columns3 home-mesonry">
									<div class="portfolio-item-wrapper portfolio-item-wrapper-style2 row-fluid">
										<div class="portfolio-item-container branding span4">
											<div class="portfolio-item">
											#application.objCMS.renderZone(zone='S',event=event, mode='div')#
											</div>
										</div>
										<!-- /span4 -->
										<div class="portfolio-item-container branding span4">
											<div class="portfolio-item">
											#application.objCMS.renderZone(zone='V',event=event, mode='div')#
											</div>
										</div>
										<!-- /span4 -->
										<div class="portfolio-item-container branding span4">
											<div class="portfolio-item">
											#application.objCMS.renderZone(zone='U',event=event, mode='div')#
											</div>
										</div>
										<!-- /span4 --> 
									</div>
									<!-- /row --> 
								</div>
								<!-- /portfolio-item-wrapper --> 
							</div><!-- /container -->
						</cfif>
			        </div><!-- Who, What, How section -->
			        <div class="main-contents margin-bottom30 padding-50" style="background-color:##f8f8ff;">
			            <div class="container">
			             	#application.objCMS.renderZone(zone='D',event=event, mode='div')#
			             	<div class="row-fluid align-center">
						        <div class="span12 our-story"> 
						        	#application.objCMS.renderZone(zone='X',event=event, mode='div')#
						        	 </div>
						      </div>
			             	
			            </div><!-- /contianer -->
			        </div><!-- Join our discussion section -->
			        <div class="main-contents margin-bottom30 padding-50" style="background-color:##f8f8ff;">
			            <div class="container">
			               #application.objCMS.renderZone(zone='G',event=event, mode='div')#
			            </div><!-- /contianer -->
			        </div><!-- The purpose section section -->
			        <div class="main-contents padding-50">
			            <div class="container">
			                <div class="row-fluid align-center">
			                    <div class="donate-box span12">
			                        <div class="row-fluid">
			                           #application.objCMS.renderZone(zone='H',event=event, mode='div')#
			                        </div>
			                    </div>
			                </div><!-- /row -->
			            </div><!-- /contianer -->
			        </div><!-- Donate section -->
			        <!-- End main-contents -->
			        <!-- Start main-footer -->
			        <cfinclude template="footer.cfm">
			        <!-- End main-footer -->
			    </div>
			     <cfinclude template="toolBar.cfm">
			</body>
		</html>
	</cfif>
	<style>
	##zoneT p {margin-bottom: 0px;}
	</style>
</cfoutput>