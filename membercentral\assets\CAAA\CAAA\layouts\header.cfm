<cfoutput>
	<style>
		.flexParent {
			padding: 0;
			margin: 0;
			list-style: none;
			display: -webkit-box;
			display: -moz-box;
			display: -ms-flexbox;
			display: -webkit-flex;
			display: flex;

			justify-content: space-between;
			align-items: baseline;
		}
		.flexChild {
			min-height: 10px;
			max-height: 200px;
		}
		##mobileNavButton {
			margin: 0 0 0 12px;
			padding: 0;
			display: inline;
			left: auto;
			position: relative;
			top: 8px;
			float: none;
			-webkit-transform: none;
			transform: none;
		}
		##logoCanvas {
			margin: 0;
			padding: 0;
			width: 220px;
		}
		@media (max-width: 480px) {
			a.search-form-trigger {
				position: relative!important;
				right: 12px!important;
				top: 2px!important;
			}
		}
		.main-header .nav > li:hover > ul, .sticky-main-nav .nav > li:hover > ul {
			top: 70px !important;
		}
		.pageTitle{
			text-align:center;
		}
		.eventImages img{
			max-width:60%!important
		}
	</style>	
	<div class="mobile-header-wrapper style2 shifter-header white-bg clearfix hidden-md hidden-lg">
		<cfset local.zoneT = "">
		<cfif application.objCMS.getZoneItemCount(zone='T',event=event)>
			<cfsavecontent variable="local.zoneT">
				#application.objCMS.renderZone(zone='T',event=event, mode='div')#
			</cfsavecontent>
			<cfset local.zoneT = replaceNoCase(local.zoneT, '<div id="zoneT" class="zonewrapper">', "", "All")>
			<cfif structKeyExists(event.getValue("mc_pageDefinition").pageZones,"T")>
				<cfloop array="#event.getValue("mc_pageDefinition").pageZones['T']#" index="local.thisItem">							
					<cfset local.zoneT = replaceNoCase(local.zoneT, '<div id="T#local.thisItem.resourceNodeAttributes.siteResourceID#" class="zoneresource">', "", "All")>
					<cfset local.zoneT = replaceNoCase(local.zoneT, "<p>", "", "All")>
					<cfset local.zoneT = replaceNoCase(local.zoneT, "</p>", "", "All")>
					<cfset local.zoneT = replaceNoCase(local.zoneT, "</div>", "", "All")>
				</cfloop>	
				<cfset local.zoneT = replaceNoCase(local.zoneT, "</div>", "", "All")>
			</cfif>
		</cfif>
		<div class="flexParent">
			<div class="flexChild" style="width: 12%;">
		        <div id="mobileNavButton" class="shifter-handle style1">
					<a href="##" class="bars">
						<span></span>
						<span></span>
						<span></span>
					</a><!-- /bars -->
				</div>
			</div>
			<cfif application.objCMS.getZoneItemCount(zone='I',event=event)>
				<div class="flexChild">
					<div id="logoCanvas" class="logo-container small-logo">
						#application.objCMS.renderZone(zone='I',event=event, mode='div')#
					</div>
				</div>
			</cfif>
			<div class="flexChild" style="width: 15%; text-align: right;">
				#local.zoneT#
			</div>
		</div>

        <div class="search-form-wrapper">
			<form name="searchbox" id="searchbox" action="/?pg=search" method="post" class="search-form"> 
				<input name="s_a" id="s_a" value="doSearch" type="hidden"> 
				<input name="s_frm" id="s_frm" value="1" type="hidden">
				<input class="dark-bg" name="s_key_all" id="s_key_all" placeholder="Type &amp; hit enter" type="search">
			</form>    
            <span class="search-form-close-trigger">
                <a href="##"><i class="icon icon-knight-521"></i></a>
            </span> 		        
        </div><!-- /search-form-wrapper -->
    </div> <!--- // div class="mobile-header-wrapper style2 shifter-header white-bg clearfix hidden-md hidden-lg" --->
    
     <div class="main-nav offcanvas-menu mobile-nav shifter-navigation fullwidth-items fullwidth-menu white-bg">
        <form action="" class="search-form">
            <input type="search" name="mobile-search-form" id="mobile-search-form" placeholder="Search">
            <input type="submit" value="&gt;">
        </form>
    </div>
    <script type="text/javascript">
	    $(document).ready(function(){
			$( ".main-nav .dropdown-toggle" ).hover(function() {
				$(".main-nav .dropdown-search-form").css({'opacity' :'1','visibility':'visible'});
			}, function() {
				$(".main-nav .dropdown-search-form").css({'opacity' :'0','visibility':'hidden'});
			});

			$( ".main-nav .dropdown-search-form" ).hover(function() {
				$(".main-nav .dropdown-search-form").css({'opacity' :'1','visibility':'visible'});
			}, function() {
				$(".main-nav .dropdown-search-form").css({'opacity' :'0','visibility':'hidden'});
			});

			$(window).scroll(function() {
				if ($(this).scrollTop() > 200) {
					$('div##logInLogOutLink').fadeOut(125);
				} else {
					$('div##logInLogOutLink').fadeIn(125);
				}
			});
		});
		
    </script>
</cfoutput>