/*! Generated by <PERSON>ont Squirrel (https://www.fontsquirrel.com) on November 19, 2018 */



@font-face {
    font-family: 'museo700';
    src: url('fonts/museo_700-webfont.woff2') format('woff2'),
         url('fonts/museo_700-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'museo900';
    src: url('fonts/museo_900-webfont.woff2') format('woff2'),
         url('fonts/museo_900-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'museo500';
    src: url('fonts/museo_500-webfont.woff2') format('woff2'),
         url('fonts/museo_500-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}




@font-face {
    font-family: 'museo100';
    src: url('fonts/museo_100-webfont.woff2') format('woff2'),
         url('fonts/museo_100-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}
@font-face {
    font-family: 'museo300';
    src: url('fonts/museo_300-webfont.woff2') format('woff2'),
         url('fonts/museo_300-webfont.woff') format('woff');
    font-weight: normal;
    font-style: normal;

}