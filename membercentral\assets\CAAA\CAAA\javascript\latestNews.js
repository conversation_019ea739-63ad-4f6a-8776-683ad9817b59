$(document).ready(function() {
 $('div.Date').each(function(index, dateElement) {
  var $dateElement = $(dateElement);
  $dateElement.text(moment($dateElement.text()).format('MMM D, YYYY'));
 });

});
 
$( function() {
	callIsotope();
});

$(window).resize(function(){
	callIsotope();
});

function callIsotope(){
	var $container =  $('div.mcMergeTemplate');
	$container.imagesLoaded( function() {
		$container.isotope({
			itemSelector: 'article.latestNews',
			// masonry is default layoutMode
		});
	});	
}