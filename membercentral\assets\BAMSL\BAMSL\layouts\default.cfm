<cfoutput>
<!doctype html>
<html>
	<head>
		<cfinclude template="head.cfm">
	</head>
	<body>
		<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
		<div class="wrapper">
		
			
				<cfinclude template="header.cfm">
				
				<div class="interesGroupBox pd_60">
					<div class="container">
						<div class="row-fluid">
							<div class="interesGroupFrame">
								<cfif len(event.getValue('mc_pageDefinition.sectionName','')) AND event.getValue('mc_pageDefinition.sectionName','') NEQ "Root">
									<p class="HeaderTextSmall">#event.getValue('mc_pageDefinition.sectionName','')#</p>
								</cfif>
								<p class="TitleText">#event.getValue('mc_pageDefinition.pagetitle','')#</p>
							</div>
						</div>
					</div>
				</div>
			
				<div class="eventBox innerEventBox innerSimpleEventBox">
					<div class="container">
						<div class="row-fluid">
							
							<div class="mainContent<cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "full"> mainContentFull</cfif>">
								<div class="contentdivFrame">
									<div class="firstContentBox">
										<cfif application.objCMS.getZoneItemCount(zone='Main',event=event)>
											#application.objCMS.renderZone(zone='Main',event=event)#
										</cfif>
									</div>								
								</div>
							</div>
							
							<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "full">
								<div class="sidebar">
									<cfif application.objCMS.getZoneItemCount(zone='R',event=event)>
										<cfset local.sponsorContent = event.getValue("mc_pageDefinition").pageZones['R']>
										<div class="text-center xsHidden767 rightSponsorsWrapper"> 
											<div class="hide" id="rightSponsorsContent">
												#local.sponsorContent[1].data#
											</div>
											<div class="rightSponsorsSection"></div>
										</div>
									</cfif>
									<div class="span12 innerEventDiv clearfix">
										<div class="eventBoxFrame">
											<cfif application.objCMS.getZoneItemCount(zone='I',event=event)>
												<cfset local.upcomingEventsHeaderContent = event.getValue("mc_pageDefinition").pageZones['I']>
												<div class="eventBoxTop">
													#local.upcomingEventsHeaderContent[1].data#
												</div>
											</cfif>
											<cfif application.objCMS.getZoneItemCount(zone='F',event=event)>
												<cfset local.upcomingEventsContent = event.getValue("mc_pageDefinition").pageZones['F']>
												<div class="eventBoxBottom hasMergeCode">
													#replace(local.upcomingEventsContent[1].data,'<p></p>','')#						
												</div>
											</cfif>
										</div>
									</div>
									<div class="span12 innerEventDiv">
										<div class="eventBoxFrame">
											<cfif application.objCMS.getZoneItemCount(zone='J',event=event)>
												<cfset local.recentNewsHeaderContent = event.getValue("mc_pageDefinition").pageZones['J']>
												<div class="eventBoxTop">
													#local.recentNewsHeaderContent[1].data#
												</div>
											</cfif>
											<cfif application.objCMS.getZoneItemCount(zone='G',event=event)>
												<cfset local.recentNewsContent = event.getValue("mc_pageDefinition").pageZones['G']>
												<div class="eventBoxBottom">
													#local.recentNewsContent[1].data#
												</div>
											</cfif>
										</div>
									</div>
								</div>
							</cfif>
						</div>
					</div>
				</div>
			
				<cfinclude template="footer.cfm">
			
		</div>
			
			<cfelse>
				
				<div style="padding: 20px;">
					<div class="contentBox">
						<cfif application.objCMS.getZoneItemCount(zone='Main',event=event)>
							#application.objCMS.renderZone(zone='Main',event=event)#
						</cfif>
					</div>
				</div>
			</cfif>
	</body>
</html>
</cfoutput>