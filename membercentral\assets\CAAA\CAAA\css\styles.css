/*--- Icon fix ---*/
[class^="icon-"], [class*=" icon-"]{
  background:none!important;
}

a[href^="tel:"] {
  color: inherit;
  text-decoration: none;
}


/* CSS RESET ELEMENTS --------------------------------------------------------------------------------------*/
body { 
	margin: 0;
	background: #f0f4fe;
	font-family: 'Open Sans', sans-serif; 
	font-size: 14px; 
	color: #65686d;
}

#loginExtraContent { padding-top:10%;  }
#mainLoginContent { border-right:1px solid black; margin-bottom:20px; }

.innerWrapper { max-width:1090px; margin-left:auto;margin-right:auto; }


#homepageCarousel { margin-top:0px; border-bottom:10px solid #B52814; }
#homepageCarousel .item { background:#fff; max-height:470px!important; overflow:hidden;}
#homepageCarousel .item p { margin-bottom:0px!important; }
#homepageCarousel .container-fluid { padding:0; }

#homepageCarousel .carousel-indicators { bottom:15px; left:48%; color:#65686d!important; margin: auto auto; }
#homepageCarousel .carousel-indicators li { float: left;width: 13px;height: 13px;margin: 0 3px;background: rgba(255,255,255,0.5);border-radius: 50%;cursor: pointer;}
#homepageCarousel .carousel-indicators .active { background-color:#B52814!important; bottom:15px; left:90%;}
#homepageCarousel .item > .container-fluid > .span4 {  padding-right:20px; }


#homepageCarousel2 { margin-top:0px; min-height:250px!important;}
#homepageCarousel2 .item { background:#fff;min-height:250px!important; padding:15px;}
#homepageCarousel2 .carousel-inner { border:0px; box-shadow: 0 0 2px rgba(0,0,0,0.2);}
#homepageCarousel2 .container-fluid { padding:10; }


#homepageCarousel2 .carousel-indicators { bottom:15px; left:48%; color:#65686d!important; margin: auto auto; }
#homepageCarousel2 .carousel-indicators li { float: left;width: 13px;height: 13px;margin: 0 3px;background: rgba(0,0,0,0.5);border-radius: 50%;cursor: pointer;}
#homepageCarousel2 .carousel-indicators .active { background-color:#B52814!important; bottom:15px; left:90%;}

/*----------------------------------------------------------------------------------------------------------*/
#thePage{ width:960px; margin:auto auto; position:relative; }

#headerTop { min-height: 42px; overflow: hidden; background: #2f3338 url(/images/page-head-bg.gif) repeat; box-shadow: 0 1px 2px rgba(0,0,0,0.2); line-height: 28px; font-size: 12px; color: #9b9fa2; }
#headerTop a { display: inline-block;text-decoration: none;  padding: 0 5px; font: 12px 'Open Sans'; font-weight:600; color: #c5cbcd; -webkit-transition: color 0.2s ease-in-out; transition: color 0.2s ease-in-out; } 
#headerTop a:hover { color: #00FF00;} 
#headerTop .top-bar-phone a:link { color: #F7C407; }
#headerTop .top-bar-phone a:hover { color: #EEE; }
#headerTop .top-bar-nav a:first-child { margin-left:20px; }

a { text-decoration: none;color: #18297d;}


#headerTop a.login { color: #F7C407; } 
#headerTop a.login:hover { color: #eee; } 

#headerBottom {height:80px; background: #282828; border-bottom: 10px solid #B52814; line-height:80px;}

/*----------------------------------------------------------------------------------------------------------*/
#searchArea{ text-align:right; }
#searchArea form { margin-top:6px!important; margin-bottom:0px!important; }
input.field{ background-color:#fff; border:1px solid #ccc; color:#aaa; font-family:arial; font-size:.75em; line-height:120%; margin-top:0; padding:2px; width:200px; font-style:italic;}
button.searchButton{ vertical-align:middle; font-size:.75em; font-weight:bold; padding:2px; background:transparent!important; border:none;}
/*----------------------------------------------------------------------------------------------------------*/
#wrapper{ position:relative; }

/*----------------------------------------------------------------------------------------------------------*/
.leftInnerShadow { background-image:url(/assets/admin/images/lay_shadowInnerLeft.png); background-repeat:repeat-y; }
.rightInnerShadow { background-image:url(/assets/admin/images/lay_shadowInnerRight.png); background-repeat:repeat-y; }
/*----------------------------------------------------------------------------------------------------------*/
#infoBarArea { padding:4px; background-color:#ccc; border:0; overflow:auto; position:relative; }
#infoBar{ color:#424242; float:left; font-size:.75em; line-height:1.5em; width:70%; padding:2px 0 2px 0; position:relative; }
/*----------------------------------------------------------------------------------------------------------*/
#loginArea{ float:right; width:25%;  position:relative; text-align:right; }
/*----------------------------------------------------------------------------------------------------------*/
#mainNav{ background:url(/images/mainNav.png) repeat-x; height:48px; position:relative; z-index:999; }
#navButtons {margin-top:-1px;}
#navButtons a { color:#ffbc01; font-weight:bold;}
/* #navButtons td:hover,#navButtons a:hover{background:url(/images/navYellowHover.png) repeat-x; color:#222427;} */

#activeNav {background:url(/images/navYellowHover.png) repeat-x; height:50px; position:relative;}
#activeNav a { color:#222427 }

/*----------------------------------------------------------------------------------------------------------*/

#content{ background-color:#ffffff; padding-top:40px; padding-bottom:40px;}
#mainContent{ padding:10px;}
#mainContent p{  }
#mainContent ul {}
 
/*FOOTER: --------------------------------------------------------------------------------------------------*/
#footer { border-top: 10px solid #020202;}
#footer { background:#2c2c2c;  }
#footer ul { list-style:none;}

/*----------------------------------------------------------------------------------------------------------*/
.clear{ clear:both; }
/*----------------------------------------------------------------------------------------------------------*/
#loginBox{ border:1px solid #333; margin:auto auto; width:60%; margin-top:10px; margin-bottom:10px; }
#loginHeader{ background-color:#3a3a3a; color:#fff; font-size:1.1em; font-weight:bold; padding:5px; }
/*----------------------------------------------------------------------------------------------------------*/
/* TOOLBAR STYLES ------------------------------------------------------------------------------------------*/
#toolBarArea{ margin:15px 0 15px 0; }
#toolBar{ background:#fff; border-top:1px solid #000000; border-bottom:1px solid #000000; color:#355b8c; padding:5px 0 5px 0; }
#toolBar a{ color:#b4191a; font-size:1em; }
/*----------------------------------------------------------------------------------------------------------*/
#picBar {background:url(/images/picBarBlank.png) no-repeat; height:180px; width:665px;}
/*--TAB-SLIDER---------------------------------------------------------------------------------------------*/
.usual { color:#076598;background:#fff;}
.usual ul, .usual li { border:none; list-style:none; margin:0; padding:0; background:#fff;}
.usual li { float:left; background:#fff;}
.usual ul{ position:relative; overflow:auto; background:#fff;}
.usual ul a { display:block; padding:7px; text-decoration:none!important; font-size:11pt; color:#076598; background:#fff; margin-right:5px; border-top:1px solid #cacaca; border-right:1px solid #cacaca; border-left:1px solid #cacaca; }
.usual ul a.selected { color:#076598; cursor:default; background:#f3f3f3;}
.tabContent { background: #ececec; margin-top:-2px; overflow:hidden; }
.tabContent2 { padding:10px 10px 10px 10px;  background: #ececec; margin-top:-2px; overflow:hidden; }

.tabContent p{ margin:0; padding:0; overflow:auto;}
#tab1 hr,#tab1 p{ border:0; margin:0; padding:0;}
#tab1 hr{ color:#dce4eb; background-color:#dce4eb; height:1px; margin:5px 0 5px 0; }
#tab1 p{ margin-bottom:10px;}
.usual li.membersOnly{ float:right; }
.usual li.membersOnly a{ display:block; padding:4px 10px 0px 0; background-color:transparent; color:#990000; font-family:Georgia; font-weight:bold; }
.usual li.membersOnly a img{ vertical-align:middle; padding-right:3px; }

.quickLinks ul li{ list-style:url(/images/dot.gif); color:#3581a9; padding:0px; margin-left:-23px;}
.quickLinks a { color:#3581a9;}
.quickLinks { padding:5px;}


#mainContentTabs {padding: 0px 5px; margin-bottom:-5px;}

#eclips, #eclips a {color: #fff; font-size:1em;}
/*----------------------------------------------------------------------------------------------------------*/
.sponsorSlide { width:100%; text-align:left; min-height:130px;}
.sponsorSlide li { width:100%;  overflow:visible;}

.sponsorsList a { line-height: 1.5; text-decoration:none; font-size:16px; }
.sponsorsList a:hover {text-decoration:underline;}
.sponsorsList ul li {list-style:url(/images/dot.gif);}

#patronPlatinum {background:url(/images/patronPlatinum.png) no-repeat; vertical-align:top; padding-top:25px;}
/*----------------------------------------------------------------------------------------------------------*/
.sponsors{ position:relative; width:226px;  }
.sponsors p{ margin:0; padding:0; position:relative; }
.sponsors .top{ background:url(/images/sponsorsTop.png) no-repeat; height:5px; }
.sponsors .content{ background:url(/images/sponsorsBody.png) repeat-y; padding:0 10px; position:relative; }
.sponsors .TitleText{ background:url(/images/sponsorsTitle.png) no-repeat top left; color:#545454; display:block; font-family: Arial, Helvetica, sans-serif; font-size:11pt; font-weight:bold; margin:0 0 0 10px; padding:5px 0 0px 16px; position:relative; text-transform:none; }
.sponsors .BodyText{ }
.sponsors .sponsorImages{ border-bottom: 2px solid #666; border-top: 2px solid #666; display:block; height:64px; margin:0 15px; padding:8px 0; position:relative; text-align:center; }
.sponsors .sponsorText{ display:block; margin:0 20px; padding:8px 0; position:relative; }
.sponsors .sponsorText a{ background:url(/images/dot.gif) no-repeat center left; color:#076598; display:block; position:relative; font-family: Arial, Helvetica, sans-serif; font-size:12pt; padding:5px 0 5px 10px; }
.sponsors .button{ background:url(/images/sponsorsButton.png) no-repeat center; display:block; height:54px; position:relative; }
.sponsors .button a{ color:#545454; display:block; font-size:13pt; font-weight:bold; font-family: Arial, Helvetica, sans-serif; padding-top:15px; text-align:center; text-decoration:none; }
.sponsors .bottom{ background:url(/images/sponsorsBottom.png) no-repeat; height:5px; position:relative; }


/* left nav ------------------------------------------------------------------------------------------------*/
.leftNav{  }
/*----------------------------------------------------------------------------------------------------------*/
#print { vertical-align:top; padding-top:10px; padding-right:10px; margin-left:0px;}
/*----------------------------------------------------------------------------------------------------------*/
.widget_header {background-color:#ccc; color:#666; margin:5px 0px;}
/*----------------------------------------------------------------------------------------------------------*/

#thisSection { width:165px; margin:auto auto; margin-bottom:15px; margin-top:5px;}
#navITSHeader{ background: url(/images/navITS_header.png) no-repeat; height:28px; width:165px; }
#navITS ul{ list-style:none; margin:0; padding:0;  }
#navITS li{ background: url(/images/navITS.png) no-repeat; height:30px; display:block;}
#navITS a{ display:inline-block; padding:6px 0 0 15px; width:cc140px;  }
#navITS a:hover{ background: url(/images/navITS_over.png) no-repeat; height:30px; width:165px; }



#popLinks {background: url(/images/popLinks.png) no-repeat; height:156px; width:165px; padding-top:15px; margin:auto auto; }
#popLinks ul li {margin-left:-15px; line-height:2em; list-style:url(/images/dot.gif);}
#popLinks a:hover {text-decoration:underline;}

#social { padding-left:10px;}
#social a:hover {text-decoration:underline;}
/*--------------------------------------------------------------------------------------------------------*/






/* MY CAOC -----------------------------------------------------------------------------------------------*/
#MyCAAA{
  position:relative;
  font-family:arial;
  font-size:9pt;
  color:#333333;
  line-height:120%;
}

#MyCAAA a{font-family:arial; color:#333333; text-decoration:none; font-size:10pt; }
#MyCAAA a:hover{  text-decoration:underline; }


#MyCAAA .searchTitle{ font-weight:bold; color:#1b6476;}
#MyCAAA .section{  margin-bottom:15px; border:1px solid #666666; }
#MyCAAA .sectionTitle { font-size:11pt; font-weight:bold; color:#ffffff; padding:8px; background:#666666; border-bottom:1px solid #666666; }


#MyCAAA .sectionContent{ padding:10px 5px; }
#MyCAAA .subSectionArea1 { padding:5px; background-color:#cccccc; }
#MyCAAA .subSectionArea2 { padding:5px; background-color:#dbdedf; }
#MyCAAA .subSectionTitle { font-size:11pt; font-weight:bold; margin-bottom:10px; margin-left:5px;}
#MyCAAA .subSectionText { font-size:8.5pt;}

#MyCAAA .welcome{
color:#5a7d90;
font-size:18px;
font-weight:bold;
padding:0 5px 15px 5px;
position:relative;
}
#MyCAAA .userLinks a{ 
color:#333333;
font-weight:bold;

 }


#MyCAAA ul{ list-style:square; margin-top:0; margin-left:10px; padding-left:15px; margin-bottom:0; position:relative; overflow:auto; }
#MyCAAA li{ margin-bottom:5px; position:relative; overflow:visible; }
/*--------------------------------------------------------------------------------------------------------*/
#mycaaaContainer table{width:100%;border:1px solid #5a7d90;margin-bottom:10px;padding:0 0 0 0;}
#mycaaaContainer table ul{list-style:url(../images/bullet.jpg);}
#mycaaaContainer a{font-family:arial;font-size:12px;font-weight:normal;text-decoration:underline;color:#5a7d90;line-height:120%;}
span.mycaaaInsideHeader{color:#000;font-family:arial;font-size:14px;font-weight:bold;}
span.mycaaaBold{color:#000;font-family:arial;font-size:12px;font-weight:bold;}
#mycaaaLeftCol{position:relative;float:left;width:58%;margin:2px;}

#mycaaaRightCol{position:relative;float:right;width:38%;margin:2px;}



.narrow{padding: 0 8px 15px 15px;
position:relative;
}
.topblawgsfeeds div ul li {
    list-style-type: none;
    border-bottom: 1px dotted #666666;
    line-height: 1.8em;
    width: 100%;
}
.topblawgsfeeds div ul li a {
    color: #000000;
}
.topblawgsfeeds div ul li a:hover {
    color: #900;
}
.blogdescr { display: block; }
.blogdescr h4{
  padding: 0;
}
.topblawgsfeeds div h3 a {
    color: #000000; 
}
.topblawgsfeeds div h3 a:hover {
    color: #900;
}
.topblawgfeedline {
    overflow: hidden;
    height: 1.8em;
    display: block;
    z-index: 1;
    position: relative;
    width: 100%;
}
.topblawgfeedline h4 { font-weight: normal; font-size: 12px; }
.topblawgfeedline div.postdescr {
    display: none;
}

.topblawgfeedlinehover {
    z-index: 10;
    overflow:hidden;
    height:auto;
    display:block;
    position: absolute;
    background-color: #EFEFEF;
    width: 100%;

}
.topblawgfeedlinehover div.postdescr {
    display: none;
}

.topblawgsfeeds {
    margin-left: 2px;
    margin-top: 40px;
    clear: both;
}
.topblawgsfeeds .narrow {
    float: left;
/*
    width:276px;
    margin-right: 10px;
    margin-left: 7px;
*/
    width: 100%;
    height: 100%;
    background-image: none;
    padding-left: 10px;
    padding-right: 10px;
    padding-top: 10px;
}

.topblawgsfeeds .narrow h3 {
    font-size: 15px;
    line-height: 14px;
    padding-bottom: 7px;
}
.topblawgsfeeds .narrow h3 img {
    position: relative;
    top: 3px;
}

.odd {
    background-color: #FFFFFF;
}
.even {
    background-color: #FFFFFF;
}
.topblawgsfeeds div ul {
    margin-bottom: 15px;
}
.topblawgsfeeds div ul li span {
    float: right;
    margin-left: 5px;
    font-size: 12px;
}

.topblawgsfeeds .narrow .blogsubscribelink {
    float: right;
    position: relative;
    top: -2px;
}


#tooltip {
    position: absolute;
    z-index: 3000;
    border: 1px solid #111;
    background-color: #FFFFFF;
    padding: 13px;
    width: 375px;
    line-height: 1.4em;
}
#tooltip h4 {
    font-size: 1.2em;
}
#tooltip span {
    display: block;
    margin-top: 10px;
    color: #666666;
}
#tooltip .hlinkurl {
    color: #000099;
}
#tooltip h3, #tooltip div { margin: 0; }
.clr { clear: both; }

.top-bar-phone { position: relative; top: -1px; font-family: 'Open Sans'; color: #dfdfdf; margin-right:25px;}
.top-bar-phone span { color: #9b9b9b; } 
.top-bar-phone span:before { position: relative; top: 1px; margin-right: 8px; font: 16px 'FontAwesome'; color: #60646a; }


.b-top-bar { height: 42px; overflow: hidden; background: #2f3338 url(/images/page-head-bg.gif) repeat; box-shadow: 0 1px 2px rgba(0,0,0,0.2); line-height: 41px; font-size: 12px; color: #9b9fa2; }
.b-top-bar:after { content: ''; display: block; clear: both; }
.b-top-bar .wrap-left { float: left; text-align: left; }
.b-top-bar .wrap-right { text-align: right; }

.content { border-top: 1px solid #e4e4e4;}
.content.gray-content { background: #fafafa;background: -webkit-gradient(linear, left top, left bottom, color-stop(2%,#fcfcfc), color-stop(100%,#f9f9f9));background: -webkit-linear-gradient(top,  #fcfcfc 2%,#f9f9f9 100%);background: linear-gradient(to bottom,  #fcfcfc 2%,#f9f9f9 100%);}
.content.gray-content .layout { padding: 35px 40px;}
.content .layout { padding: 40px;}

.b-titlebar { padding: 0;border-top: 1px solid #e4e4e4;background: #fff;}
.b-titlebar h1 { margin-bottom: 0;line-height: 54px;white-space: nowrap;color: #313538;font-size: 20px;font-family: 'Open Sans';}
.b-titlebar.m-dark { position: relative;overflow: hidden;background: #2f3338 url(../img/page-head-bg.gif) repeat;box-shadow: 0 2px 3px rgba(0,0,0,0.3) inset;border-top: none;}
.b-titlebar.m-dark .layout { position: relative;padding: 35px 0 35px;}
.b-titlebar.m-dark .img-wrap { position: absolute;top: 0;		left: -9999px;bottom: 0;	right: -9999px;padding: 0;}
.b-titlebar.m-dark .img-wrap img { display: block;height: 100%;margin: 0 auto;}
.b-titlebar.m-dark h1 { margin-top: 0;white-space: normal;text-align: center;font: 40px/1 'Open Sans';text-transform: uppercase;font-weight: normal;color: #fff;}
.b-titlebar.m-dark p { margin-top: 9px;margin-bottom: 1px;text-align: center;font-size: 14px;color: #d2d6d9;}


.crumbs { float: right;margin: 0;padding: 0;list-style: none;font-size: 11px;color: #c4c4c4;}
.crumbs li { display: inline-block;line-height: 54px;}
.crumbs li:after { content: "\f054"; display: inline-block;position: relative;top: -1px;padding: 0 0 0 3px;font: 6px/54px 'FontAwesome';}
.crumbs li:first-child:after, .crumbs li:last-child:after {display: none;}
.crumbs a { padding: 0 4px;text-decoration: none;color: #c4c4c4;-webkit-transition: color 0.2s ease;transition: color 0.2s ease;}
.crumbs a:hover { color: #ee4a37;}

.b-copyright {
overflow: hidden;
padding: 0 30px;
background: #2f3338 url(/images/page-head-bg2.gif) repeat;
font-size: 14px;
line-height: 64px;
}

.copy {
float: left;
font-size: 12px;
color: #eee;

}
.copy a {
color: #b8bdc0;
-webkit-transition: all 0.2s ease;
transition: all 0.2s ease;
}

.b-social { margin: 0 0 10px; padding: 0; list-style: none; font-size: 0; }
.b-social li { display: inline-block; margin: 0 5px 5px 0; vertical-align: middle; }

.b-social { margin: 0;}
.b-social a { width: 18px;height: 18px;background: none;border: none;text-align: center;line-height: 18px;font-size: 16px;color: #5c5f63;cursor: pointer;text-shadow: 0 1px 1px rgba(0,0,0,0.1);}
.b-social a.tw { font-size: 15px;}
.b-social li { margin: 0 12px 0 0;font-size: 12px; color: #eee; /*color: #64696d;*/ }
.b-social a.fb:hover { color: #576ec7;text-shadow: 0 0 12px #3c4978;}
.b-social a.tw:hover { color: #5db4f0;text-shadow: 0 0 12px #3f6c8d;}
.b-social a.pt:hover { color: #c8222f;text-shadow: 0 0 12px #74232c;}
.b-social a.lin:hover { color: #007bb6;text-shadow: 0 0 12px #105070;}
.b-social a.gl:hover { color: #ba3526;text-shadow: 0 0 12px #6d2d27;}
.b-social a.dr:hover { color: #e24d87;text-shadow: 0 0 12px #813958;}
.b-social a.tl:hover { color: #35506b;text-shadow: 0 0 12px #2b3a4a;}
.b-social a.vk:hover { color: #45688e;text-shadow: 0 0 12px #33465c;}
.b-social a.dx:hover { color: #0073ce;text-shadow: 0 0 12px #104c7c;}
.b-social a.fs:hover { color: #219bce;text-shadow: 0 0 12px #21607c;}
.b-social a.is:hover { color: #c07145;text-shadow: 0 0 12px #704b37;}
.b-social a.mx:hover,  .b-social a.rss:hover { color: #fa9200;text-shadow: 0 0 12px #8d5b14;}
.b-social a.sk:hover { color: #29aae1;text-shadow: 0 0 12px #256785;}
.b-social a.yt:hover {color: #ed3639;text-shadow: 0 0 12px #872d31;}
.b-social a.gh:hover { color: #a0a3a8;text-shadow: 0 0 12px #60646a;}
.b-social { float: right; }

#headerTop .top-bar-social  { display: inline-block !important; float:right; } 
#headerTop .top-bar-social a { display: inline-block; width: 28px;height: 28px; text-align: center; font-size: 16px; line-height: 40px; color: #60646a; text-shadow: 0 1px 1px rgba(0,0,0,0.1); -webkit-transition: color 0.2s ease-in-out; transition: color 0.2s ease-in-out; padding:0px;}
#headerTop .top-bar-social a.tw { font-size: 15px; }
#headerTop .top-bar-social a.fb:hover { color: #576ec7; text-shadow: 0 0 12px #3c4978; }
#headerTop .top-bar-social a.tw:hover { color: #5db4f0; text-shadow: 0 0 12px #3f6c8d; }
#headerTop .top-bar-social a.pt:hover { color: #c8222f; text-shadow: 0 0 12px #74232c; }
#headerTop .top-bar-social a.lin:hover { color: #007bb6; text-shadow: 0 0 12px #105070; }
#headerTop .top-bar-social a.gl:hover { color: #ba3526; text-shadow: 0 0 12px #6d2d27; }
#headerTop .top-bar-social a.dr:hover { color: #e24d87; text-shadow: 0 0 12px #813958; }
#headerTop .top-bar-social a.tl:hover { color: #35506b; text-shadow: 0 0 12px #2b3a4a; }
#headerTop .top-bar-social a.vk:hover { color: #45688e; text-shadow: 0 0 12px #33465c; }
#headerTop .top-bar-social a.dx:hover { color: #0073ce; text-shadow: 0 0 12px #104c7c; }
#headerTop .top-bar-social a.fs:hover { color: #219bce;  text-shadow: 0 0 12px #21607c; }
#headerTop .top-bar-social a.is:hover { color: #c07145; text-shadow: 0 0 12px #704b37; }
#headerTop .top-bar-social a.mx:hover, .top-bar-social a.rss:hover { color: #fa9200; text-shadow: 0 0 12px #8d5b14; } 
#headerTop .top-bar-social a.sk:hover { color: #29aae1; text-shadow: 0 0 12px #256785; }
#headerTop .top-bar-social a.yt:hover { color: #ed3639; text-shadow: 0 0 12px #872d31; }
#headerTop .top-bar-social a.gh:hover { color: #a0a3a8; text-shadow: 0 0 12px #60646a; }

.b-widgets { 
	padding: 35px 40px 25px;
	background: #2C2C2C;
	color: #fff;
}
.b-widgets h1, .b-widgets h2, .b-widgets h3, .b-widgets h4, .b-widgets h5, .b-widgets h6 { font-family:'Open Sans',Arial,sans-serif; color:#fff; /*color: #343434;*/ }
.b-widgets h3 { margin-bottom: 15px; font-size: 16px; font-family: 'Open Sans'; text-transform: uppercase;	font-weight:bold}
.b-widgets .b-list { margin-bottom: 30px;}
.b-widgets .b-list i { font-size: 15px;color: #d3d3d3;}
.b-widgets h5 { color: #303030; }

.b-list { position: relative; margin: 0 0 20px 0; padding: 0; list-style: none; line-height: 20px; }
.b-list li{ margin-bottom: 2px; }
.b-list li span { display: block;overflow: hidden; }
.b-list li i { width: 16px; float: left; margin-right: 10px; text-align: center; color: #444; font-size: 14px; }

.row { display: table;margin: 0 -12px;letter-spacing: -.31em;word-spacing: -.43em;font-size: 0px;}
.row.m-block { display: block;}
.row-item { display: inline-block;margin: 0 12px;vertical-align: top;	text-align: left;font-size: 14px;letter-spacing: normal;word-spacing: normal;}

.sidebar section { text-align:center; padding: 8px; box-shadow: 0px 2px 4px 1px #dadacf; margin: 10px 0; }
.sidebar section h3 { font-size: 18px; color:#B52814; }
#continuingEducation { margin-top:95px; margin-right:10px; }

.sidebar section.b-side-nav { text-align:left; padding:0px!important; }
.printlink{float: right; padding: 0;padding: 7.5px 16px; border: 1px solid #e2e2e;color:#fff;background: #b42418;cursor: pointer;}
a:hover,a:focus{color: #fff !important;}
.b-side-nav { margin: 0 0 30px 0; padding: 0; box-shadow: 0 2px 3px rgba(0,0,0,0.02); list-style: none; background: #fff; background: -webkit-gradient(linear, left top, left bottom, color-stop(2%,#ffffff), color-stop(100%,#fbfbfb)); background: -webkit-linear-gradient(top,  #ffffff 2%,#fbfbfb 100%); background: linear-gradient(to bottom,  #ffffff 2%,#fbfbfb 100%); border-top: 1px solid #e2e2e2; }
.b-side-nav a { display: block; position: relative; padding: 7.5px 16px; border: 1px solid #e2e2e2; border-top: none;color: #65686d; -webkit-transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out; transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out; }
.b-side-nav a:hover { color: #e34735; } 
.b-side-nav a.active { margin-top: -1px; border-color: #e34735; background: #e34735 url(/images/sidenav-bg.png) repeat-x top; font-family: 'Open Sans'; color: #fff; }
.b-side-nav a:hover { color: #B52814; }
.b-side-nav a.active { border-color: #B52814; background: #B52814 url(../../img/sidenav-bg.png) repeat-x top; }
.b-side-nav a.active:hover { color: #fff; } 

.link-overlay { position: absolute;top: 0;       left: 0;bottom: 0;    right: 0;background: rgba(0,0,0,0);-webkit-transition: background-color 0.3s ease;transition: background-color 0.3s ease;}
.link-overlay:before { display: block;width: 46px;height: 46px;position: absolute;top: 50%;    left: 50%;margin: -22px 0 0 -25px;border-radius: 50%;background: #fff;text-align: center;font: 14px/46px 'FontAwesome';color: #2c2c2c;-webkit-transform: scale(0.8);transform: scale(0.8);opacity:0;-webkit-transition: -webkit-transform 0.3s ease, opacity 0.3s ease;transition: transform 0.3s ease, opacity 0.3s ease;}
.link-overlay:before { background: rgba(250,250,250,0.95); }

.btn.colored { background: #B52814 url(../../img/btn-bg.png) repeat-x top; border: 1px solid #B52820/*#26a4b1*/;  color: #fff; text-shadow: 0 -1px 0px #178e9d; }
.btn.colored:hover {  background: #777 url(../../img/btn-bg.png) repeat-x top; box-shadow: 0 1px 3px #d5eff1; border: 1px solid #178e9d;  color: #fff; text-shadow: 0 -1px 0px #178e9d; }


.full-shadow { border: 1px solid #ccc;box-shadow: 1px 1px 4px 0 rgba(180,180,180,0.3);}
.img-wrap { position: relative;clear: both;}
.img-wrap.shadow, .img-wrap .shadow { margin-bottom: 5px;}
.img-wrap img { display: block;box-shadow: 0 1px 1px rgba(0,0,0,0.1);}

img.no-shadow { box-shadow: none;}
.pretty-photo-item { display: block;position: relative;z-index: 1;}
.pretty-photo-item:hover .link-overlay { background: rgba(15,16,17,0.5); }
.pretty-photo-item:hover .link-overlay:before { -webkit-transform: scale(1);transform: scale(1);opacity:1;}
.img-wrap.m-left, img.m-left { float: left;margin: 0 25px 20px 0;}
.img-wrap.m-right, img.m-right { float: right;margin: 0 0 20px 25px;}
.img-wrap.m-center { display: block;margin: 0 0 16px; text-align: center;}
.img-wrap.m-center img { display: block; }

.img-wrap.m-center .shadow { display: inline-block;}
img.m-center { display: block;margin: 0 auto 20px;}
.img-title { position: absolute;bottom: 0;	left: 0;right: 0;z-index: 1;padding: 20% 20px 16px;background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(35,38,40,0)), color-stop(100%,rgba(35,38,40,0.6)));background: -webkit-linear-gradient(top,  rgba(35,38,40,0) 0%,rgba(35,38,40,0.6) 100%);background: linear-gradient(to bottom,  rgba(35,38,40,0) 0%,rgba(35,38,40,0.6) 100%);text-align: left;color: #ececec;text-shadow: 0 1px 1px rgba(0,0,0,0.1);}
.shadow, .shadow-2 { position: relative;margin-bottom: 43px;}
.shadow:after, .shadow-2:after { content: ''; display: block;position: absolute;}
.shadow:after { height: 43px;bottom: -46px;	left: 0;right: 0;background: url("/images/shadow.png") no-repeat scroll 0 0 / 100% auto transparent;}
.shadow-2:after { height: 55px;bottom: -55px;	left: 0;right: 0;background: url("/images/shadow-2.png") no-repeat scroll 0 0 / 100% auto transparent;}
.col-1_3 .shadow, .col-1_2 .shadow,.col-1_4 .shadow,.col-3_4 .shadow {margin-bottom: 0;}
.col-1_3 .shadow:after, .col-1_2 .shadow:after, .col-1_4 .shadow:after, .col-3_4 .shadow:after { height: 20px;bottom: -23px;}
.col-3_4 .shadow:after { height: 30px;bottom: -33px;}

.just-links.m-dark a:hover { color: #B52814; }
.just-links.m-yellow a { color: #F7C407; }
.just-links.m-yellow a:hover { color: #eee; }
