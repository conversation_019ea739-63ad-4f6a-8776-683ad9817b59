<cfsilent>
	<cfset qrySectionList = event.getValue('mc_pageDefinition.qrySectionTreeUp') />

	<cfquery dbtype="query" name="qrySections">
		SELECT
			*
		FROM
			qrySectionList
		ORDER BY
			DEPTH
	</cfquery>

	<cfset sectionCodeList = valueList(qrySections.sectionCode) />
</cfsilent>

<cfoutput>
<cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "direct"><!--- DISPLAY THE FOLLOWING IN DIRECT MODE --->
	<cfinclude template="directMode.cfm" />
<cfelse>
	<!DOCTYPE html>
	<html xmlns="//www.w3.org/1999/xhtml">

	<head>
		<cfinclude template="head.cfm">
	</head>

	<body class="shifter shifter-left offcanvas-menu-left offcanvas-menu-white mobile-header-style2 sticky-header">
		<cfinclude template="header.cfm">
		<div class="main-wrapper shifter-page">
			<div class="main-header style2 fixed-header">
				<div class="main-header-inner">
					<div class="main-bar style2 padding-15 white-bg">
						<div class="container-fluid">
							<div class="row-fluid">
								<div class="logo-container">
									#application.objCMS.renderZone(zone='I',event=event, mode='div')#
								</div><!-- /logo-container -->

								<div class="menu-container clearfix nav-collapse collapse">
									<div class="main-nav active-style1 style1" id="main-nav" style="margin-top: 30px;">
										<cfif structKeyExists(local.strMenus,"primaryNav")>
											#local.strMenus.primaryNav.menuHTML.rawcontent#
										</cfif>

										<ul class="clearfix nav serach-icon1">
											<li class="icon icon-search dropdown">
												<cfsavecontent variable="local.zoneJ">#application.objCMS.renderZone(zone='J',event=event, mode='div')#</cfsavecontent>
												<cfset local.zoneJ = replaceNoCase(local.zoneJ, '<div id="zoneJ" class="zonewrapper">', "", "All")>
												<cfif structKeyExists(event.getValue("mc_pageDefinition").pageZones,"J")>
													<cfloop array="#event.getValue("mc_pageDefinition").pageZones['J']#" index="local.thisItem">						
														<cfset local.zoneJ = replaceNoCase(local.zoneJ, '<div id="J#local.thisItem.resourceNodeAttributes.siteResourceID#" class="zoneresource">', "", "All")>
														<cfset local.zoneJ = replaceNoCase(local.zoneJ, "<p>", "", "All")>
														<cfset local.zoneJ = replaceNoCase(local.zoneJ, "</p>", "", "All")>
														<cfset local.zoneJ = replaceNoCase(local.zoneJ, "</div>", "", "All")>
													</cfloop>	
												</cfif>

												#local.zoneJ#

												<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
													<a class="menu-item color_link" href="/?logout" class="">Logout</a>
												</cfif>

												<div class="sub-menu dropdown-menu dropdown-search-form">
													<form name="searchbox" id="searchbox" action="/?pg=search" method="post" class="search-form"> 
														<input name="s_a" id="s_a" type="hidden" value="doSearch" /> 
														<input name="s_frm" id="s_frm" type="hidden" value="1" />
														<input class="dark-bg" type="text" name="s_key_all" id="s_key_all" placeholder="Search">
														<input type="submit" value="&gt;">
													</form>
												</div><!-- /sub-menu -->
											</li>
										</ul>
									</div>
								</div><!-- /menu-container -->
							</div>
						</div><!-- /container -->
					</div><!-- /main-bar -->
				</div><!-- /main-header-inner -->

				<div class="title-barBF ">
					<div class="title-barBF-imageless"></div>
					<div class="container">
						#application.objCMS.renderZone(zone='A',event=event, mode='div')#
					</div>
				</div>

				<div class="main-contents margin-top30">
					<div class="container">
						<div class="row-fluid padding-50 align-center">
							#application.objCMS.renderZone(zone='B',event=event, mode='div')#
						</div><!-- /row -->

						<div class="row">
							<div class="span6 abt-detail" style="border-right: 1px solid ##CCC;">
								#application.objCMS.renderZone(zone='Main',event=event, mode='div')#
							</div>
							<div class="span6 text-center" id="loginExtraContent">
								<cfif application.objCMS.getZoneItemCount(zone='U', event=event)>
									#application.objCMS.renderZone(zone='U',event=event, mode='div')#
								</cfif>
							</div>
						</div>
					</div><!-- /contianer -->
				</div>

				<cfinclude template="footer.cfm">
				<!-- End main-footer -->
			</div>
		<cfinclude template="toolBar.cfm">
	</body>
	</html>
</cfif>
</cfoutput>
