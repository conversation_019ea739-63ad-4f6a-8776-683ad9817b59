(function(e){"use strict";e.fn.twittie=function(){var t=arguments[0]instanceof Object?arguments[0]:{},n=typeof arguments[0]==="function"?arguments[0]:arguments[1];var r=e.extend({username:null,list:null,hashtag:null,count:10,hideReplies:false,dateFormat:"%b/%d/%Y",template:"{{date}} - {{tweet}}",apiPath:"php/twitter-feed/tweet.php",loadingText:"Loading..."},t);if(r.list&&!r.username){e.error("If you want to fetch tweets from a list, you must define the username of the list owner.")}var i=function(e){var t=e.replace(/(https?:\/\/([-\w\.]+)+(:\d+)?(\/([\w\/_\.]*(\?\S+)?)?)?)/ig,'<a href="$1" target="_blank" title="Visit this link">$1</a>').replace(/#([a-zA-Z0-9_]+)/g,'<a href="https://twitter.com/search?q=%23$1&src=hash" target="_blank" title="Search for #$1">#$1</a>').replace(/@([a-zA-Z0-9_]+)/g,'<a href="https://twitter.com/$1" target="_blank" title="$1 on Twitter">@$1</a>');return t};var s=function(e){var t=e.split(" ");e=new Date(Date.parse(t[1]+" "+t[2]+", "+t[5]+" "+t[3]+" UTC"));var n=["January","February","March","April","May","June","July","August","September","October","November","December"];var i={"%d":e.getDate(),"%m":e.getMonth()+1,"%b":n[e.getMonth()].substr(0,3),"%B":n[e.getMonth()],"%y":String(e.getFullYear()).slice(-2),"%Y":e.getFullYear()};var s=r.dateFormat;var o=r.dateFormat.match(/%[dmbByY]/g);for(var u=0,a=o.length;u<a;u++){s=s.replace(o[u],i[o[u]])}return s};var o=function(e){var t=r.template;var n=["date","tweet","avatar","url","retweeted","screen_name","user_name"];for(var i=0,s=n.length;i<s;i++){t=t.replace(new RegExp("{{"+n[i]+"}}","gi"),e[n[i]])}return t};this.html("<span>"+r.loadingText+"</span>");var u=this;e.getJSON(r.apiPath,{username:r.username,list:r.list,hashtag:r.hashtag,count:r.count,exclude_replies:r.hideReplies},function(e){u.find("span").fadeOut("fast",function(){u.html("<ul></ul>");for(var t=0;t<r.count;t++){var a=false;if(e[t]){a=e[t]}else if(e.statuses!==undefined&&e.statuses[t]){a=e.statuses[t]}else{break}var f={user_name:a.user.name,date:s(a.created_at),tweet:a.retweeted?i("RT @"+a.user.screen_name+": "+a.retweeted_status.text):i(a.text),avatar:'<img src="'+a.user.profile_image_url+'" />',url:"https://twitter.com/"+a.user.screen_name+"/status/"+a.id_str,retweeted:a.retweeted,screen_name:i("@"+a.user.screen_name)};u.find("ul").append("<li>"+o(f)+"</li>")}if(typeof n==="function"){n()}})})}})(jQuery)