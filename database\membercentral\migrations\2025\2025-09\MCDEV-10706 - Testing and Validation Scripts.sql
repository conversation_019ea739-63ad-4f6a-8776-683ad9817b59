-- MCDEV-10706: Testing and Validation Scripts
-- Use these scripts to test the migration in staging and validate results in production

USE membercentral
GO

PRINT '=== MCDEV-10706 Testing and Validation Scripts ===';
PRINT '';

-- =====================================================
-- PRE-MIGRATION VALIDATION QUERIES
-- =====================================================

PRINT '1. PRE-MIGRATION STATUS CHECK';
PRINT '==============================';

-- Current state of recurring events feature across all sites
SELECT 
    'Current Feature Status' AS CheckType,
    COUNT(*) AS TotalActiveSites,
    SUM(CASE WHEN sf.recurringEvents = 1 THEN 1 ELSE 0 END) AS SitesWithRecurringEvents,
    SUM(CASE WHEN sf.recurringEvents = 0 THEN 1 ELSE 0 END) AS SitesWithoutRecurringEvents,
    CAST(SUM(CASE WHEN sf.recurringEvents = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS decimal(5,2)) AS PercentageEnabled
FROM dbo.siteFeatures sf
INNER JOIN dbo.sites s ON s.siteID = sf.siteID
INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID 
    AND sr.siteResourceStatusID = 1;

-- Breakdown by AMS vs Non-AMS sites
SELECT 
    CASE WHEN sf.subscriptions = 1 THEN 'AMS Sites' ELSE 'Non-AMS Sites' END AS SiteType,
    COUNT(*) AS TotalSites,
    SUM(CASE WHEN sf.recurringEvents = 1 THEN 1 ELSE 0 END) AS WithRecurringEvents,
    SUM(CASE WHEN sf.recurringEvents = 0 THEN 1 ELSE 0 END) AS WithoutRecurringEvents,
    CAST(SUM(CASE WHEN sf.recurringEvents = 1 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS decimal(5,2)) AS PercentageEnabled
FROM dbo.siteFeatures sf
INNER JOIN dbo.sites s ON s.siteID = sf.siteID
INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID 
    AND sr.siteResourceStatusID = 1
GROUP BY sf.subscriptions
ORDER BY sf.subscriptions DESC;

-- Sample of sites that will be affected by the migration
PRINT '';
PRINT '2. SITES TO BE AFFECTED BY MIGRATION';
PRINT '====================================';

SELECT TOP 20 
    s.siteCode, 
    o.orgCode, 
    o.orgName,
    CASE WHEN sf.subscriptions = 1 THEN 'AMS' ELSE 'Non-AMS' END AS SiteType,
    sf.recurringEvents AS CurrentRecurringEventsStatus
FROM dbo.siteFeatures sf
INNER JOIN dbo.sites s ON s.siteID = sf.siteID
INNER JOIN dbo.organizations o ON o.orgID = s.orgID
INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID 
    AND sr.siteResourceStatusID = 1
WHERE sf.recurringEvents = 0
ORDER BY s.siteCode;

-- Check for any existing recurring events data
PRINT '';
PRINT '3. EXISTING RECURRING EVENTS DATA';
PRINT '=================================';

SELECT 
    'Existing Data Check' AS CheckType,
    (SELECT COUNT(*) FROM dbo.ev_recurringSeries) AS TotalRecurringSeries,
    (SELECT COUNT(*) FROM dbo.ev_events WHERE recurringSeriesID IS NOT NULL) AS EventsInRecurringSeries,
    (SELECT COUNT(DISTINCT siteID) FROM dbo.ev_recurringSeries) AS SitesWithRecurringData;

-- =====================================================
-- POST-MIGRATION VALIDATION QUERIES
-- =====================================================

PRINT '';
PRINT '4. POST-MIGRATION VALIDATION';
PRINT '============================';

-- Verify all sites have recurring events enabled
SELECT 
    'Post-Migration Status' AS CheckType,
    COUNT(*) AS TotalActiveSites,
    SUM(CASE WHEN sf.recurringEvents = 1 THEN 1 ELSE 0 END) AS SitesWithRecurringEvents,
    SUM(CASE WHEN sf.recurringEvents = 0 THEN 1 ELSE 0 END) AS SitesWithoutRecurringEvents,
    CASE 
        WHEN SUM(CASE WHEN sf.recurringEvents = 0 THEN 1 ELSE 0 END) = 0 
        THEN 'SUCCESS: All sites enabled' 
        ELSE 'WARNING: Some sites not enabled' 
    END AS ValidationResult
FROM dbo.siteFeatures sf
INNER JOIN dbo.sites s ON s.siteID = sf.siteID
INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID 
    AND sr.siteResourceStatusID = 1;

-- List any sites that still don't have recurring events enabled (should be empty after migration)
IF EXISTS (
    SELECT 1 FROM dbo.siteFeatures sf
    INNER JOIN dbo.sites s ON s.siteID = sf.siteID
    INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID 
        AND sr.siteResourceStatusID = 1
    WHERE sf.recurringEvents = 0
) BEGIN
    PRINT '';
    PRINT '5. SITES STILL WITHOUT RECURRING EVENTS (SHOULD BE EMPTY)';
    PRINT '=========================================================';
    
    SELECT 
        s.siteCode, 
        o.orgCode, 
        o.orgName,
        CASE WHEN sf.subscriptions = 1 THEN 'AMS' ELSE 'Non-AMS' END AS SiteType,
        sf.recurringEvents AS RecurringEventsStatus
    FROM dbo.siteFeatures sf
    INNER JOIN dbo.sites s ON s.siteID = sf.siteID
    INNER JOIN dbo.organizations o ON o.orgID = s.orgID
    INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID 
        AND sr.siteResourceStatusID = 1
    WHERE sf.recurringEvents = 0
    ORDER BY s.siteCode;
END
ELSE BEGIN
    PRINT '';
    PRINT '✓ SUCCESS: All active sites now have recurring events enabled.';
END

-- =====================================================
-- FUNCTIONAL TESTING QUERIES
-- =====================================================

PRINT '';
PRINT '6. FUNCTIONAL TESTING QUERIES';
PRINT '=============================';

-- Check that site_getSiteInfo returns the recurring events flag correctly
PRINT 'Testing site_getSiteInfo stored procedure...';

-- Sample a few sites to verify the flag is returned correctly
SELECT TOP 5 
    s.siteCode,
    o.orgCode,
    sf.recurringEvents AS DirectFlagValue,
    'Use EXEC site_getSiteInfo to verify sf_recurringEvents is returned correctly' AS TestNote
FROM dbo.siteFeatures sf
INNER JOIN dbo.sites s ON s.siteID = sf.siteID
INNER JOIN dbo.organizations o ON o.orgID = s.orgID
INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID 
    AND sr.siteResourceStatusID = 1
WHERE sf.recurringEvents = 1
ORDER BY s.siteCode;

-- =====================================================
-- ROLLBACK SCRIPT (IF NEEDED)
-- =====================================================

PRINT '';
PRINT '7. ROLLBACK SCRIPT (USE ONLY IF NEEDED)';
PRINT '======================================';
PRINT '-- Uncomment and run the following to rollback to AMS-only recurring events:';
PRINT '/*';
PRINT 'UPDATE dbo.siteFeatures ';
PRINT 'SET recurringEvents = 0 ';
PRINT 'WHERE siteID IN (';
PRINT '    SELECT sf.siteID ';
PRINT '    FROM dbo.siteFeatures sf ';
PRINT '    INNER JOIN dbo.sites s ON s.siteID = sf.siteID';
PRINT '    INNER JOIN dbo.cms_siteResources sr ON sr.siteResourceID = s.siteResourceID';
PRINT '        AND sr.siteResourceStatusID = 1';
PRINT '    WHERE sf.subscriptions = 0 AND sf.recurringEvents = 1';
PRINT ');';
PRINT '*/';

-- =====================================================
-- PERFORMANCE MONITORING QUERIES
-- =====================================================

PRINT '';
PRINT '8. PERFORMANCE MONITORING';
PRINT '=========================';

-- Monitor queue processing for recurring events
SELECT 
    'Queue Monitoring' AS CheckType,
    COUNT(*) AS TotalQueueItems,
    SUM(CASE WHEN queueTypeName = 'Events Import Queue' THEN 1 ELSE 0 END) AS EventsImportQueueItems,
    MAX(dateEntered) AS LatestQueueEntry
FROM platformQueue.dbo.tblQueueItems qi
INNER JOIN platformQueue.dbo.tblQueueTypes qt ON qt.queueTypeID = qi.queueTypeID
WHERE qi.dateEntered >= DATEADD(day, -1, GETDATE());

PRINT '';
PRINT '=== End of MCDEV-10706 Testing and Validation Scripts ===';

GO
