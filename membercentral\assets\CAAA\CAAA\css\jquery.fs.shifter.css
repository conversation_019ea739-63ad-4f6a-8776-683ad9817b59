/* 
 * Shifter v3.1.2 - 2014-10-28 
 * A jQuery plugin for simple slide-out mobile navigation. Part of the Formstone Library. 
 * http://formstone.it/shifter/ 
 * 
 * Copyright 2014 <PERSON>; MIT Licensed 
 */


.shifter-open {
  overflow: hidden;
}
.shifter-open .shifter-page *,
.shifter-open .shifter-header * {
  pointer-events: none;
}
.shifter-navigation {
  display: none;
  opacity: 0;
  visibility: hidden;
}
.shifter-handle {
  display: none;
}
.shifter-enabled .shifter-page {
  min-height: 100%;
  position: relative;
  z-index: 1;
  background: #ffffff;
  display: block;
}
.shifter-enabled .shifter-navigation {
  width: 300px;
  height: 100%;
  display: block;
  position: fixed;
  top: 0;
  z-index: 0;
  background: #ffffff;
  visibility: visible;
  opacity: 1;
  overflow: auto;
  overflow-x: hidden;
  pointer-events: none;
  -webkit-transition: opacity 0.001s ease 0.6s, -webkit-transform 0.6s ease;
          transition: opacity 0.001s ease 0.6s, transform 0.6s ease;
}
.shifter-enabled .shifter-page,
.shifter-enabled .shifter-header,
.shifter-enabled .shifter-navigation {
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
}
.shifter-enabled .shifter-page,
.shifter-enabled .shifter-header {
  -webkit-transform: translate3D(0, 0, 0);
      -ms-transform: translate3D(0, 0, 0);
          transform: translate3D(0, 0, 0);
  -webkit-transition: -webkit-transform 0.6s ease;
          transition: transform 0.6s ease;
}
.shifter-enabled .shifter-navigation {
  right: 0;
  -webkit-transform: translate3D(0, 0, 0);
      -ms-transform: translate3D(0, 0, 0);
          transform: translate3D(0, 0, 0);
}
.shifter-open .shifter-page,
.shifter-open .shifter-header {
  -webkit-transform: translate3D(-300px, 0, 0);
      -ms-transform: translate3D(-300px, 0, 0);
          transform: translate3D(-300px, 0, 0);
}
.shifter-enabled.shifter-left .shifter-navigation {
  left: 0;
  -webkit-transform: translate3D(0, 0, 0);
      -ms-transform: translate3D(0, 0, 0);
          transform: translate3D(0, 0, 0);
}
.shifter-enabled.shifter-left.shifter-open .shifter-page,
.shifter-enabled.shifter-left.shifter-open .shifter-header {
  -webkit-transform: translate3D(300px, 0, 0);
      -ms-transform: translate3D(300px, 0, 0);
          transform: translate3D(300px, 0, 0);
}
.shifter-open .shifter-navigation {
  opacity: 1;
  pointer-events: all;
  -webkit-transform: translate3D(0, 0, 0);
      -ms-transform: translate3D(0, 0, 0);
          transform: translate3D(0, 0, 0);
  -webkit-transition: opacity 0.001s ease 0s, -webkit-transform 0.6s ease;
          transition: opacity 0.001s ease 0s, transform 0.6s ease;
}
.no-csstransforms3d .shifter-enabled.shifter-navigation {
  right: -300px;
}
.no-csstransforms3d .shifter-enabled.shifter-left .shifter-navigation {
  left: -300px;
}
.no-csstransforms3d .shifter-enabled.shifter-open .shifter-page {
  left: -300px;
}
.no-csstransforms3d .shifter-enabled.shifter-open .shifter-navigation {
  right: 0;
}
.no-csstransforms3d .shifter-enabled.shifter-left.shifter-open .shifter-page {
  left: auto;
  right: -300px;
}
.no-csstransforms3d .shifter-enabled.shifter-left.shifter-open .shifter-navigation {
  left: 0;
}
