	
/* image replacement */
.graphic, #prevBtn, #nextBtn, #slider1prev, #slider1next{
  margin:0;
  padding:0;
  display:block;
  overflow:hidden;
  text-indent:-8000px;
}
/* // bootstrap img fix */

/* Easy Slider */

#slider ul, #slider li{
 border:none; list-style:none; margin:0; padding:0; height:300px;
}

#slider p{ padding:0 10px; }
.itemHeader{ 
  font-family:verdana; 
  font-size:16pt; 
  color:#000000; 
  display:block; 
  padding-bottom:10px; 
  margin-bottom:10px;
  border-bottom:2px solid #e9e9e9;
}
.itemTitle{ font-family:verdana; font-size:14pt; color:#076598; }
.itemText{ font-family:verdana; font-size:10pt; color:#000000; }
#slider li a{ }


/* For interior pages */

.sponsorsList ul, .sponsorsList li{
 border:none; list-style:none; margin:0; padding:0;
}

.sponsorsList ul { width:265px; height:275px; overflow:hidden; }
.sponsorsList li { width:265px; overflow:hidden; }
.sponsorsList p{ padding:0 10px 5px 10px; }
.sponsorsList li a{ display:block; text-align:right; }

--------------------------------------------------------------------
p#hide { display:none; }

#arrows {display:none; position:relative;float:right;width:159px;margin-top:-32px;margin-right:14px;*margin-right:-138px;z-index:800;}
#prevBtn, #slider1prev{float:left;width:20px;height:20px; padding-left:205px;}
#nextBtn, #slider1next{width:20px;height:20px;}
#prevBtn a, #slider1prev a{display:block;width:20px;height:20px;background:url(../images/prev.png) no-repeat;}
#nextBtn a, #slider1next a{display:block;width:20px;height:20px;background:url(../images/next.png) no-repeat;}
/**/
	
/* numeric controls */	
ol#controls{
  
  position:relative;
  float:right;
  margin:0 0 0 0;
  padding:0 0 0 0;
  height:18px;
  list-style:none;
  font-family:arial;
  font-size:13px;
  
  margin-right:10px;
  margin-top:-325px;
  z-index:900;
  background-color:#ffffff;
  
  }
ol#controls li{margin:0 0 0 0;margin-left:5px;float:left;list-style:none; }
ol#controls li a{
  background: url(../images/mh_control.png) no-repeat;
  display:block;
  cursor:pointer;
  height:10px;
  width:12px;
  padding:3px 0;
  text-align:center;
  text-decoration:none;
  }
ol#controls li.current a{ background: url(../images/mh_controlOver.png) no-repeat;}
ol#controls li a:hover{ background: url(../images/mh_controlOver_red.png) no-repeat; }
ol#controls li a:focus, #prevBtn a:focus, #nextBtn a:focus{outline:none;}


