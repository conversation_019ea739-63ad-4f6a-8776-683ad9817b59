<cfoutput>
<!doctype html>
<html>
	<head>
		<cfinclude template="head.cfm">
	</head>
	<body>
		<div class="wrapper">
			<cfinclude template="header.cfm">

			<div class="slider">
				<cfif application.objCMS.getZoneItemCount(zone='Main',event=event)>
					<div class="owl-carousel owl-theme">
						<cfset local.mainPageContent = event.getValue("mc_pageDefinition").pageZones['Main']>
						<cfset local.zoneCContent = event.getValue("mc_pageDefinition").pageZones['C']>
				
						<cfloop from="1" to="#arrayLen(local.mainPageContent)#" index="local.thisItem" >
							
							<cfif lcase(trim(local.mainPageContent[local.thisItem].view)) EQ 'echo' AND len(trim(local.mainPageContent[local.thisItem].data)) >
								<cfset local.mainSliderImage = replaceNoCase(replaceNoCase(local.mainPageContent[local.thisItem].data,'<p>', "", "All"),'</p>', "", "All")/>
								<cfif len(trim(local.mainSliderImage))>
									<div class="item">
										#local.mainSliderImage#
										<cfif application.objCMS.getZoneItemCount(zone='C',event=event) AND ArrayLen(local.zoneCContent) GTE local.thisItem  AND len(trim(local.zoneCContent[local.thisItem].data))>
											<div class="carousel-caption">
												<div class="captionFrame">
													#local.zoneCContent[local.thisItem].data#
												</div>
											</div>
										</cfif>
									</div>
								</cfif>
							</cfif>
						</cfloop>
					</div>
				</cfif>
				
				<cfif application.objCMS.getZoneItemCount(zone='D',event=event)>
					<div class="captionBtnBox">
						<div class="captionBtnFrame">
							<cfset local.zoneDContent = event.getValue("mc_pageDefinition").pageZones['D']>
                            #local.zoneDContent[1].data#
						</div>
					</div>
				</cfif>			
			</div>

			<div class="progresReportBox pd_40">
				<div class="container">
					<div class="row-fluid" id="websiteCarousel">[[websiteCarousel name=[Homepage Carousel Bottom]]]</div>
				</div>
			</div>

			<div class="eventBox pd_40 homeEventContainer">
				<div class="container">
					<div class="row-fluid flexBox">
						<div class="span4">
							<div class="eventBoxFrame">
								<cfif application.objCMS.getZoneItemCount(zone='I',event=event)>
									<cfset local.upcomingEventsHeaderContent = event.getValue("mc_pageDefinition").pageZones['I']>
									<div class="eventBoxTop">
										#local.upcomingEventsHeaderContent[1].data#
									</div>
								</cfif>
								<cfif application.objCMS.getZoneItemCount(zone='F',event=event)>
									<cfset local.upcomingEventsContent = event.getValue("mc_pageDefinition").pageZones['F']>
									<div class="eventBoxBottom hasMergeCode">
										#replace(local.upcomingEventsContent[1].data,'<p></p>','')#						
									</div>
								</cfif>
							</div>
						</div>
							
						<div class="span4">
							<div class="eventBoxFrame">
								<cfif application.objCMS.getZoneItemCount(zone='J',event=event)>
									<cfset local.recentNewsHeaderContent = event.getValue("mc_pageDefinition").pageZones['J']>
									<div class="eventBoxTop">
										#local.recentNewsHeaderContent[1].data#
									</div>
								</cfif>
								<cfif application.objCMS.getZoneItemCount(zone='G',event=event)>
									<cfset local.recentNewsContent = event.getValue("mc_pageDefinition").pageZones['G']>
									<div class="eventBoxBottom">
										#local.recentNewsContent[1].data#
									</div>
								</cfif>
							</div>
						</div>
						<div class="span4">
							<div class="eventBoxFrame">
								<cfif application.objCMS.getZoneItemCount(zone='K',event=event)>
									<cfset local.jobListingHeaderContent = event.getValue("mc_pageDefinition").pageZones['K']>
									<div class="eventBoxTop">
										#local.jobListingHeaderContent[1].data#
									</div>
								</cfif>
								<cfif application.objCMS.getZoneItemCount(zone='H',event=event)>
									<cfset local.jobListingContent = event.getValue("mc_pageDefinition").pageZones['H']>
									<div class="eventBoxBottom">
										#local.jobListingContent[1].data#
									</div>
								</cfif>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="featureSlider pd_60">
				<div class="container">
					<div class="row-fluid" id="featuredPartnersSection">						
						<cfif application.objCMS.getZoneItemCount(zone='Q',event=event)>
							<div class="hide" id="featuredPartnersContent">
								#application.objCMS.renderZone(zone='Q',event=event)#
							</div>
						</cfif>	
					</div>
				</div>
			</div>
			<cfinclude template="footer.cfm">
		</div>
	</body>
</html>
</cfoutput>