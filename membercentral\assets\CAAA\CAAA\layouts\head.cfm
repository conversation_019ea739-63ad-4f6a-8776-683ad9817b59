<cfset local.strMenus = application.objCMS.getPageMenus(event=event)>
<cfoutput>
	<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1" />
	<title>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</title>
	<meta name="google-site-verification" content="E9UwC2oWaHr5tVuoQnaaWKQMsXo_NUtj9H8k6FcMB3o" />	
	
	#application.objCMS.getBootstrapHeadHTML()#
	#application.objCMS.getResponsiveHeadHTML()#
	<link href='https://fonts.googleapis.com/css?family=Montserrat:400,700|Droid+Sans:400,700' rel='stylesheet' type='text/css'>
	<link rel="stylesheet" href="/css/main.css" />
	<link rel="stylesheet" href="/css/print.css" media="print" />
	
	<link href="/assets/common/javascript/owlCarousel/234/owl.carousel.min.css" rel="stylesheet" type="text/css" />
	<link rel="stylesheet" href="/css/animate.css">
    <link rel="stylesheet" href="/css/magnific-popup.css">
    <link rel="stylesheet" href="/css/jquery.fs.shifter.css">
    
        <!-- Icon Fonts CSS -->
    <link rel="stylesheet" href="/css/knight-iconfont.css">
    #application.objCMS.getFontAwesomeHTML(includeVersion4Support=false)#
    <!-- Vendor CSS -->
    <link rel="stylesheet" href="/css/reset.css">

    <!-- Plugins CSS -->
    <link rel="stylesheet" href="/css/animate.css">
    <link rel="stylesheet" href="/css/magnific-popup.css">
    <link rel="stylesheet" href="/css/jquery.fs.shifter.css">

    <!-- Template CSS -->
    <link rel="stylesheet" href="/css/main_new.css">
    <link rel="stylesheet" href="/css/shortcodes.css">
    <link rel="stylesheet" href="/css/custom-bg.css">
    
    <link rel="stylesheet" href="/css/style.css">
	<cfif event.getValue('mc_pageDefinition.layoutMode','normal') NEQ "direct">		
		<cfif len(event.getValue('mc_pageDefinition.pagekeywords',''))>
			<meta name="keywords" content="#event.getValue('mc_pageDefinition.pagekeywords','')#">
		</cfif>
		<cfif len(event.getValue('mc_pageDefinition.pageDescription',''))>
			<meta name="description" content="#event.getValue('mc_pageDefinition.pageDescription','')#">
		</cfif>
		
	 	<link rel="stylesheet" type="text/css" href="/assets/common/javascript/jQueryAddons/mmenu/jquery.mmenu.all.css" />	
	 	<link rel="stylesheet" href="/assets/common/css/commonBootstrapMobile.css">
		
		<link rel="stylesheet" href="/css/slideshow.css" />
		

		<script type="text/javascript" src="/assets/common/javascript/jQueryAddons/mmenu/jquery.mmenu.min.all.js"></script>
		<script type="text/javascript" src="/assets/common/javascript/hideaddrbar.js"></script>
		<script type="text/javascript" src="/assets/common/javascript/commonBootstrapMobile.js"></script>
		
		<script type="text/javascript" src="/javascript/jquery.slideshow.js"></script>		
		
		#application.objCMS.getMobileBookmarkIconsHTML(orgcode=event.getValue('mc_siteInfo.orgcode'),sitecode=event.getValue('mc_siteInfo.sitecode'))#
	</cfif>
	
	<link rel="shortcut icon" href="/images/favicon.ico">

	

		
	<style type="text/css">
	  <cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
		  ##noRightsLoggedOut{display:none;}
			##noRightsLoggedIn{display:block;}
  	<cfelse>
			##noRightsLoggedOut{display:block;}
			##noRightsLoggedIn{display:none;}
	  </cfif>
	</style>
	
	<script type="text/javascript">
		$(document).ready(function() {
			iOS_hoverFix($('##menuLeft').siblings('a'));
			iOS_hoverFix($('##menuRight').siblings('a'));
		});
	</script>

	<link rel="stylesheet" href="/css/latestNews.css">
	#application.objCMS.getSiteCustomCSS(siteID=arguments.event.getValue('mc_siteInfo.siteID'))#
</cfoutput>