/* This shit show is supposed to load at the END of the home page */

* { font-size: 3em; }

div#zoneA > div.zoneresource h1,
div#zoneA > div.zoneresource h2,
div#zoneA > div.zoneresource h3,
div#zoneA > div.zoneresource h4,
div#zoneA > div.zoneresource h5,
div#zoneA > div.zoneresource h6,
div#zoneA > div.zoneresource div
{
	color: #FFF!important;
}

@media only screen and (max-width: 992px) {
	div#zoneA > div.zoneresource h1,
	div#zoneA > div.zoneresource h2,
	div#zoneA > div.zoneresource h3,
	div#zoneA > div.zoneresource h4,
	div#zoneA > div.zoneresource h5,
	div#zoneA > div.zoneresource h6,
	div#zoneA > div.zoneresource div
	{
		color: #616161!important;
	}
}

/*---Banner Section---*/
.banner .item{position: relative;}
.owl-carousel .owl-item img.xsHidden{display: block;}
.owl-carousel .owl-item img.xsVisible{display: none;}
.banner .item .carouselCaption{text-align:left,left:10px;    position: absolute;    top: 50%;    left: 120px;    margin: 0 auto;    display: block;    transform: translateY(-50%);}
.banner .item .carouselImg:after{content: '';position: absolute;width: 100%;height: 100%;top: 0px;left: 0px;opacity: 0.7;}
.banner .item .carouselCaption h3{text-align:left;    font-size: 32px;    color: #fff;    font-family: 'Libre Baskerville',serif;    font-weight: 400;    margin-top: 0px;    margin: 0 0px 15px;    display: inline-block;    width: 100%;    vertical-align: top;}
.banner .item .carouselCaption h3 .fa{font-size: 32px;color: #060e43;margin-right: 10px;}
.banner .item .carouselCaption h2{text-align:left;font-size: 70px;font-weight: 400;font-family: 'Libre Baskerville', serif;color: #fff;line-height: normal;margin: 0px 0px 20px 0px;display: inline-block;width: 100%;} 
.banner .item .carouselCaption  p{ float: left;text-align:left;ont-size: 20px;font-weight: 400;color: #fff; font-family: 'Lato', sans-serif;max-width: 675px;line-height: 1.4;margin-top: 25px;display: inline-block;vertical-align: top;margin-bottom: 60px;}
.banner .item .carouselCaption .SliderbtnBlock .btnCustom{padding: 18px 45px;text-transform: uppercase;letter-spacing: 1px;}
.banner .item .carouselCaption .SliderbtnBlock .btnDefault2{background: #e5e5e5;border: 2px solid #060e43;color: #060e43;}
.banner .item .carouselCaption .SliderbtnBlock .btnDefault2:hover{background:#060e43; border:2px solid #060e43;color: #fff;}
.banner .item .carouselCaption .SliderbtnBlock .btnCustom:first-child{margin-right: 20px;}
.banner .owl-carousel .owl-prev,.banner .owl-carousel .owl-next{position: absolute;top: 50%;transform: translateY(-50%);}
.banner .owl-carousel .owl-prev .fa,.banner .owl-carousel .owl-next .fa{color: #fff;font-weight: 300;font-size: 60px;}
.banner .owl-carousel .owl-prev{left: 20px;}
.banner .owl-carousel .owl-next{right: 20px;}
.banner .owl-dots{position: absolute;bottom: 30px;left: 50%;transform: translateX(-50%);display: inline-flex;}
.banner .owl-dots .owl-dot{width: 12px;height: 12px;border:3px solid #a4b1b6;background: transparent;border-radius: 50%;margin: 0 4px;}
.banner .owl-dots .owl-dot.active{border:3px solid #060e43;}

.inner .banner{	padding: 50px 0px ;text-align: center;}

.banner .item .carouselCaption .SliderbtnBlock .btnCustom:first-child {
    margin-right: 20px;
}
.banner .item .carouselCaption .SliderbtnBlock{
   float: left;
   width:100%;
   display:inline-flex;
}
.banner .item .carouselCaption .SliderbtnBlock .btnCustom {
    padding: 18px 45px;
    text-transform: uppercase;
    letter-spacing: 1px;
}
.banner .item .carouselCaption .SliderbtnBlock .btnDefault1 {
    color: #fff;
    background: #060e43;
    border: 2px solid #060e43;
    padding: 18px 45px;
    transition: all .3s ease 0s;
    -moz-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    -ms-transition: all 0.3s ease-in-out;
    -webkit-transition: all 0.3s ease-in-out;
}

element.style {
}
.banner .item .carouselCaption .SliderbtnBlock .btnCustom:first-child {
    margin-right: 20px;
}
.banner .item .carouselCaption .SliderbtnBlock .btnCustom {
    padding: 18px 45px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

@media only screen and (max-width:479px) {
	.btnCustom, .btnDefault1, .btnDefault2{
		padding: 10px 10px;
	}
	.banner .item .carouselCaption h2 {
		font-size: 27px;
	}
	.banner .item .carouselCaption h3, .banner .item .carouselCaption h3 .fa {
		font-size: 18px;
		margin-bottom: 5px;
	}
	.banner .item .carouselCaption p {
		margin-bottom: 30px;
		max-width: 100%;
		margin-top: 15px;
		font-size: 16px;
	}
	.banner .item .carouselCaption{    left: 20px;
		right: 20px;
	}
}
@media only screen and (max-width:768px) {
	.bannerSlider .owl-nav{display: none;}
	.banner .item .carouselCaption{
		left: 35px;
		right: 35px;
	}
	.owl-carousel .owl-item img.xsHidden{display: none;}
		.owl-carousel .owl-item img.xsVisible{    display: block;
		height: 557px;
		width: 100%;
		object-fit: cover;}
		.banner .item .carouselCaption h3, .banner .item .carouselCaption h3 .fa{margin-bottom: 10px;}
		.banner .item .carouselCaption h2 {
		margin-bottom: 15px;
	}
	.banner .item .carouselCaption p {
		margin-bottom: 30px;
		max-width: 100%;
		margin-top: 15px;
		font-size: 18px;
	}
	.banner .item .carouselCaption .SliderbtnBlock .btnCustom:first-child {
		margin-right: 7px;
	}
	.banner .item .carouselCaption .SliderbtnBlock .btnCustom {
		font-size: 14px;
		padding: 8px 11px;
	}
}

@media only screen and (max-width:991px) {
	.banner .item .carouselCaption h2{
			font-size: 36px;
	}
	.banner .owl-dots {
		bottom: 3px;
	}
	.banner .item .carouselCaption h3, .banner .item .carouselCaption h3 .fa{line-height: normal;font-size: 24px;}
	hr.orange{margin: 10px 0px;}
	.banner .item .carouselCaption p{
			margin-bottom: 10px;
			max-width: 575px;
	}
	.banner .item .carouselCaption .SliderbtnBlock .btnCustom{
		font-size: 14px;
		padding: 8px 20px;
	}
}

@media only screen and (max-width:1199px) {
	.banner .item .carouselCaption .SliderbtnBlock .btnCustom{padding: 12px 25px;}
	.banner .item .carouselCaption h3, .banner .item .carouselCaption h3 .fa {
		font-size: 26px;
		margin-bottom: 5px;
	}
	.banner .item .carouselCaption h2 {
		font-size: 36px;
		margin-bottom: 0px;
	}
	.banner .owl-dots{
		bottom: 15px;
	}
	.banner .item .carouselCaption p{font-size: 18px;}

	.banner .owl-carousel .owl-prev .fa, .banner .owl-carousel .owl-next .fa{    font-size: 36px;}
}

@media only screen and (max-width:1365px){
	.banner .item .carouselCaption h2{
		font-size: 48px;
		margin-bottom: 0px;
	}
	.banner .item .carouselCaption h3,.banner .item .carouselCaption h3 .fa{
		    font-size: 24px;
	}
	.banner .item .carouselCaption p{
		margin-top: 0px;
		margin-bottom: 30px;
	}
}
}
@media only screen and (max-width:1599px){
	.banner .item .carouselCaption{left: 70px;}	
}
hr.orange {
    width: 110px;
    border-top: 3px solid #060e43;
    border-bottom: 3px solid #060e43;
}

/*****Service Section******/
.services-section img{width:72px;}
.services-section { margin-top: -30px;}
.services-section { padding: 45px 0px 15px 0px; background: #060e43 ; }
.services-left { position: relative; padding-bottom: 5px; display: inline-block; width: 100%; }
.spin-cls a { float: left; -webkit-transition: -webkit-transform .4s ease-in-out; -moz-transition: transform .4s ease-in-out; -ms-transition: transform .4s ease-in-out; transition: transform .4s ease-in-out; }
.spin-cls a:hover { transform: rotateY(360deg); -moz-transform: rotateY(360deg); -ms-transform: rotateY(360deg); -webkit-transform: rotateY(360deg); }
.spin-right { float: right; width: 70%; }
.spin-right h3 a, .spin-right h3 { font-family: 'Futura Book'; text-transform: uppercase; font-size: 24px; line-height: normal; font-weight: bold; color: #fff; margin: 0px; }
.spin-right h3 a:hover { opacity: 0.8; }
.spin-right p { color: #fff; font-size: 14px; line-height: 24px; margin: 0 0 7px 0; overflow-wrap: break-word; }

 @media (min-width:768px) and (max-width: 979px) {
.spin-cls { text-align: center; }
.spin-cls a { float: none; }
.spin-right { float: none; width: 100%; margin-top: 10px; text-align: center; }
}
@media only screen and (max-width:767px) {
.spin-right { width: 84%; padding-left: 20px; }
.spin-cls { width: 20%; }
}
@media only screen and (max-width:600px) {
.spin-cls { width: 22%; }
.spin-right { width: 80%; }
}
@media only screen and (max-width:450px) {
.spin-cls { width: 25%; }
.spin-right { width: 75%; }
}
.topBar{
	
	margin-bottom:0 !important;
}