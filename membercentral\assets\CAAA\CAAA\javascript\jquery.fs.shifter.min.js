/* 
 * Shifter v3.1.2 - 2014-10-28 
 * A jQuery plugin for simple slide-out mobile navigation. Part of the Formstone Library. 
 * http://formstone.it/shifter/ 
 * 
 * Copyright 2014 <PERSON>; MIT Licensed 
 */

!function(a,b){"use strict";function c(c){i||(k=a.extend({},n,c||{}),k.$html=a("html"),k.$body=a("body"),k.$shifts=a([g(l.page),g(l.header)].join(", ")),k.$nav=a(g(l.navigation)),k.$shifts.length>0&&k.$nav.length>0&&(i=!0,k.$body.on(m.click,g(l.handle),e),void 0!==b.matchMedia&&(k.mediaQuery=b.matchMedia("(max-width:"+(1/0===k.maxWidth?"100000px":k.maxWidth)+")"),k.mediaQuery.addListener(d),d())))}function d(){k.mediaQuery.matches?o.enable():o.disable()}function e(a){a.preventDefault(),a.stopPropagation(),j||(k.$body.hasClass(l.isOpen)?o.close():o.open()),"touchstart"===a.type&&(j=!0,setTimeout(f,500))}function f(){j=!1}function g(a){return"."+a}var h="shifter",i=!1,j=!1,k={},l={handle:"shifter-handle",page:"shifter-page",header:"shifter-header",navigation:"shifter-navigation",isEnabled:"shifter-enabled",isOpen:"shifter-open"},m={click:"touchstart."+h+" click."+h},n={maxWidth:"980px"},o={close:function(){i&&(k.$html.removeClass(l.isOpen),k.$body.removeClass(l.isOpen),k.$shifts.off(g(h)),k.$nav.find("input").trigger("blur"))},enable:function(){i&&k.$body.addClass(l.isEnabled)},destroy:function(){i&&(k.$html.removeClass(l.isOpen),k.$body.removeClass([l.isEnabled,l.isOpen].join(" ")).off(m.click),void 0!==b.matchMedia&&k.mediaQuery.removeListener(d),k={},i=!1)},disable:function(){i&&(o.close(),k.$body.removeClass(l.isEnabled))},open:function(){i&&(k.$html.addClass(l.isOpen),k.$body.addClass(l.isOpen),k.$shifts.one(m.click,e))}};a[h]=function(a){return o[a]?o[a].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof a&&a?this:c.apply(this,arguments)}}(jQuery,window);