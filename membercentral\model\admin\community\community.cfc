<cfcomponent output="false">

	<cffunction name="getCommunityData" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any">

		<cfset var qryCommunity = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCommunity">
			select ai.siteresourceID, comm.communityID, ai.applicationInstanceID, comm.CommunityName, 
				comm.CommunityDescription, comm.defaultCommunityPageName, comm.emailList, comm.hidden,
				comm.communityCenterAppInstanceID, srt.resourceType as pageResourceType, 
				grandParentType.resourceType as grandParentType, o.orgID, o.orgCode, s.siteID, s.siteCode,
				ai.applicationInstanceName, page.pageName, page.siteResourceID as pageResourceID
			from dbo.comm_Communities comm
			inner join dbo.cms_applicationInstances ai on ai.applicationInstanceID = comm.applicationInstanceID 
				and comm.communityID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('commID')#">
			inner join dbo.cms_siteResources sr on ai.siteResourceID = sr.siteResourceID
			inner join dbo.cms_siteResources pageResource on sr.parentSiteResourceID = pageResource.siteResourceID
			inner join dbo.cms_pages page ON page.siteResourceID = pageResource.siteResourceID
			inner join dbo.cms_siteResourceTypes srt on pageResource.resourceTypeID = srt.resourceTypeID
			inner join dbo.sites s on ai.siteID = s.siteID
			inner join dbo.organizations o on s.orgID = o.orgID
			left outer join dbo.cms_siteResources thisCommunityGrandParentResource
				inner join dbo.cms_siteResourceTypes grandParentType on thisCommunityGrandParentResource.resourceTypeID = grandParentType.resourceTypeID
				on thisCommunityGrandParentResource.siteResourceID = pageResource.parentSiteResourceID
		</cfquery>

		<cfreturn qryCommunity>
	</cffunction>
	
	<cffunction name="getCommunityCenters" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="currentCommunityCenterApplicationInstanceID" type="numeric" required="true">
		
		<cfset var qryCommunityCenters = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCommunityCenters">
			SELECT AI.applicationInstanceID, AI.siteResourceID, AI.applicationInstanceName
			FROM dbo.cms_ApplicationInstances AI
			INNER JOIN dbo.cms_ApplicationTypes AT ON AI.applicationTypeID = AT.applicationTypeID
				and AT.applicationTypeName = 'communityCenter'
			inner join dbo.cms_siteResources sr on ai.siteResourceID = sr.siteResourceID and sr.siteResourceStatusID = 1
			WHERE (
				ai.applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.currentCommunityCenterApplicationInstanceID#">
				or
				AI.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			)
		</cfquery>

		<cfreturn qryCommunityCenters>
	</cffunction>

	<cffunction name="saveMeta" access="public" output="true" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.returnStruct = { success=true };
			local.emailList = application.objcommon.getValidatedEmailAddresses(emailAddressList=arguments.event.getValue('newEmailList',''), delimiter=';');
		</cfscript>	

		<cfif arguments.event.getValue('commID',0) gt 0> 
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.saveMeta">
				SET NOCOUNT ON;

				DECLARE @communityID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('commID')#">, @applicationInstanceID int;

				SELECT @applicationInstanceID = applicationInstanceID 
				FROM dbo.comm_communities 
				WHERE communityID = @communityID;

				BEGIN TRAN
					UPDATE dbo.comm_communities
					SET communityName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#LEFT(arguments.event.getValue('newcommunityName'), 100)#">,
						communityDescription = <cfqueryparam cfsqltype="cf_sql_varchar" value="#LEFT(arguments.event.getValue('newcommunityDescription'), 400)#" null="#IIF(arguments.event.getValue('newcommunityDescription') EQ "", true, false)#">,
						emailList = NULLIF(<cfqueryparam cfsqltype="cf_sql_varchar" value="#local.emailList#">,''),
						communityCenterAppInstanceID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('newCommunityCenterAppInstanceID')#">,
						hidden = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('newHidden','false')#">
					WHERE communityID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('commID')#">;

					UPDATE dbo.cms_applicationInstances 
					SET applicationInstanceName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('instanceName')#">, 
						applicationInstanceDesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('instanceName')#">
					WHERE applicationInstanceID=@applicationInstanceID;
				COMMIT TRAN
			</cfquery>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getPageName" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="pageResourceID" type="numeric" required="yes">

		<cfset var qryPage = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryPage">
			SELECT p.pageid, p.pageName
			FROM dbo.cms_pages AS p
			WHERE p.siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.pageResourceID#">
			AND p.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
		</cfquery>

		<cfreturn qryPage>
	</cffunction>

	<cffunction name="updatePageName" access="public" output="true" returntype="void">
		<cfargument name="Event" type="any">
		
		<cfset var qryUpdatePage = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryUpdatePage">
			SET NOCOUNT ON;

			DECLARE @communityID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('commID',0)#">,
				@pageResourceID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('pageResourceID',0)#">,
				@newPageName varchar(100) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#LEFT(arguments.event.getValue('newPageName'), 100)#">,
				@oldPageName varchar(100);

			SELECT @oldPageName = pageName
			FROM dbo.cms_pages
			WHERE siteResourceID = @pageResourceID;

			BEGIN TRAN;
				-- update page Name
				UPDATE dbo.cms_pages 
				SET pageName = @newPageName 
				WHERE siteResourceID = @pageResourceID;

				--update community defaultCommunityPageName
				UPDATE dbo.comm_communities
				SET defaultCommunityPageName = @newPageName+'Home'
				WHERE communityID = @communityID; 

				-- update sub page Names
				UPDATE p
				SET p.pageName = replace(p.pageName,@oldPageName,@newPageName)
				FROM dbo.fn_comm_getSubPagesAdmin(@communityID) fn
				INNER JOIN dbo.cms_pages p ON fn.pageID = p.pageID;
			COMMIT TRAN;	
		</cfquery>
	</cffunction>
		
</cfcomponent>