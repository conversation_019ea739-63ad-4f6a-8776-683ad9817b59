function ValidateDropDownSelection(SelectName, ValidationName)
{
	/*This is a check to see that they selected an actual value rather
	 * than a 'please select' with no value.  It will display an
	 * existing element that will show the error message
	 * Inputs
	 *    SelectName - name of the drop down select element
	 *    ValidationName - name of element you want to be displayed
	 */
   var myElement = document.getElementById(SelectName);
   if(myElement != null && myElement.value.length < 1)
   {
   	    if(document.getElementById(ValidationName)!= null)
   	    {
   	    	document.getElementById(ValidationName).style.display = 'inline';
   	    	return false;
   	    }
   }
}

function PollWindow(mypage, myname, w, h, scroll) 
{
	var winl = (screen.width - w) / 2;
	var wint = (screen.height - h) / 2;
	winprops = 'height=' + h + ',width=' + w + ',top=' + wint + ',left=' + winl + ',scrollbars=' + scroll + ',resizable';
	win = window.open(mypage, myname, winprops);
	if (parseInt(navigator.appVersion) >= 4) 
	{ 
		win.window.focus(); 
	}
}

function NewWindowImage(mypage, myname, w, h, scroll)
{
	var winl = (screen.width - w) / 2;
	var wint = (screen.height - h) / 2;
	winprops = "height=" + h + ",width=" + w + ",top=" + wint + ",left=" + winl + ",scrollbars=yes,resizable";
	win = window.open(mypage, myname, winprops);
	if (parseInt(navigator.appVersion) >= 4)
	{
		win.window.focus();
	}
}


function dropdownjump(targ,selObj)
{
	var loc=selObj.options[selObj.selectedIndex].value;
	if (loc)	
		{
       	eval(targ+".location='"+loc+"'");
     	}
    else
    	{
           selObj.selectedIndex=0;
        }
}


function validateUSDate(strValue ) {
/************************************************
DESCRIPTION: Validates that a string contains only
    valid dates with 2 digit month, 2 digit day,
    4 digit year. Date separator can be  -
    Uses combination of regular expressions and
    string parsing to validate date.
    Ex. mm/dd/yyyy or mm-dd-yyyy or mm.dd.yyyy

PARAMETERS:
   strValue - String to be tested for validity

RETURNS:
   True if valid, otherwise false.

REMARKS:
   Avoids some of the limitations of the Date.parse()
   method such as the date separator character.
*************************************************/

  var objRegExp = /^\d{1,2}(\-)\d{1,2}\1\d{4}$/

  // var objRegExp = /^\d{1,2}(\-|\/|\.)\d{1,2}\1\d{4}$/

  //check to see if in correct format
  if(!objRegExp.test(strValue))
  {

	return false; //doesn't match pattern, bad date

	}
  else{
    var strSeparator = strValue.substring(2,3) //find date separator

	var arrayDate = strValue.split(strSeparator); //split date into month, day, year
    //create a lookup for months not equal to Feb.


    var arrayLookup = { '01' : 31,'03' : 31,'04' : 30,'05' : 31,'06' : 30,'07' : 31,'08' : 31,'09' : 30,'10' : 31,'11' : 30,'12' : 31}
    var intDay = parseInt(arrayDate[1],10);

    //check if month value and day value agree
    if(arrayLookup[arrayDate[0]] != null) {
      if(intDay <= arrayLookup[arrayDate[0]] && intDay != 0)
        return true; //found in lookup table, good date
    }

    //check for February (bugfix 20050322)
    var intMonth = parseInt(arrayDate[0]);
    if (intMonth == 2) {
       var intYear = parseInt(arrayDate[2]);
       if( ((intYear % 4 == 0 && intDay <= 29) || (intYear % 4 != 0 && intDay <=28)) && intDay !=0)
          return true; //Feb. had valid number of days
       }
  }

  return false; //any other values, bad date
}



function Trim(TRIM_VALUE){
	if(TRIM_VALUE.length < 1){
	return"";
	}
	TRIM_VALUE = RTrim(TRIM_VALUE);
	TRIM_VALUE = LTrim(TRIM_VALUE);
	if(TRIM_VALUE==""){
	return "";
	}
	else{
	return TRIM_VALUE;
	}
	} //End Function

function RTrim(VALUE){
	var w_space = String.fromCharCode(32);
	var v_length = VALUE.length;
	var strTemp = "";
	if(v_length < 0){
	return"";
	}
	var iTemp = v_length -1;

	while(iTemp > -1){
	if(VALUE.charAt(iTemp) == w_space){
	}
	else{
	strTemp = VALUE.substring(0,iTemp +1);
	break;
	}
	iTemp = iTemp-1;

	} //End While
	return strTemp;

	} //End Function

function LTrim(VALUE){
	var w_space = String.fromCharCode(32);
	if(v_length < 1){
	return"";
	}
	var v_length = VALUE.length;
	var strTemp = "";

	var iTemp = 0;

	while(iTemp < v_length){
	if(VALUE.charAt(iTemp) == w_space){
	}
	else{
	strTemp = VALUE.substring(iTemp,v_length);
	break;
	}
	iTemp = iTemp + 1;
	} //End While
	return strTemp;
	} //End Function
	

function isnumeric(alphane)
{
	var numaric = alphane;
	for(var j=0; j<numaric.length; j++)
		{
		  var alphaa = numaric.charAt(j);
		  var hh = alphaa.charCodeAt(0);
		  //alert(hh);
		  if ((hh > 47 && hh<58) || (hh == 32) || (hh == 41) || (hh == 40))
		  {

		  }
		else	{
			 return false;
		  }
		}
 return true;
}

function fieldIsNumeric(elementId) {
	if (!elementId.value) return false;
	var numberToTest = elementId.value;
	var isFound = /^-?\d+$/.test(numberToTest);
	return isFound
}

function shortTrim(str) {
	if (str.length < 101) {
		//if the string is shorter than 101 characters then trim.
		return str.replace(/^\s\s*/, '').replace(/\s\s*$/, '');
	} else {
		//if the string is longer than 101 characters then call the longTrim function.
		return longTrim(str);
	}
}

function longTrim(str) {
	var	str = str.replace(/^\s\s*/, ''),
		ws = /\s/,
		i = str.length;
	while (ws.test(str.charAt(--i)));
	return str.slice(0, i + 1);
}
function clearPassword()
{
	document.getElementById('password').value = "";
}

function restoreIfBlank()
{
 if(document.getElementById('password').value == "")document.getElementById('password').value = "PASSWORD";
}
