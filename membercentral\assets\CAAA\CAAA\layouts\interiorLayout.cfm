<cfset qrySectionList = event.getValue('mc_pageDefinition.qrySectionTreeUp') />
<cfquery dbtype="query" name="qrySections">
	SELECT * FROM qrySectionList
	ORDER BY DEPTH
</cfquery>
<cfset sectionCodeList = valueList(qrySections.sectionCode) />
<cfoutput>
	<cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "direct"><!--- DISPLAY THE FOLLOWING IN DIRECT MODE --->
		<cfinclude template="directMode.cfm" />
	<cfelse>
		<!DOCTYPE html>
		<html xmlns="//www.w3.org/1999/xhtml">
			<head>
				<cfinclude template="head.cfm">
			</head>
			<body>
				<body class="shifter shifter-left offcanvas-menu-left offcanvas-menu-white mobile-header-style2 sticky-header">
				<cfinclude template="header.cfm">
				<div class="main-wrapper shifter-page">
					<div class="main-header style2 fixed-header">
			            <div class="main-header-inner">
			                <div class="main-bar style2 padding-15 white-bg">
			                    <div class="container-fluid">
			                        <div class="row-fluid">
			                            <div class="logo-container">
			                                #application.objCMS.renderZone(zone='I',event=event, mode='div')#
			                            </div><!-- /logo-container -->
			                            <div class="menu-container clearfix nav-collapse collapse">
			                                <div class="main-nav active-style1 style1" id="main-nav">
			                                   <cfif structKeyExists(local.strMenus,"primaryNav")>
													#local.strMenus.primaryNav.menuHTML.rawcontent#
												</cfif>


												<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
													<ul class="nav">
														<li>
															<a class="menu-item color_link" href="/?pg=login" class="">Login</a>
														</li>
													</ul>
												<cfelse>
													<ul class="nav">
														<li>
															<a class="menu-item" href="/?logout" class="">Logout</a>
														</li>
													</ul>
												</cfif>


			                                     <ul class="clearfix nav serach-icon1">
			                                        <li class="icon icon-search dropdown">
			                                           <cfsavecontent variable="local.zoneJ">
															#application.objCMS.renderZone(zone='J',event=event, mode='div')#
														</cfsavecontent>
														<cfset local.zoneJ = replaceNoCase(local.zoneJ, '<div id="zoneJ" class="zonewrapper">', "", "All")>
														<cfif structKeyExists(event.getValue("mc_pageDefinition").pageZones,"J")>
															<cfloop array="#event.getValue("mc_pageDefinition").pageZones['J']#" index="local.thisItem">							
															    <cfset local.zoneJ = replaceNoCase(local.zoneJ, '<div id="J#local.thisItem.resourceNodeAttributes.siteResourceID#" class="zoneresource">', "", "All")>
																<cfset local.zoneJ = replaceNoCase(local.zoneJ, "<p>", "", "All")>
																<cfset local.zoneJ = replaceNoCase(local.zoneJ, "</p>", "", "All")>
																<cfset local.zoneJ = replaceNoCase(local.zoneJ, "</div>", "", "All")>
															</cfloop>	
														</cfif>   
						            					#local.zoneJ#
			                                            <div class="sub-menu dropdown-menu dropdown-search-form">
			                                                <form name="searchbox" id="searchbox" action="/?pg=search" method="post" class="search-form"> 
			                                                	<input name="s_a" id="s_a" type="hidden" value="doSearch" /> 
																<input name="s_frm" id="s_frm" type="hidden" value="1" />
			                                                    <input class="dark-bg" type="text" name="s_key_all" id="s_key_all" placeholder="Search">
			                                                    <input type="submit" value="&gt;">
			                                                </form>
			                                            </div><!-- /sub-menu -->
			                                        </li>
			                                    </ul>
			                                </div>
			                            </div><!-- /menu-container -->
			                        </div>
			                    </div><!-- /container -->
			                </div><!-- /main-bar -->
			            </div><!-- /main-header-inner -->
			            <div class="title-barBF header-fullcategory">
			            	<div class="innerbanner-img">
			            		<cfsavecontent variable="local.zoneZ">
									#application.objCMS.renderZone(zone='Z',event=event, mode='div')#
								</cfsavecontent>
								<cfset local.zoneZ = replaceNoCase(local.zoneZ, '<div id="zoneZ" class="zonewrapper">', "", "All")>
								<cfif structKeyExists(event.getValue("mc_pageDefinition").pageZones,"Z")>
									<cfloop array="#event.getValue("mc_pageDefinition").pageZones['Z']#" index="local.thisItem">							
									    <cfset local.zoneZ = replaceNoCase(local.zoneZ, '<div id="Z#local.thisItem.resourceNodeAttributes.siteResourceID#" class="zoneresource">', "", "All")>
										<cfset local.zoneZ = replaceNoCase(local.zoneZ, "<p>", "", "All")>
										<cfset local.zoneZ = replaceNoCase(local.zoneZ, "</p>", "", "All")>
										<cfset local.zoneZ = replaceNoCase(local.zoneZ, "</div>", "", "All")>
									</cfloop>	
								</cfif>   
            					#local.zoneZ#
            				</div>
			                <div class="container header-large banner-caption">
			                    <h6 class="heading-alt-styleBF white-color">#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</h6>
			                </div><!-- /container -->
			            </div><!-- /header-banner -->
			            <div class="container header-small">
			                <h6 class="heading-alt-styleBF-small">#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</h6>
			            </div>

			        </div>
			        <section class="main-contents">
			            <div class="container">
			                <div class="cat1">
			                <!-- /row -->
			                	#application.objCMS.renderZone(zone='Main',event=event, mode='div')#
			                </div>
			            </div><!-- /contianer -->
			        </section>
			         <cfinclude template="footer.cfm">

			        <!-- End main-footer -->
			    </div>
			    <cfinclude template="toolBar.cfm">
			</body>
		</html>
	</cfif>
</cfoutput>