<cfif structKeyExists(local.strMenus,"mobileNav")>
	<div class="navbar navbar-fixed-top hidden-desktop hidden-tablet hidden-print" id="mainNavBar">
		<div class="navbar-inner">
			<div class="container">
				<a class="btn btn-navbar btn-inverse pull-left" href="javascript:MCCommonBootstrapMobile.toggleMainNav();"><i class="icon-reorder icon-large icon-white"></i></a>
				<cfif application.objCMS.getZoneItemCount(zone='MobileNavbarTop',event=event)>
					<cfoutput>#application.objCMS.renderZone(zone='MobileNavbarTop',event=event, mode='li')#</cfoutput>
				<cfelse>
					<!-- Be sure to leave the brand out there if you want it shown -->
					<a class="brand" href="/"><cfoutput>#ucase(event.getValue('mc_siteInfo.sitename'))#</cfoutput></a>
				</cfif>
				<div class="pull-right">
					<a class="btn btn-navbar visible-desktop visible-tablet visible-phone" href="/"><i class="icon-home icon-large icon-white"></i></a>
					<cfif application.objCMS.getAppMenuCount(event=event)>
						<a class="btn btn-navbar visible-desktop visible-tablet visible-phone" href="javascript:MCCommonBootstrapMobile.toggleAppNav();"><i class="icon-ellipsis-vertical icon-large icon-white"></i></a>
					</cfif>
				</div>
			</div>
		</div>
	</div>
	<div style="display:none;"><nav id="mainSiteMenuMMenuNav"><cfoutput>#local.strMenus.mobileNav.menuHTML.rawcontent#</cfoutput></nav></div>
</cfif>
<cfif application.objCMS.getAppMenuCount(event=event)>
	<cfoutput>#application.objCMS.renderAppMenu(event=event)#</cfoutput>
</cfif>
