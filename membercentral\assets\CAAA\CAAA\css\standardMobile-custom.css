@charset "utf-8";

body {
  margin: 0;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #333333;
  background-color: #ffffff;
}

a {
  color: #0088cc;
} 

p {
  font-size: 16px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #333333;
}

.page-header {
  border-bottom: 1pxpx solid #eeeeee;
}

hr {
  border-top: 1px solid #e5e5e5;
  border-bottom: 1px solid #ffffff;
}

select,
textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
.uneditable-input {
  background-color: #ffffff;
  padding: 4px 4px 4px 4px;
  font-size: 13px;
  color: #555555;
  border: 1px solid #ccc;
}

.well {
  background-color: #f5f5f5;
}

.btn {
  color: #333333;
  background-image: -moz-linear-gradient(top, #ffffff, #e6e6e6);
  background-image: -ms-linear-gradient(top, #ffffff, #e6e6e6);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#e6e6e6));
  background-image: -webkit-linear-gradient(top, #ffffff, #e6e6e6);
  background-image: -o-linear-gradient(top, #ffffff, #e6e6e6);
  background-image: linear-gradient(top, #ffffff, #e6e6e6);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#'ffffff, endColorstr='#'e6e6e6, GradientType=0);
}

.btn:hover,
.btn:active,
.btn.active,
.btn.disabled,
.btn[disabled] {
  color: #333333;
  background-color: #e6e6e6;
}

.btn-primary {
  background-color: #006dcc;
  color: #ffffff;
  background-image: -moz-linear-gradient(top, #7c0000, #440000);
  background-image: -ms-linear-gradient(top, #7c0000, #440000);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#7c0000), to(#440000));
  background-image: -webkit-linear-gradient(top, #7c0000, #440000);
  background-image: -o-linear-gradient(top, #7c0000, #440000);
  background-image: linear-gradient(top, #7c0000, #440000);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#'7c0000, endColorstr='#'440000, GradientType=0);
}

.btn-primary:hover,
.btn-primary:active,
.btn-primary.active,
.btn-primary.disabled,
.btn-primary[disabled] {
  background-color: #440000;
}

.btn-warning {
  color: #ffffff;
  background-image: -moz-linear-gradient(top, #f7b100, #e29e14);
  background-image: -ms-linear-gradient(top, #f7b100, #e29e14);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#f7b100), to(#e29e14));
  background-image: -webkit-linear-gradient(top, #f7b100, #e29e14);
  background-image: -o-linear-gradient(top, #f7b100, #e29e14);
  background-image: linear-gradient(top, #f7b100, #e29e14);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#'f7b100, endColorstr='#'e29e14, GradientType=0);
}

.btn-warning:hover,
.btn-warning:active,
.btn-warning.active,
.btn-warning.disabled,
.btn-warning[disabled] {
  background-color: #e29e14;
}

.btn-danger {
  color: #ffffff;
  background-image: -moz-linear-gradient(top, #ee5f5b, #bd362f);
  background-image: -ms-linear-gradient(top, #ee5f5b, #bd362f);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ee5f5b), to(#bd362f));
  background-image: -webkit-linear-gradient(top, #ee5f5b, #bd362f);
  background-image: -o-linear-gradient(top, #ee5f5b, #bd362f);
  background-image: linear-gradient(top, #ee5f5b, #bd362f);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#'ee5f5b, endColorstr='#'bd362f, GradientType=0);
}

.btn-danger:hover,
.btn-danger:active,
.btn-danger.active,
.btn-danger.disabled,
.btn-danger[disabled] {
  background-color: #bd362f}

.btn-success {
  color: #ffffff;
  background-image: -moz-linear-gradient(top, #666666, #333333);
  background-image: -ms-linear-gradient(top, #666666, #333333);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#666666), to(#333333));
  background-image: -webkit-linear-gradient(top, #666666, #333333);
  background-image: -o-linear-gradient(top, #666666, #333333);
  background-image: linear-gradient(top, #666666, #333333);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#'666666, endColorstr='#'333333, GradientType=0);
}
.btn-success:hover,
.btn-success:active,
.btn-success.active,
.btn-success.disabled,
.btn-success[disabled] {
  background-color: #333333;
}

.btn-info {
  color: #ffffff;
  background-image: -moz-linear-gradient(top, #0088cc, #0044cc);
  background-image: -ms-linear-gradient(top, #0088cc, #0044cc);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#0088cc), to(#0044cc));
  background-image: -webkit-linear-gradient(top, #0088cc, #0044cc);
  background-image: -o-linear-gradient(top, #0088cc, #0044cc);
  background-image: linear-gradient(top, #0088cc, #0044cc);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#'0088cc, endColorstr='#'0044cc, GradientType=0);
}
.btn-info:hover,
.btn-info:active,
.btn-info.active,
.btn-info.disabled,
.btn-info[disabled] {
  background-color: #0044cc;
}

.alert {
  padding: 8px 35px 8px 14px;
  background-color: #fcf8e3;
  border: 1px solid #fbeed5;
}

.alert-success {
  background-color: #dff0d8;
  border-color: #d6e9c6;
}
.alert-success, .alert-success .alert-heading {
  color: #468847;
}
.alert-danger, .alert-error {
  background-color: #f2dede;
  border-color: #eed3d7;
}
.alert-danger,
.alert-error,
.alert-danger .alert-heading,
.alert-error .alert-heading {
  color: #b94a48;
}
.alert-info {
  background-color: #d9edf7;
  border-color: #bce8f1;
}
.alert-info, .alert-info .alert-heading {
  color: #3a87ad;
}

.navbar-inner {
  background-image: -moz-linear-gradient(top, #ffffff, #f2f2f2);
  background-image: -ms-linear-gradient(top, #ffffff, #f2f2f2);
  background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#ffffff), to(#f2f2f2));
  background-image: -webkit-linear-gradient(top, #ffffff, #f2f2f2);
  background-image: -o-linear-gradient(top, #ffffff, #f2f2f2);
  background-image: linear-gradient(top, #ffffff, #f2f2f2);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#'ffffff, endColorstr='#'f2f2f2, GradientType=0);
}

.navbar .brand {
  padding: 8px 20px 12px;
  font-size: 20px;
  color: #727272;
}

.navbar .divider-vertical {
  height: 41px;
  background-color: #ffffff;
  border-right: 1px solid #c6c6c6;
}

.navbar .nav > li > a {
  padding: 10px 10px 11px;
  font-size: 14px;
  color: #666666;
}

.navbar .nav > .active > a,
.navbar .nav > .active > a:hover,
.navbar .nav > .active > a:focus {
  color: #424242;
}

.label,
.badge {
  padding: 1px 3px 2px;
  font-size: 9.75px;
  color: #ffffff;
  background-color: #999999;
}

.label-important,
.badge-important {
  background-color: #468847;
}

.label-warning,
.badge-warning {
  background-color: #b94a48;
}

.label-success,
.badge-success {
  background-color: #f89406;
}

.label-info,
.badge-info {
   background-color: #3a87ad;
}
