<cfoutput>
<div class="dyouk pd_40">
	<div class="container">
		<div class="row-fluid">
			<cfif application.objCMS.getZoneItemCount(zone='L',event=event)>
				#application.objCMS.renderZone(zone='L',event=event)#
			</cfif>					
		</div>
	</div>
</div>
<div class="footer pd_40">
	<div class="container">
		<div class="footer-top">
			<div class="footCol2 mobileShow socialSection">
				<cfif application.objCMS.getZoneItemCount(zone='N',event=event)>
					#application.objCMS.renderZone(zone='N',event=event)#
				</cfif>
			</div>
			<div class="footCol1 mobileShow contactSection">
				<cfif application.objCMS.getZoneItemCount(zone='M',event=event)>
					#application.objCMS.renderZone(zone='M',event=event)#
				</cfif>
			</div>
			<div class="row-fluid">
				<div class="span5 footCol1 desktopShow contactSection">
					<cfif application.objCMS.getZoneItemCount(zone='M',event=event)>
						#application.objCMS.renderZone(zone='M',event=event)#
					</cfif>
				</div>
				<div class="span2 footCol2 desktopShow socialSection">
					<cfif application.objCMS.getZoneItemCount(zone='N',event=event)>
						#application.objCMS.renderZone(zone='N',event=event)#
					</cfif>
				</div>
				<div class="span5 footCol3 latestNewsSection">
					<cfif application.objCMS.getZoneItemCount(zone='O',event=event)>
						#application.objCMS.renderZone(zone='O',event=event)#
					</cfif>
				</div>
			</div>
		</div>
		<cfif application.objCMS.getZoneItemCount(zone='P',event=event)>
			<div class="footer-bottom">
				#application.objCMS.renderZone(zone='P',event=event)#
			</div>
		</cfif>
	</div>
	<cfif application.objCMS.getZoneItemCount(zone='ToolBar',event=event) >
		<div id="toolBarArea">
			<div id="toolBar">#application.objCMS.renderZone(zone='ToolBar',event=event)#</div>
		</div>
	</cfif>
</div>

<cfif event.getValue('mc_pageDefinition.pageName') EQ "MyMembershipCard">

  <script type="text/javascript" src="/javascript/html2canvas.js"></script>
  <script type="text/javascript" src="/javascript/qrcode.js"></script>
  <script type="text/javascript" src="/javascript/FileSaver.js"></script>
  <script type="text/javascript" src="/javascript/canvas-toBlob.js"></script>
  <script type="text/javascript" src="/javascript/Blob.js"></script>
  <script type="text/javascript">
    var qrCodeTxt = "[[membernumber]],[[lastname]],[[firstname]]";

    new QRCode(document.querySelector("##qrcodecontainer"), {
        text: qrCodeTxt,
        width: 50,
        height: 50,
        colorDark : "##000000",
        colorLight : "##ffffff",
        correctLevel : QRCode.CorrectLevel.L,
        useSVG: false
    });

    function saveImg() {
      html2canvas(document.querySelector("##printarea"), {
        useCORS: true,
        allowTaint: false,
        foreignObjectRendering: false}).then(function(canvas) {
          console.log(canvas);
          //var img = new Image();
          //var imgSrc = canvas.toDataURL("image/png");
          //img.src = imgSrc;
          //$('##output').html(img);
          //mySave(imgSrc, "BAMSLMembershipCard.png");

          if(!navigator.userAgent.match('CriOS')) {
            canvas.toBlob(function(blob) {
              // Generate file download
              window.saveAs(blob, "BAMSLMembershipCard.png");
            });
          } else {
            //for chrome in apple devices
            var a = document.createElement('a');
            a.href = canvas.toDataURL();
            a.download = 'screen.png';
            a.target='blank';
            a.click();
          }
        });
    }
    document.getElementById("dl").addEventListener('click', saveImg, false);
  </script>

</cfif>
  
<script>
  var loc = window.location.href;
      $('.side-menu ul li').each(function() {
          var link = $(this).find('a:first').attr('href');
          if(loc.indexOf(link) >= 0)
              $(this).addClass('active');
      });
</script>
</cfoutput>
