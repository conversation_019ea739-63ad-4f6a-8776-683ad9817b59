<cfoutput>
	<div class="hide printHeader"></div>
	<div class="hide printHeaderContact"></div>
	<header class="header outer-width">
		<div id="navbar-example" class="navbar">
			<div class="navbar-inner">
				<div class="container">
					<div class="row-fluid"> 
						<a class="btn btn-navbar" data-toggle="collapse" data-target=".nav-collapse"> 
							<span class="icon-bar"></span>
							<span class="icon-bar"></span>
							<span class="icon-bar"></span>
						</a>
						<cfif application.objCMS.getZoneItemCount(zone='A',event=event)>
							<cfset local.siteLogoContent  = event.getValue("mc_pageDefinition").pageZones['A']>
							<div class="hide siteBrandSection">#REReplace(REReplace(local.siteLogoContent[1].data,"<p>",""),"</p>","")#</div>
							<div class="hide"><div class="logoSubUrl">#event.getvalue('mc_siteInfo.MAINHOSTNAME','')#</div></div>
						</cfif>
						
						<cfset local.membercenterContent = "">
                        <cfset local.quickLinksContent = "">
                        <cfif application.objCMS.getZoneItemCount(zone='B',event=event)>
                            <cfloop array="#event.getValue("mc_pageDefinition").pageZones['B']#" index="local.zoneBContent">
                                <cfif trim(local.zoneBContent.contentAttributes.contentTitle) EQ "Welcome Message">
                                    <cfsavecontent variable="local.membercenterContent">
                                        #trim(replaceNoCase(local.zoneBContent.data, '<p>', "", "All"))#
                                    </cfsavecontent>
                                <cfelse>
                                    <cfsavecontent variable="local.quickLinksContent">
                                        #trim(replaceNoCase(local.zoneBContent.data, '<p>', "", "All"))#
                                    </cfsavecontent>
                                </cfif>
                            </cfloop>
                        </cfif>
						
						<ul id="searchSectionContentTop" style="display:none;">
							<li class="dropdown searchBtnFn xs979 hoverMenu">
								<ul class=" row-fluid">
								<li class="span9 megaMenuSection">
								<div class="formframe clearfix">
									<form action="/?pg=search" method="get" name="searchboxTop" onsubmit="return validateSearchForm()">
										<input name="pg" type="hidden" value="search">
										<input name="s_a" type="hidden" value="doSearch"> 
										<input name="s_frm" type="hidden" value="1"> 
										<input name="s_key_all" onblur="if (this.value == '') {this.value = 'Search our website...';}"
										onfocus="if (this.value == 'Search our website...') {this.value = '';}" value="Search our website..."
										type="text">
										<a href="javascript:document.forms.searchboxTop.submit();" class="btn btnWhite"><i class="fa fa-search"></i></a>
									</form>
								</div>
								</li>
								</ul>
							</li>
						</ul>
						
						<ul id="searchSectionContentBottom" style="display:none;">
							<li class="dropdown searchBtnFn xsHidden979 hoverMenu"> <a href="javascript:void(0);" class="dropdown-toggle">
								<i class="fa fa-search"></i> </a>
								<ul class="dropdown-menu row-fluid">
									<li class="span3 megaMenuSection">
										<div class="heading text-center searchHeading">
											<p class="TitleText">SEARCH</p>
										</div>
									</li>
									<li class="span9 megaMenuSection">
										<div class="formframe clearfix">
											<form class="marginZero" action="/?pg=search" method="get" onsubmit="return validateSearchForm()">
												<input name="pg" type="hidden" value="search">
												<input name="s_a"  type="hidden" value="doSearch"> 
												<input name="s_frm" type="hidden" value="1"> 
												<input name="s_key_all" onblur="if (this.value == '') {this.value = 'What can we help you find?';}"
												onfocus="if (this.value == 'What can we help you find?') {this.value = '';}" value="What can we help you find?"
												type="text">
												<button class="btn btnCustom btnWhite" type="submit">Submit</button>
											</form>
										</div>
									</li>
								</ul>
							</li>
						</ul>
						
						<ul id="memberCenterSectionContentTop" style="display:none;">
							<li class="dropdown memberFirst xs979 clearfix"> <a <cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>href="/?pg=myBAMSL" <cfelse>  href="javascript:void(0);" </cfif> class="dropdown-toggle"><cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser) AND (session.cfcuser.memberData.hasMemberPhotoThumb OR session.cfcuser.memberData.hasMemberPhoto)>
											<cfif session.cfcuser.memberData.hasMemberPhotoThumb>
												<cfset local.photoFolderPath = "/memberphotosth/#LCASE(session.cfcUser.memberData.memberNumber)#.jpg?cb=#getTickCount()#">
											<cfelse>
												<cfset local.photoFolderPath = "/memberphotos/#LCASE(session.cfcUser.memberData.memberNumber)#.jpg?cb=#getTickCount()#">
											</cfif>
											<img class="avatarPic" src="#local.photoFolderPath#" >
										<cfelse>
											<img src="/images/member.png" alt=""> 
										</cfif>                                         
										member center
										
								</a>
								<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser) >
									<a class="logoutLink" href="/?logout">Logout</a>
								</cfif>
								<ul class="dropdown-menu row-fluid memberSection">
									<li class="span3 megaMenuSection xsHidden979">
										<div class="heading text-center">
											<p class="TitleText"> member center</p>
											<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
												<a href="/?pg=myBAMSL" class="btnCustom btnWhite">My BAMSL</a>
											</cfif>
										</div>
									</li>
									<li class="span6 megaMenuSection">											
										#local.membercenterContent#
									</li>
									<li class="span3 megaMenuSection">
										<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
											#local.quickLinksContent#
										<cfelseif event.getValue('mc_pageDefinition.pageName','') NEQ "Login">
											<p class="HeaderText">Site Login</p>
											<form action="/?pg=login&returnurl=#URLEncodedFormat("/?pg=myBAMSL")#" method="post" name="frmLoginMob" class="mc_form_login">
												#application.objUser.renderSecurityKeyElement()#
												<label>Username:</label>
												<input type="text" name="username" placeholder="Username">
												<label>Password:</label>
												<input type="password" name="password" placeholder="Password" autocomplete="off">
												<button class="btnCustom btnWhite" type="submit">Login</button> <a href="/?pg=login&logact=requestReset"> I
												forgot my username or password</a>
											</form>
										</cfif>
									</li>
								</ul>
							</li>
						</ul>
						
						<ul id="memberCenterSectionContentBottom" style="display:none;">
							<li class="dropdown xsHidden979 hoverMenu"> <a <cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>href="/?pg=myBAMSL"  class="dropdown-toggle paddingTop15"><cfelse>  href="javascript:void(0);" class="dropdown-toggle "></cfif>
										<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser) AND (session.cfcuser.memberData.hasMemberPhotoThumb OR session.cfcuser.memberData.hasMemberPhoto)>
											<cfif session.cfcuser.memberData.hasMemberPhotoThumb>
												<cfset local.photoFolderPath = "/memberphotosth/#LCASE(session.cfcUser.memberData.memberNumber)#.jpg?cb=#getTickCount()#">
											<cfelse>
												<cfset local.photoFolderPath = "/memberphotos/#LCASE(session.cfcUser.memberData.memberNumber)#.jpg?cb=#getTickCount()#">
											</cfif>
											<img class="avatarPic" src="#local.photoFolderPath#" >
										<cfelse>
											<img src="/images/member.png" alt=""> 
										</cfif>  member center</a>
								<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser) >
									<a class="logoutLink" href="/?logout">Logout</a>
								</cfif>
								<ul class="dropdown-menu row-fluid memberSection">
									<li class="span3 megaMenuSection xsHidden979">
										<div class="heading text-center">
											<p class="TitleText"> member center</p>
											<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
												<a href="/?pg=myBAMSL" class="btnCustom btnWhite">My BAMSL</a>
											</cfif>
										</div>
									</li>
								<li class="span6 megaMenuSection">
									#local.membercenterContent#
								</li>
								<li class="span3 megaMenuSection">
									<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
											#local.quickLinksContent#
									<cfelseif event.getValue('mc_pageDefinition.pageName','') NEQ "Login">
										<p class="HeaderText">Site Login</p>
										<form action="/?pg=login&returnurl=#URLEncodedFormat("/?pg=myBAMSL")#" method="post" name="frmLogin" class="mc_form_login">
											#application.objUser.renderSecurityKeyElement()#
											<label for="username">Username:</label>
											<input type="text" name="username" placeholder="Username">
											<label for="password">Password:</label>
											<input type="password" name="password" placeholder="Password" autocomplete="off">
											<button class="btnCustom btnWhite" type="submit">Login</button> <a href="/?pg=login&logact=requestReset"> I
											forgot my username or password</a>
										</form>
									</cfif>
								</li>
								</ul>
							</li>
						</ul>
						
						<cfset local.strMenus = application.objCMS.getPageMenus(event=event)>
						
						<div class="nav-collapse collapse mainNavigationWrapper" style="display:none;">
							<cfif structKeyExists(local.strMenus,"primaryNav")>
								#local.strMenus.primaryNav.menuHTML.rawcontent#
							</cfif>
						</div>
						
					</div>
				</div>
			</div>
		</div>
	<!-- /navbar- -->
	</header><div class="headerSpace"></div><!--Header End-->
</cfoutput>