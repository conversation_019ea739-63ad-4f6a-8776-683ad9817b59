article.latestNews {
	width: 100%;
	background-color: #EFEFEF;
	margin-bottom: 20px;
}

@media screen and (min-width: 480px) {
	article.latestNews {
		width: 48%;
		margin-right: 2%;
	}
}

@media screen and (min-width: 920px) {
	article.latestNews {
		width: 31%;
		margin-right: 2%;
	}
}

article.latestNews > img {
	width: 100%;
}

div.latestNews {
	background-color: #A09E9F;
	color: #FFF;
	text-transform: uppercase;
	text-align: center;
	width: 60%;
	margin: 0 auto;
	padding: 0.66em 0;
	position: relative;
	top: -24px;
}

div.content-canvas {
	padding: 0 20px 10px 20px;
}

a.readMoreLink,
div.Date {
	color: #18297d;
	text-transform: uppercase;
	font-size: 1em;
}

div.Title {
	font-size: 1.5em;
	font-weight: bold;
	text-transform: uppercase;
}

div.Summary {
	font-size: 1em;
}