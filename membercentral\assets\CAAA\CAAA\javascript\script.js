$(document).ready(function(e) {
    $(".pageTitle").html($(".upcomingsHolder h1").text());    
    var upcommingsContent="<div class='events-main text-center'>";

    $(".upcomingsHolder ul").each(function(key){
        var locationHtml = "";
        if($($(this).children()[6]).html().trim().length > 0){
            locationHtml = '<p><strong>Location</strong> <span class="block">'+ $($(this).children()[6]).html() +'</span></p>';
        }
        var descriptionHtml = "";
        if($($(this).children()[5]).html().trim().length > 0){
            descriptionHtml = '<p><strong>Description</strong> <span class="block">'+ $($(this).children()[5]).html() +'</span></p>';
        }
        var imageHtml = "";
        var spliterClass = "event-noimage";
        if($($(this).children()[1]).find('img').attr('src').trim().length > 0){
            imageHtml = '<div class="span6 event-left">'+ $($(this).children()[1]).html() +'</div>';
            spliterClass = "event-spliter";
        }
                
        upcommingsContent = upcommingsContent +   
            '<div class="event-box">\
                <p class="category-section-title">'+ $($(this).children()[2]).html() +'</p>\
                <h6>'+ $($(this).children()[3]).html() + ' ' + $($(this).children()[4]).html() +'</h6>\
                <div class="row-fluid event-lt-rt">\
                    '+ imageHtml +'\
                    <div class="span6 event-right '+ spliterClass +'">\
                        '+ locationHtml +'\
                        '+ descriptionHtml +'\
                        <p><strong>Date & Time</strong> <span class="block">'+ $($(this).children()[7]).html() +'</span></p>\
                      <!-- <p><strong>Cost</strong> <span class="block">'+ $($(this).children()[8]).html() +'</span></p>-->\
                    </div>\
                </div>\
                '+ $($(this).children()[0]).html() +'\
            </div>';   
    });

    upcommingsContent = upcommingsContent + '</div>';
    $('.upcomingsHolder').html(upcommingsContent);
    $(".pageTitle").show();
    $('.upcomingsHolder').show();
	
	// banner Slider
	$('.bannerSlider').owlCarousel({ 
	  autoplay: 23000, //Set AutoPlay to 3 seconds  
	  autoplaySpeed : 500,
	  autoHeight: true,
	  loop: false,
	  margin: 0,
	  items : 1,
	  itemsDesktop : [1199,1],
	  itemsDesktopSmall : [979,1],
	  itemsTablet:[768,1],
	  nav : true,
	  navText : ["<i class='fa fa-angle-left'></i>","<i class='fa fa-angle-right'></i>"],
	  pagination: false,
	  touchDrag: true,
	  mouseDrag: true,
	  loop: true,
	  transitionStyle :true,
	  animateIn: 'fadeIn',
	  animateOut: 'fadeOut',
	  responsiveClass:true,
		responsive:{
			0:{
				autoplay: 4000, //Set AutoPlay to 3 seconds  
				autoplaySpeed : 500,
			},
			481:{
				autoplay: 4000, //Set AutoPlay to 3 seconds  
				autoplaySpeed : 500,
			},
			768:{
				autoplay: 3000, //Set AutoPlay to 3 seconds  
				autoplaySpeed : 500,
			},
			992:{
				autoplay: 3000, //Set AutoPlay to 3 seconds  
				autoplaySpeed : 500,
			}
		}
	});
	 prepareServices();
});
function prepareServices(){
    var servicesContent = "";
    var count = 0;    
    $(".servicesWrapper > ul").each(function(key){
        count++;
        if($(this).children('li').length){
            var boxSelector = $(this).find("> li");
            if(count % 4 == 1){
                servicesContent = servicesContent + '<div class="row-fluid">';
            }            
            servicesContent = servicesContent +              
                '<div class="span3 wow fadeInUp animated">'+
                    '<div class="services-inner">'+
                        '<div class="services-left">'+
                            '<div class="spin-cls">'+
                                $(boxSelector[0]).html()+
                            '</div>'+
                            '<div class="spin-right">'+
                                '<h3>'+ $(boxSelector[1]).html() +'</h3>'+
                                '<p>'+ $(boxSelector[2]).html() +'</p>'+
                            '</div>'+
                        '</div>'+
                    '</div>'+
                '</div>';
            if(count % 4 == 0){
                servicesContent = servicesContent + '</div>';
            }               
        }              
    });
    $('.servicesHolder').replaceWith(servicesContent);   
    $('.servicesHolder').show();
}