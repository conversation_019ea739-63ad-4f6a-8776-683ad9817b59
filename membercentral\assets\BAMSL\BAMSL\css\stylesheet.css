@charset "utf-8";
body { margin: 0; padding: 0; font-family: 'museo300'; color: #333; background-color: #fff; }
*, input[type="search"] { -moz-box-sizing: border-box; -ms-box-sizing: border-box; -o-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; }
.firstContentBox input[type=text],.firstContentBox input[type=password], .contentBox input[type=text],.contentBox input[type=password]{ -moz-box-sizing: initial; -ms-box-sizing: initial; -o-box-sizing: initial; -webkit-box-sizing: initial; box-sizing: initial;}
input { outline: none; }
img { max-width: 100%; }
a { color: #008e89; text-decoration: none; }
a:hover, a:focus { color: #39506b; text-decoration: none }
p { font-size: 14px; color: #434343; }
ul.nav, ul.nav ul,ul.unstyled  { margin: 0; padding: 0; list-style: none; }
.marginZero{margin:0px;}
.pd_40 { padding: 40px 0px; }
.pd_60 { padding: 60px 0; }
.clearfix::before, .clearfix::after { content: ""; display: table; width: 100%; clear: both; }
.container { padding: 0 15px; }
h2, .TitleText { font-size: 32px; color: #455f84; font-weight: 300; line-height: normal; }
h3, .HeaderText { font-size: 24px; color: #455f84; font-weight: bold; }
h4, .HeaderTextSmall { font-size: 20px; color: #455f84; font-weight: 600; letter-spacing: 2px; text-transform: uppercase; }
h5 { color: #455f84; font-weight: 600; letter-spacing: 2px; text-transform: uppercase; font-size: 14px; }
a.HeaderTextSmall, h4 a { font-weight: 600; text-transform: uppercase; letter-spacing: 2px; color: #008e89; font-size: 20px; text-decoration: underline; margin-bottom: 5px; display: block; }
a.TitleText, a.HeaderText, a.HeaderTextSmall, h2 a, h3 a, h4 a { color: #008e89; text-decoration: underline; }
p, .BodyText { font-weight: 300; line-height: 26px; font-size: 18px; color: #434343; }
p.BodyTextLarge, .BodyTextLarge { font-size: 16px; }
p.BlueText, .BlueText { font-size: 14px; color: #455f84; }
.seaGreenTextBox,.HighlightContent { background: #5bbfbe; }
.HeaderTextMediumLink { color: #008e89; font-size: 20px; text-decoration: none; }
.HeaderTextMediumLink:hover { text-decoration: underline; color: #008e89; }
.btn { border-radius: 0px!important; }
.btnCustom{background: transparent; border: 2px solid; font-size: 13px; font-weight: 400; font-family: 'museo700'; height: 50px; min-width: 160px; text-transform: uppercase; border-radius: 0px; line-height: 46px; padding: 0; margin: 0; letter-spacing: 2px; box-shadow: none; text-shadow: none; padding: 0 25px;}
.btnWhite { color: #fff; border-color: #fff; }
.btnWhite:hover { background: #fff; color: #2d3e55; border-color: #fff; }
.btnSeaGreen { color: #5bbfbe; border-color: #5bbfbe; font-size: 12px; }
.btnSeaGreen:hover { background-color: #5bbfbe; color: #fff; border-color: #5bbfbe; }
.btnBlueSolid { border-color: #39506b; color: #39506b; }
.btnBlueSolid:hover { background: #39506b; color: #fff; }
.btnGreen { border-color: #008e89; color: #008e89; }
.btnGreen:hover { background: #008e89; color: #fff; border-color: #008e89; }
.btn-primary{background: #fff;color: #333;border-color: #000;}
.btn-primary:hover,.btn-primary:focus{background: #e6e6e6;color: #333;}
.xs979 { display: none !important; }
.xs767 { display: none !important; }
.xsHidden979 { display: block !important; }
.xsHidden767 { display: block !important; }
.textUnderline { text-decoration: underline; }
/***Header***/
.header { background: #fff; min-height: 140px; position: fixed; width: 100%; top: 0; z-index: 999; }
.headerSpace { width: 100%; height: 139px; background-color: transparent; }
.header .navbar { margin-bottom: 0; }
/*.container .nav li,.container .nav a,.container a.brand{line-height:30px;}*/
.megaMenuSection form button.btnCustom{min-width: auto;float: left;}
.header .navbar-inner { border: none; -moz-border-radius: 0; -ms-border-radius: 0; -o-border-radius: 0; -webkit-border-radius: 0; border-radius: 0; -moz-box-shadow: none; -ms-box-shadow: none; -o-box-shadow: none; -webkit-box-shadow: none; box-shadow: none; padding: 0; min-height: inherit; background: #fff; }
.header .navbar-brand { margin-left: 0px; float: left; max-height: 100%; height: auto; padding: 0; line-height: 140px; }
.navbar .navbar-brand img { width: auto; max-width: none; margin-left: 62px; }
.header .navbar .nav li a { color: #39506b; padding: 58px 18px; position: relative; display: block; font-weight: 300; font-size: 18px; text-align: center; text-decoration: none; -moz-text-shadow: none; -ms-text-shadow: none; -o-text-shadow: none; -webkit-text-shadow: none; text-shadow: none; text-transform: capitalize; background: transparent; max-height: 140px; }
.header .navbar .nav > li.dropdown:hover > a:after, .header .navbar .nav > li.dropdown:focus > a:after, .header .navbar .nav > li.dropdown:visited > a:after { width: 0; height: 0; border-left: 35px solid transparent; border-right: 35px solid transparent; border-top: 27px solid #fff; content: ""; position: absolute; top: 100%; z-index: 9; left: 0; right: 0; margin: 0 auto; bottom: 0px; }
.header .navbar .nav > li:last-child > a { background-color: #c9b572; margin-left: 25px; width: 183px; padding: 35px 6px; color: #fff; text-align: center; font-size: 14px; font-family: 'museo300'; text-transform: uppercase; }
.header .navbar .nav > li:last-child:hover > a, .header .navbar .nav > li:last-child:focus > a, .header .navbar .nav > li:last-child:visited > a, .header .navbar .nav li:last-child > a:hover, .header .navbar .nav > li:last-child > a:focus, .header .navbar .nav > li:last-child > a:visited { background-color: #5bbfbe; color: #fff; }
.header .navbar .nav > li.dropdown:last-child:hover:hover > a:after, .header .navbar .nav > li.dropdown:last-child:hover:focus > a:after, .header .navbar .nav > li.dropdown:last-child:hover:visited > a:after { border-top: 27px solid #017976; display: none; }
.header .navbar .nav li:last-child a img { width: 37px; height: 37px; display: block; margin: 0 auto; margin-bottom: 15px; }
.header .navbar .nav li:last-child { max-width: none; }
.header .navbar .nav li.dropdown .memberSection li, .header .navbar .nav li.dropdown .memberSection li p, .header .navbar .nav li.dropdown .memberSection li a { color: #fff; display: inline-block; padding: 0; font-weight: 300; font-size: 16px; letter-spacing: 0.2px; }
.header .navbar .nav li.dropdown .memberSection .mainMenu >li{display: block;}
.header .navbar .nav li.dropdown .memberSection li p { margin-bottom: 20px; }
.header .navbar .nav li.dropdown .memberSection li a { text-decoration: underline; }
.header .navbar .nav li.dropdown .memberSection li label { font-weight: 300; font-size: 16px; letter-spacing: 0.2px; }
.header .navbar .nav li.dropdown .memberSection li input { background-color: #4d9d9c; border: 2px solid #fff; height: 50px; border-radius: 0; width: 100%; margin-bottom: 20px; color: #fff; }
.header input::-webkit-input-placeholder { /* Chrome/Opera/Safari */
 color: #8fb9b9;
font-size: 20px;
font-family: 'museo300';
font-style: italic;
}
.header input::-moz-placeholder { /* Firefox 19+ */
 color: #8fb9b9;
font-size: 20px;
font-family: 'museo300';
font-style: italic;
}
.header input:-ms-input-placeholder { /* IE 10+ */
 color: #8fb9b9;
font-size: 20px;
font-family: 'museo300';
font-style: italic;
}
.header input:-moz-placeholder { /* Firefox 18- */
 color: #8fb9b9;
font-size: 20px;
font-family: 'museo300';
font-style: italic;
}
.header .navbar .nav li.dropdown .memberSection li form a.btn { color: #fff; background: transparent; border: 2px solid #fff; font-size: 13px; font-weight: 500; height: 50px; min-width: auto; text-transform: uppercase; border-radius: 0px; font-family: 'museo700'; line-height: 46px; padding: 0; margin: 0; letter-spacing: 2px; box-shadow: none; text-shadow: none; padding: 0 25px; display: inline-block; width: auto; text-decoration: none; }
.header .navbar .nav li.dropdown .memberSection li form a.btn:hover { background: #fff; color: #2d3e55; }
.header .navbar .nav li.dropdown .memberSection li form a { width: 50%; float: left; }
.header .navbar .nav li.dropdown .memberSection li form a:last-child { font-size: 14px; text-align: left; padding: 0px; text-decoration: none; margin-left: 15px; margin-top: 5px; text-transform: inherit; font-style: italic;}
.header .navbar .nav li.dropdown li a:hover, .header .navbar .nav li .dropdown-menu>li.dropdown-submenu ul li a:hover, .header .navbar .nav li.dropdown li a:focus, .header .navbar .nav li .dropdown-menu>li.dropdown-submenu ul li a:focus, .header .navbar .nav li .dropdown-menu>li:hover a { background: transparent; }
.dropdown li { padding: 0 0px; }
.header .navbar .nav li.active a { color: #006eb3; background-color: #ffffff; border-color: #eeeeee; box-shadow: none; }
.header .navbar .nav > li { display: inline-block; padding: 0; max-width: 166.8px; position: static; }
.header .navbar .nav li a:hover, .header .navbar .nav li a:focus { background: transparent; color: #008e89; }
.header .nav-collapse.collapse { margin: 0; }
.header .nav-collapse .nav { margin: 0; float: right; width: auto; position: static; font-family: 'museo500'; }
.header .navbar .nav>li>.dropdown-menu::after, .header .navbar .nav>li>.dropdown-menu::before { display: none; }
.dropdown-menu>li>a { color: #3b3b3c; }
.header .navbar .nav li .dropdown-menu>li>a { padding: 7px 10px; font-size: 11px; line-height: 16px; border-right: none; text-align: left; white-space: normal; }
.header .dropdown-menu { -moz-box-shadow: none; -ms-box-shadow: none; -o-box-shadow: none; -webkit-box-shadow: none; box-shadow: none; }
.header .navbar .nav li .dropdown-menu>li:last-child a { border-bottom: none; }
.nav-collapse .dropdown-menu { width: 215px; }
.navbar .nav>li>a { position: relative; background: #FFF; z-index: 99999; }
.dropdown-submenu>.dropdown-menu { border: none; padding: 0; margin: 0; }
.dropdown-submenu>a::after { /*display: none;*/ }
.header .navbar .nav li .dropdown-menu>li.dropdown-submenu ul li a { border: none; background: rgba(0, 0, 0, 0.1); }
.navbar .nav li.dropdown.open>.dropdown-toggle, .navbar .nav li.dropdown.active>.dropdown-toggle, .navbar .nav li.dropdown.open.active>.dropdown-toggle, .navbar .nav li.active>.dropdown-toggle { color: #006eb3; background-color: #ffffff; border-color: #eeeeee; box-shadow: none; }
 .nav-collapse [data-toggle="dropdown"] {
 display: none;
}
.nav-collapse  .dropdown-menu { border-radius: 0; background: rgb(0, 107, 182); }
.header .navbar .nav li.dropdown li a { padding: 3px 20px; border: none; margin-bottom: 0px; color: #3b3b3c; line-height: 1.42857; font-size: 14px; font-weight: 700; }
.header .navbar .nav li.dropdown .megaMenuSection .heading { max-width: 215px; margin: 0; top: 50%; transform: translateY(-50%); position: absolute; }
.header .navbar .nav li.dropdown .megaMenuSection .searchHeading { text-transform: uppercase; font-weight: 500; width: 100%; max-width: 308px; text-align: right; }
.header .navbar .nav li.dropdown .megaMenuSection .heading .TitleText { color: #fff; line-height: normal; color: #fff; line-height: normal; font-size: 38px; font-weight: 500; margin: 0; text-transform: capitalize; }
.header .navbar .nav li.dropdown .megaMenuSection .formframe { width: 100%; background-color: #4d9d9c; padding: 15px 15px; }
.header .navbar .nav li.dropdown .megaMenuSection .formframe input { float: left; background: transparent; border: 0; color: #fff; width: calc(100% - 160px); font-size: 32px; height: auto; display: inline-block; margin: 0; height: 50px; box-shadow: none; outline: none; padding: 0 15px; font-weight: 300; font-style: italic; letter-spacing: 1px; font-family: 'Roboto', sans-serif; }
.header .navbar .nav li.dropdown .megaMenuSection .formframe input::-webkit-input-placeholder { /* Chrome/Opera/Safari */
 color:  #fff;
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe input::-moz-placeholder { /* Firefox 19+ */
 color:  #fff;
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe input:-ms-input-placeholder { /* IE 10+ */
 color: #fff;
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe input:-moz-placeholder { /* Firefox 18- */
 color:  #fff;
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe a { float: right; color: #fff; background: transparent; border: 2px solid #fff; font-size: 13px; font-weight: 500; height: 50px; min-width: auto; text-transform: uppercase; border-radius: 0px; font-family: 'museo700'; line-height: 46px; padding: 0; margin: 0; letter-spacing: 2px; box-shadow: none; text-shadow: none; padding: 0 25px; display: inline-block; width: auto; }
.header .navbar .nav li.dropdown .megaMenuSection .formframe a:hover { background: #fff; color: #2d3e55; }
.header .navbar .nav li.dropdown .megaMenuSection .heading .btn { color: #fff; background: transparent; border: 2px solid #fff; font-size: 13px; font-weight: 400; height: 50px; min-width: auto; text-transform: uppercase; border-radius: 0px; font-family: 'museo700'; line-height: 46px; padding: 0; margin: 0; letter-spacing: 2px; box-shadow: none; text-shadow: none; padding: 0 25px; margin-top: 20px; display: inline-block; width: auto; }
.header .navbar .nav li.dropdown .megaMenuSection .heading .btn:hover { background: #fff; color: #2d3e55; border-color: #fff; }
.header .navbar .nav li.dropdown .megaMenuSection li a { color: #fff; text-decoration: none; text-align: left; font-weight: 400; font-size: 16px; text-transform: capitalize; padding: 3px 0px; }
.header .navbar .nav li.dropdown .megaMenuSection li a i { padding-right: 5px; }
.header .navbar .nav li.dropdown .megaMenuSection li a:hover, .header .navbar .nav li.dropdown .megaMenuSection .BodyText:hover { color: #38506a; }
.header .navbar .nav li.dropdown .megaMenuSection li .subMenu { padding-left: 20px; }
.header .navbar .nav li.dropdown .megaMenuSection .HeaderText { font-weight: 100; color: #fff; position: relative; padding-bottom: 5px; font-size: 26px; font-family: 'museo700'; line-height: normal; margin-bottom: 20px; }
.header .navbar .nav li.dropdown .megaMenuSection .HeaderText::before { width: 290px; top: 100%; height: 2px; background: #fffc; content: ""; position: absolute; border-radius: 4px; }
.header .navbar .nav li.dropdown .megaMenuSection .BodyText { font-family: 'museo300'; font-size: 13px; text-transform: uppercase; font-weight: bold; letter-spacing: 2px; color: #fff; margin-top: 20px; }
.searchBtnFn .default { display: block; }
.searchBtnFn .hover { display: none; }
.searchBtnFn:hover .default { display: none; }
.searchBtnFn:hover .hover { display: block; }
.mainMenuOnclickBtn { cursor: pointer; }
.header .navbar .nav li.searchBtnFn a { font-size: 24px; }
/*-------Slider-----***/
.slider { position: relative; }
.slider .owl-carousel .item { height: 650px;background-repeat: no-repeat; background-size: cover; background-position: center; }
.slider .owl-carousel .item:before { position: absolute; width: 100%; content: ""; background: rgba(34,40,58,0.75); height: 100%; z-index: 0; }
/*.slider .owl-carousel .item img { height: 650px; }*/
.slider .owl-carousel .owl-dots { position: absolute; left: 0; right: 0; margin: 0; bottom: 80px; }
.slider .owl-carousel .owl-item img {display:block; width:100%; height:100%; object-fit: cover;}
.owl-theme .owl-dots .owl-dot { outline: none; }
.carousel-caption { background: transparent; max-width: 1920px; left: 0; right: 0; margin: 0 auto; padding: 0; top: 50%; transform: translateY(-50%); color: #fff; }
.captionFrame { margin-left: 86px; max-width: 900px; }
.captionFrame h3 { color: #5cc0be; font-size: 20px; text-transform: uppercase; letter-spacing: 2px; margin: 0; font-weight: 400; font-family: 'museo900'; }
.captionFrame h3 i { padding-right: 12px; }
.captionFrame h1 { font-size: 48px; color: #fff; font-weight: 400; line-height: normal; margin-bottom: 40px; margin-top: 20px; font-family: 'museo100'; }
.captionBtnBox { position: absolute; right: 0; width: 100%; max-width: 1920px; height: 100%; top: 0; left: 0; margin: 0 auto; }
.captionBtnFrame { background-color: rgba(45, 62, 85, 0.5); position: absolute; right: 0; width: 100%; max-width: 430px; height: 100%; padding: 25px 25px; top: 0; z-index: 1; }
.captionBtnBox ul li { height: 130px; width: 100%; overflow: hidden; position: relative; margin-bottom: 25px; }
.captionBtnBox ul li:last-child { margin-bottom: 0px; }
/*.captionBtnBox ul li a,*/.captionBtnBox ul li > div { padding: 15px 25px; display: inline-block; width: 100%; vertical-align: top; background: #39506b; }
/*.captionBtnBox ul li a:hover,*/.captionBtnBox ul li > div:hover { background: #5bbfbe; }
.captionBtnBox ul li a .iconBox,.captionBtnBox ul li > div > div:first-child { width: 50px; height: 40px; float: left; margin: 30px 0px; text-align: center; }
.captionBtnBox ul li a .iconBox img,.captionBtnBox ul li > div > div:first-child > a > img{ margin: 0 auto; padding-top: 2px; }
.captionBtnBox ul li a .iconBox img.default,.captionBtnBox ul li > div > div:first-child > a > img:first-child { display: block; }
.captionBtnBox ul li a .iconBox img.hover,.captionBtnBox ul li > div > div:first-child > a > img:last-child { display: none; }
.captionBtnBox ul li a:hover .iconBox img.default,.captionBtnBox ul li > div:hover > div:first-child > a >  img:first-child { display: none; }
.captionBtnBox ul li a:hover .iconBox img.hover,.captionBtnBox ul li > div:hover > div:first-child > a >  img:last-child { display: block; }
.captionBtnBox ul li a .textBox,.captionBtnBox ul li > div > div:nth-child(2) { position: absolute; left: 100px; top: 50%; transform: translateY(-50%); max-width: 230px; overflow: hidden; }
.captionBtnBox ul li a .textBox h2,.captionBtnBox ul li > div > div:nth-child(2) h2 a{ text-decoration:none;margin: 0; padding: 0; color: #fff; font-size: 24px; font-weight: 400; font-family: 'museo300'; }
.captionBtnBox ul li a .arrow,.captionBtnBox ul li > div span { float: right; padding: 40px 0px; }
/*----Progress Report------**/
.progresReportBox { background: #232d4b; text-align: center; }
.progresReportBox .TitleText { font-size: 32px; font-weight: 300; color: #fff; font-family: 'museo500'; margin-bottom: 20px; }
.progresReportBox ul { text-align: center; margin:0;}
.progresReportBox ul li { display: inline-block; margin: 0 10px; }
.progresReportBox ul li a.btn img.default { display: inline-block; }
.progresReportBox ul li a.btn img.hover { display: none; }
.progresReportBox ul li a.btn:hover img.default { display: none; }
.progresReportBox ul li a.btn:hover img.hover { display: inline-block; }
/*-------Event Box Css----------*/
.eventBox { background: #e7e7dc; }
.eventBoxTop { position: relative; }
.eventImgBox { position: relative; }
.eventImgBox img { width: 100%; }
.eventImgBox:before { position: absolute; top: 0; left: 0; width: 100%; height: 100%; content: ""; z-index: 1; opacity: 0.5; background: #b9992e; }
.eventimgText { position: absolute; top: 50%; width: 100%; height: auto; left: 50%; transform: translate(-50%, -50%); text-align: center; z-index: 9; }
.eventimgText img { margin-bottom: 40px; }
.eventBoxBottom { padding: 18px 25px 0; }
.eventTextSection { margin-bottom: 30px; }
.eventTextBox p { color: #666666; }
.eventTextBox .Bodytext { font-family: 'museo700'; margin-bottom: 6px; line-height: 22px; }
.eventTextBox .Bodytext span { display: block; font-size: 13px; color: #a6a6a6; line-height: 15px; margin-top: 3px; }
.eventTextBox .BodyTextLarge { margin-bottom: 0px; color: #666666; font-size: 15px; line-height: 20px; font-family: 'museo500'; margin-bottom: 16px; }
.eventBoxFrame .eventBtnBox{position: absolute; left: 50%; -moz-transform: translateX(-50%); -webkit-transform: translateX(-50%); transform: translateX(-50%); bottom: -30px;width: 100%;}
.eventBoxFrame .btn.btnBlue { margin-bottom: 0; padding: 0 10px;  border: none;  min-width: 142px; height: 50px; line-height: 47px; }
.eventBoxFrame { background: #fff; }
.eventBoxFrame { position: relative; }
.eventTextBox a { color: #666666; text-decoration: underline; cursor: pointer; }
.eventBtnBox a { color: #fff; text-decoration: none; }
.eventTextBox a.seeJob { color: #5bbfbe; text-transform: uppercase; font-family: 'museo700'; text-decoration: none; font-size: 13px; margin-top: 2px; display: inline-block; }
.eventTextBox a.seeJob i { margin-right: 8px; }
.eventBtnBox { text-align: center; }
.btnBlue { background: #39506b; color: #fff; }
.btnBlue:hover { background: #5bbfbe; color: #fff; border-color: #5bbfbe; }
.sidebar .eventBox .span4,.homeEventContainer.eventBox .span4 { padding: 10px; background: #fff; }
.eventBoxBottom h3.HeaderText { padding-right: 80px; }
.seeAll { position: absolute; right: 0; top: 0; text-transform: uppercase; font-size: 14px; color: #c9b572; font-family: 'museo700'; }
.seeAll i { font-size: 15px; margin-right: 5px; }
/*-----Feature Slider----***/
.featureSlider { text-align: center; }
.featureSlider .row-fluid { padding: 0 30px; }
.sliderFrame { padding: 0 25px; }
.sliderFrame .owl-carousel .owl-dots.disabled, .sliderFrame .owl-carousel .owl-nav.disabled { display: block; }
.eventBoxFrame h3.HeaderText, .featureSlider h3.HeaderText, .footer h3.HeaderText { font-size: 24.5px; font-weight: 400; font-family: 'museo700'; color: #c9b572; text-transform: uppercase; letter-spacing: 1px; line-height: normal; margin-bottom: 40px; position: relative; padding-bottom: 8px; }
.eventBoxFrame h3.HeaderText::after, .featureSlider h3.HeaderText::after, .footer h3.HeaderText:after { position: absolute; content: ""; bottom: -8px; left: 0; right: 0; width: 116px; height: 4px; background: #c9b572; margin: 0 auto; }
h3.HeaderText.text-left:after,.contactSection h3.HeaderText:after,.socialSection h3.HeaderText:after,.latestNewsSection h3.HeaderText:after { left: 0; margin: 0; }
.featureSlider .item img { width: auto; margin: 0 auto; display: inline-block; vertical-align: middle; }
.featureSlider .item { padding: 10px; margin: 0 1px; border: 1px solid #ccc; height: 100px; line-height: 69px; }
.featureSlider .owl-nav { padding: 0; margin: 0; }
.featureSlider .owl-nav .owl-prev { position: absolute; left: -30px; top: 50%; -moz-transform: translateY(-50%); -webkit-transform: translateY(-50%); transform: translateY(-50%); }
.featureSlider .owl-nav .owl-next { position: absolute; right: -30px; top: 50%; -moz-transform: translateY(-50%); -webkit-transform: translateY(-50%); transform: translateY(-50%); }
.featureSlider .owl-nav .owl-prev:hover, .featureSlider .owl-nav .owl-next:hover { background: transparent; }
.featureSlider .owl-carousel .owl-item { float: none; display: inline-block; vertical-align: middle; }
.featureSlider .btnBlueSolid { margin-top: 40px; font-family: 'museo900'; }
/*--------Did You Know---------***/
.dyouk { background: #5bbfbe url(../images/dyk.png) no-repeat center / cover; text-align: center; }
.dyouk .BodyTextLarge { font-family: 'museo900'; color: #fff; margin-bottom: 15px; line-height: normal; }
.dyouk .HeaderText { font-size: 24px; font-weight: 400; font-family: 'museo100'; color: #fff; margin-bottom: 0px; line-height: normal; }
/*-----------------------------Featured Content Page CSS----------------------------***/

/*---interesGroup--**/
.interesGroupBox { background: url(../images/interest.jpg) no-repeat center / cover; text-align: center; }
.interesGroupBox .row-fluid { padding: 0px 30px; }
.interesGroupFrame { position: relative; }
.interesGroupBox .HeaderTextSmall { color: #c9b572; font-size: 20px; font-family: 'museo300'; line-height: normal; margin-bottom: 15px; }
.interesGroupBox .TitleText { color: #fff; font-size: 40px; font-weight: 300; margin: 0; line-height: 30px; font-family: 'museo100'; }
/*---  Main Div-----***/
.innerEventBox { padding-bottom: 0px; }
/*--SideBar--*/
.sidebar { width: 25%; float: left; padding-right: 10px; padding-left: 10px; margin-top: 30px; }
.eventlightGreen .eventBoxBottom { padding: 40px 0; }
.eventlightGreen .eventTextSection { color: #434343; text-align: left; }
.eventlightGreen .eventTextSection p { color: #434343; }
.eventlightGreen .eventTextSection .BodytextBold { color: #455f84; font-weight: 600; letter-spacing: 2px; text-transform: uppercase; margin-bottom: 7px; font-size: 14px; }
.eventlightGreen .eventTextSection .BodyTextLargeBold { font-weight: 600; line-height: 24px; }
.eventlightGreen .eventTextSection .mailText { color: #028b87; line-height: 24px; }
.eventlightGreen a.btn { text-decoration: none; color: #455f84; }
.eventlightGreen a.btn:hover { color: #fff; }
.eventTextSectionBtn { margin-bottom: 0px; }
.adBox { margin-top: 70px; display: inline-block; vertical-align: top; }
/*---Main Content Div----***/
.mainContent { width: 74%; float: right; background: #fff; margin-left: 10px; padding: 45px 0; }
.mainContentFull { width: 100%!important; }
.innerEventBox .row-fluid { padding: 0px 10px; }
.innerEventBox .row-fluid .eventdiv { padding: 0 10px; width: 33.33%; float: left; }
.innerEventBox .eventTextBox { min-height: 300px; }
/*--------content Div-------***/
.contentdivFrame { padding: 50px 50px 50px; }
.contentdivFrame .TitleText { color: #39506b; font-weight: 300; line-height: normal; font-family: 'museo700'; }
.contentdivFrame .HeaderText { color: #455f84; font-family: 'museo500'; }
.contentdivFrame .HeaderTextMedium { color: #455f84; font-weight: 600; letter-spacing: 2px; text-transform: uppercase; }
.contentdivFrame .HeaderTextMediumLink { font-weight: 600; text-transform: uppercase; letter-spacing: 2px; }
.secondContentBox,.HighlightContent { padding: 35px 35px 40px; }
.thirdContentBox,.Resources { padding-top: 60px; }
.thirdContentBox .HeaderText,.Resources .HeaderText { color: #5bbfbe; margin-bottom: 35px; }
.thirdContentBox .HeaderText span,.Resources .HeaderText span { position: relative; padding-bottom: 5px; display: block; line-height: normal; }
.thirdContentBox .HeaderText span:after,.Resources .HeaderText span:after { width: 116px; height: 4px; position: absolute; left: 0; top: 100%; background-color: #5bbfbe; content: ""; }
.thirdContentBox .BodyTextLarge,.Resources .BodyTextLarge { color: #455f84; font-weight: bold; font-family: 'Roboto Mono', monospace; margin-bottom: 10px; }
.contentdivFrame .BodyText { margin-bottom: 30px; line-height: 30px; color: #434343; }
h4 .HeaderTextSmall, h4.HeaderTextSmall a { color: #c9b572; }
h4 .HeaderTextSmall:hover, h4.HeaderTextSmall a:hover{color: #39506b;}
.HeaderText.headingWd-img,.Resources h3.HeaderText { padding-left: 75px; position: relative; }
.HeaderText.headingWd-img img,.Resources h3.HeaderText img { position: absolute; left: 0; top: 5px; }
/*----------------------------Inner Simple Page----------------***/
.innerSimpleEventBox .row-fluid .innerEventDiv { margin-bottom: 50px; margin-right: 0; margin-left: 0; }
.innerSimpleEventBox .contentdivFrame { padding-top: 0px; }
.innerSimpleEventBox .contentdivFrame .firstContentBox .HeaderTextSmall:last-child { margin-bottom: 0px; }
.innerSimpleEventBox .eventTextBox { min-height: auto !important; }
.innerEventDiv .eventBoxFrame { padding: 10px; }
.secondContentBox .btn.btnBlue,.HighlightContent .btn.btnBlue { border-color: #39506b; font-family: 'museo900'; height: 55px; line-height: 53px; }
.divWd-img img { float: left; margin-right: 60px; }
.thirdContentBox p.BodyText,.Resources p.BodyText  { font-size: 16px; line-height: 28px; }
/**--------Footer---------***/
.footer { background: #1c2541; padding: 40px 0px 10px; }
.footer h3.HeaderText { font-family: 'museo300'; text-transform: capitalize; }
.info li,.contactSection li { display: inline-block; padding-right: 10px; width: 26%; margin: 0 -2px; vertical-align: top; color: #fff; font-family: 'museo500'; font-size: 16px; line-height: 23px; }
.info li:nth-child(3),.contactSection li:nth-child(3) { width: 48%; }
.info li span,.contactSection li span { display: block; color: #c9b572; text-transform: uppercase; font-size: 13px; font-family: 'museo700'; }
.info li span i,.contactSection li span i { margin-right: 4px; }
.info li a,.contactSection li a { color: #fff; }
.social li i,.socialSection ul li i { font-size: 22px; color: #c9b572; width: 36px; display: inline-block; vertical-align: middle; position: absolute; left: 0; top: 2px; }
.social li a,.socialSection ul li a { color: #fff; font-size: 16px; display: inline-block; vertical-align: middle; }
.footer a:hover { text-decoration: underline; }
.social li,.socialSection ul li  { margin-bottom: 15px; padding-left: 36px; position: relative; }
.social li.stll,.socialSection ul li:nth-child(5) { padding-left: 0; }
.social li.stll a,.socialSection ul li:nth-child(5) a { color: #c9b572; font-family: 'museo700'; font-size: 16px; }
.footEvent p,.latestNewsSection > div p { margin-top: 10px; }
.footEvent p, .footEvent a,.latestNewsSection > div p,.latestNewsSection > div a { color: #fff; font-size: 15px; line-height: 25px; }
.footEvent .BodyText,.latestNewsSection > div .BodyText{ font-family: 'museo700'; font-size: 18px; margin: 0; }
.footEvent a,.latestNewsSection > div a { font-size: 13px; line-height: 15px; }
.footEvent a i,.latestNewsSection > div a i { padding-right: 5px; }
.footer-top { margin-bottom: 20px; }
.footer-bottom p { font-size: 13px; color: #6c6b6b; margin: 0; }
.footer-bottom p a { color: #c9b572; }
.rightSponsorsSection{min-height:300px;}
.rightSponsorsWrapper{margin-bottom:20px;}
#zoneToolBar form,#zoneToolBar select{margin:0!important;}
.logoutLink{padding: 5px!important;
    font-size: 10px!important;
    font-weight: 600!important;
    position: absolute!important;
    height: auto!important;
    top: 110px!important;}
.header .navbar .nav > li:last-child > a, .header .navbar .nav > li:last-child > a + a.logoutLink{background-color: #5bbfbe;}
.owl-prev.disabled,.owl-next.disabled{display:none!important;}
.footer-bottom{ font-family: 'Roboto', sans-serif;}
.avatarPic{border-radius: 50%;}
.logoutLink{background:transparent!important;}
.header .nav-collapse {
    -webkit-overflow-scrolling: touch;
}
.header .nav-collapse .nav{
    -webkit-overflow-scrolling: touch; 
}
.nav .dropdown-menu {
display: block; visibility: visible; opacity: 1; margin: 0;
}
.hoverMenu .dropdown-menu {
display: none; visibility: hidden; opacity: 0;
}

.hoverMenu:hover .dropdown-menu {
display: block; visibility: visible; opacity: 1; margin: 0;
}
.alert-info.span12 strong{line-height:28px;}
.logoSubUrl{display:none;}
div[name=MC-carousel-HomepageCarouselBottom] .owl-nav {
    display: none;
}
@media print{
	.header,.nav,footer,.footer,nav,.sidebar,.interesGroupBox,.dyouk,#zoneToolBar{display:none!important;width:0px!important;}
	img{max-width:500px!important;}
	
	video,audio,embed,object{display:none;width:0px!important;}
	.mainContent{width:100%!important;padding:0px!important;}
	.eventBox > .container{max-width:100%!important;padding:0px!important;}
	.eventBox{background:none!important;}
	.logoSubUrl{display:block;text-align: center;line-height: 20px;}
	.navbar-brand img{width: 200px!important;margin:0px!important;}
	.navbar-brand{line-height:20px!important;}
	.contentdivFrame{padding:0px!important;}
	.innerEventBox .row-fluid{padding:0px!important;}
	.headerSpace{height:10px!important;}
	.header{min-height:125px;top: 1cm;}
	.printHeader{display:block;}
	.printHeaderContact{padding-top:10px;display:block;}
	.printHeaderContact br{display:none;}
	.slider,.homeEventContainer,.featureSlider,.progresReportBox{display:none;}
}

@page { 
   margin: 1cm; 
}