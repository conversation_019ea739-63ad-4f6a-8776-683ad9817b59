<cfoutput>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
	<meta name="description" content="#event.getValue('mc_pageDefinition.pageDescription',event.getValue('mc_siteInfo.sitename'))#">
	<title>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</title>
	<link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon" />
	<link rel="stylesheet" href="/css/main.css" type="text/css">
	#application.objCMS.getBootstrapHeadHTML()#
	#application.objCMS.getResponsiveHeadHTML()#
	<link rel="stylesheet" href="/assets/common/javascript/owlCarousel/233/owl.carousel.min.css" type="text/css">
	<link rel="stylesheet" href="/assets/common/javascript/owlCarousel/233/owl.theme.default.min.css" type="text/css">
	#application.objCMS.getFontAwesomeHTML(includeVersion4Support=true)#
	<link href="/css/stylesheet.css" rel="stylesheet" type="text/css">
	<link href="/css/responsive.css" rel="stylesheet" type="text/css">
	<link href="/css/fonts.css" rel="stylesheet" type="text/css">
	#application.objCMS.getSiteCustomCSS(siteID=arguments.event.getValue('mc_siteInfo.siteID'))#
	<style>
	.mainMenuOnclick {
		display: none;
	}
	</style>
	<cfif structKeyExists(application.objCMS,"getPlatformCacheBusterKey")>
		<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
	<cfelse>
		<cfset local.assetCachingKey = "">
	</cfif>
	<script src="/javascript/custom.js#local.assetCachingKey#" type="text/javascript"></script>
	<script src="/assets/common/javascript/owlCarousel/233/owl.carousel.min.js"></script>
</cfoutput>



